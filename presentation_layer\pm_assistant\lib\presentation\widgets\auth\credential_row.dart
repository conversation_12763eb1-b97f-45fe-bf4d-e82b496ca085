import 'package:flutter/material.dart';

/// Credential row widget following Single Responsibility Principle
/// Handles only credential display UI logic for demo purposes
class CredentialRow extends StatelessWidget {
  final String role;
  final String email;
  final String password;

  const CredentialRow({
    super.key,
    required this.role,
    required this.email,
    required this.password,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Color(0xFFE5E7EB).withOpacity(0.5)),
      ),
      child: Row(
        children: [
          Icon(
            role.contains('Admin') ? Icons.admin_panel_settings : Icons.person,
            color: Color(0xFF4FD1C7),
            size: 16,
          ),
          SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  role,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF374151),
                  ),
                ),
                Text(
                  '$email / $password',
                  style: TextStyle(
                    fontSize: 10,
                    color: Color(0xFF94A3B8),
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () {
              // Copy credentials functionality can be added here
            },
            child: Icon(
              Icons.copy,
              size: 14,
              color: Color(0xFF94A3B8),
            ),
          ),
        ],
      ),
    );
  }
}
