"""
Chat Router
==========
Handles all chat-related endpoints
"""

from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.responses import StreamingResponse
from typing import Dict, Any, List
import json
import asyncio
import uuid
from datetime import datetime

from models.chat_models import MessageRequest, MessageResponse, SessionCreate, SessionResponse, ConversationSummary
from services.auth_service import AuthService
from services.message_service import MessageService
from services.session_service import SessionService

router = APIRouter(prefix="/chat", tags=["chat"])
auth_service = AuthService()
message_service = MessageService()
session_service = SessionService()


@router.post("/send", response_model=MessageResponse)
async def send_message(
    message_data: MessageRequest,
    current_user: Dict[str, Any] = Depends(auth_service.get_current_user)
) -> MessageResponse:
    """Send a chat message and get AI response"""
    try:
        result = await message_service.process_message(
            message=message_data.message,
            user_id=current_user["id"],
            session_id=message_data.session_id,
            user_role=current_user.get("role", "user")
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.post("/stream")
async def stream_message(
    message_data: MessageRequest,
    current_user: Dict[str, Any] = Depends(auth_service.get_current_user)
):
    """Stream chat message response token by token"""

    async def generate_stream():
        try:
            # Send initial response with metadata
            initial_data = {
                "type": "start",
                "session_id": message_data.session_id or "new_session",
                "timestamp": str(datetime.now()),
                "message_id": str(uuid.uuid4())
            }
            yield f"data: {json.dumps(initial_data)}\n\n"

            # Stream the AI response with disconnect handling
            sent_any_token = False
            async for chunk_data in message_service.stream_ai_response(
                message=message_data.message,
                user_id=current_user["id"],
                role=current_user.get("role", "user")
            ):
                try:
                    # Extract chunk content from the response
                    chunk_content = ""
                    full_response = None
                    if isinstance(chunk_data, dict):
                        chunk_content = chunk_data.get("chunk", "") or ""
                        full_response = chunk_data.get("full_response")
                    else:
                        chunk_content = str(chunk_data)

                    # If we have token content, send it
                    if chunk_content:
                        response_data = {
                            "type": "token",
                            "chunk": chunk_content,  # Flutter expects 'chunk'
                            "content": chunk_content
                        }
                        sent_any_token = True
                        yield f"data: {json.dumps(response_data)}\n\n"

                    # If no tokens were emitted but we have a full_response, send it as a single token
                    if not sent_any_token and full_response:
                        response_data = {
                            "type": "token",
                            "chunk": full_response,
                            "content": full_response
                        }
                        sent_any_token = True
                        yield f"data: {json.dumps(response_data)}\n\n"

                    # Check if streaming is complete
                    if isinstance(chunk_data, dict) and chunk_data.get("completed", False):
                        break

                except (ConnectionResetError, BrokenPipeError, OSError):
                    # Client disconnected, stop streaming gracefully
                    break

            # Send completion signal
            end_data = {
                "type": "end",
                "timestamp": str(datetime.now())
            }
            yield f"data: {json.dumps(end_data)}\n\n"

        except (ConnectionResetError, BrokenPipeError, OSError):
            # Client disconnected, don't log as error
            pass
        except Exception as e:
            try:
                error_data = {
                    "type": "error",
                    "error": str(e)
                }
                yield f"data: {json.dumps(error_data)}\n\n"
            except (ConnectionResetError, BrokenPipeError, OSError):
                # Client disconnected while sending error, ignore
                pass

    return StreamingResponse(
        generate_stream(),
        media_type="text/event-stream",  # Correct SSE media type
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no"  # Disable buffering on proxies like Nginx
        }
    )


@router.post("/conversation", response_model=SessionResponse)
async def create_conversation(
    session_data: SessionCreate,
    current_user: Dict[str, Any] = Depends(auth_service.get_current_user)
) -> SessionResponse:
    """Create a new conversation/session"""
    try:
        result = await session_service.create_session(
            user_id=current_user["id"],
            title=session_data.title
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/conversations", response_model=List[ConversationSummary])
async def get_conversations(
    current_user: Dict[str, Any] = Depends(auth_service.get_current_user)
) -> List[ConversationSummary]:
    """Get all conversations for the current user"""
    try:
        conversations = await session_service.get_user_conversations(current_user["id"])
        return conversations
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/conversation/{conversation_id}")
async def get_conversation_history(
    conversation_id: str,
    current_user: Dict[str, Any] = Depends(auth_service.get_current_user)
) -> Dict[str, Any]:
    """Get conversation history with all messages"""
    try:
        conversation = await session_service.get_conversation_detail(
            conversation_id=conversation_id,
            user_id=current_user["id"]
        )
        return conversation
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Conversation not found")


@router.delete("/conversation/{conversation_id}")
async def delete_conversation(
    conversation_id: str,
    current_user: Dict[str, Any] = Depends(auth_service.get_current_user)
) -> Dict[str, Any]:
    """Delete a conversation"""
    try:
        result = await session_service.delete_conversation(
            conversation_id=conversation_id,
            user_id=current_user["id"]
        )
        return {"success": True, "message": "Conversation deleted successfully"}
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to delete conversation")


@router.put("/conversation/{conversation_id}")
async def update_conversation_title(
    conversation_id: str,
    title_data: Dict[str, str],
    current_user: Dict[str, Any] = Depends(auth_service.get_current_user)
) -> Dict[str, str]:
    """Update conversation title"""
    try:
        await session_service.update_conversation_title(
            conversation_id=conversation_id,
            user_id=current_user["id"],
            title=title_data.get("title", "")
        )
        return {"message": "Conversation title updated successfully"}
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Conversation not found")
