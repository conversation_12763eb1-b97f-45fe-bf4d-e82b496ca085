#!/bin/bash

# EMERGENCY FIX: Model Not Generating Content
echo "🚨 EMERGENCY FIX: Model Not Generating Content"
echo "=============================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Problem: Model generating 0-character chunks"
echo ""
echo "🔧 Emergency fixes applied:"
echo "   ✅ Increased temperature to 0.7 (was 0.3)"
echo "   ✅ Increased top_p to 0.9 (was 0.8)"
echo "   ✅ Reduced repeat_penalty to 1.1 (was 1.3)"
echo "   ✅ Removed all stop words"
echo "   ✅ Increased max_tokens to 500"
echo "   ✅ Removed aggressive stopping conditions"
echo ""

print_status "Rebuilding container with emergency fixes..."

# Stop current container
docker-compose -f docker-compose.vps.yml stop pm-assistant

# Rebuild with no cache
print_status "Rebuilding with emergency fixes..."
docker-compose -f docker-compose.vps.yml build --no-cache pm-assistant

# Start container
print_status "Starting fixed container..."
docker-compose -f docker-compose.vps.yml up -d pm-assistant

# Wait for startup
print_status "Waiting for startup..."
sleep 25

# Check health
print_status "Checking health..."
for i in {1..10}; do
    if curl -f http://localhost:8000/health &>/dev/null; then
        print_success "✅ Service is healthy!"
        break
    else
        print_status "Waiting for service... (attempt $i/10)"
        sleep 5
    fi
    
    if [ $i -eq 10 ]; then
        print_error "❌ Health check failed"
        docker-compose -f docker-compose.vps.yml logs --tail=20 pm-assistant
        exit 1
    fi
done

# Test response generation immediately
print_status "Testing INSTANT response generation..."
echo ""
echo "🧪 Test 1: Simple question"
start_time=$(date +%s%N)
response=$(timeout 30s curl -s -X POST http://localhost:8000/chat \
    -H "Content-Type: application/json" \
    -d '{"message": "What is project management?"}')
end_time=$(date +%s%N)
duration=$(( (end_time - start_time) / 1000000 ))

echo "Response time: ${duration}ms"
if echo "$response" | grep -q "Thinking\|Answer\|response"; then
    print_success "✅ Model is generating content!"
    echo "Response preview: $(echo "$response" | head -c 300)..."
else
    print_error "❌ Model still not generating content"
    echo "Full response: $response"
fi

echo ""
echo "🧪 Test 2: Streaming test"
stream_response=$(timeout 20s curl -s -X POST http://localhost:8000/chat/stream \
    -H "Content-Type: application/json" \
    -d '{"message": "Explain agile methodology"}' | head -15)

if echo "$stream_response" | grep -q "data:"; then
    print_success "✅ Streaming is working!"
    
    # Count non-empty chunks
    non_empty_chunks=$(echo "$stream_response" | grep -o '"content":"[^"]\+' | wc -l)
    print_status "Non-empty chunks received: $non_empty_chunks"
    
    if [ "$non_empty_chunks" -gt 0 ]; then
        print_success "✅ Model is generating streaming content!"
        echo "Sample chunks:"
        echo "$stream_response" | grep '"content":"[^"]\+' | head -3
    else
        print_error "❌ Still receiving empty chunks"
        echo "Stream response:"
        echo "$stream_response"
    fi
else
    print_error "❌ Streaming not working"
    echo "Stream response: $stream_response"
fi

echo ""
print_status "Checking debug logs..."
docker logs pm-assistant-vps | grep -E "(🔍|🛑|ERROR|WARNING)" | tail -10

echo ""
if [ "$non_empty_chunks" -gt 0 ] || echo "$response" | grep -q "Thinking\|Answer"; then
    print_success "🎉 EMERGENCY FIX SUCCESSFUL!"
    echo ""
    echo "✅ Model is now generating content"
    echo "✅ Responses should be instant"
    echo "✅ No more empty chunks"
else
    print_error "🚨 EMERGENCY FIX FAILED"
    echo ""
    echo "❌ Model still not generating content"
    echo "❌ May need manual intervention"
    echo ""
    echo "🔧 Next steps:"
    echo "1. Check model file exists: ls -la business_logic_layer/models/"
    echo "2. Check model loading: docker logs pm-assistant-vps | grep -i model"
    echo "3. Try different model file"
    echo "4. Check system resources: free -h && nproc"
fi

echo ""
echo "📋 Access Information:"
echo "   Backend API: http://54.38.33.180:8000"
echo "   Health: curl http://54.38.33.180:8000/health"
echo "   Test: curl -X POST http://54.38.33.180:8000/chat -H 'Content-Type: application/json' -d '{\"message\": \"test\"}'"
