<svg viewBox="0 0 1400 1000" xmlns="http://www.w3.org/2000/svg">
  <!-- Definitions for gradients and shadows -->
  <defs>
    <!-- Background gradient -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:1" />
    </linearGradient>
    
    <!-- Layer gradients -->
    <linearGradient id="presentationGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f4f8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d1ecf1;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="businessGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#f0f8e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d4edda;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="aiGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#fdf2e9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8d7da;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="dataGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#f4f0ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e3ff;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="infraGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ffeaa7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fdcb6e;stop-opacity:1" />
    </linearGradient>
    
    <!-- Component gradients -->
    <linearGradient id="componentGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
    
    <!-- Shadow filter -->
    <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.15"/>
    </filter>
    
    <!-- Layer shadow -->
    <filter id="layerShadow" x="-10%" y="-10%" width="120%" height="120%">
      <feDropShadow dx="0" dy="6" stdDeviation="8" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
    
    <!-- Arrow marker -->
    <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#6c757d" opacity="0.8"/>
    </marker>
  </defs>
  
  <!-- Background -->
  <rect width="1400" height="1000" fill="url(#bgGradient)"/>
  
  <!-- Title with enhanced styling -->
  <text x="700" y="35" text-anchor="middle" font-family="Segoe UI, Arial, sans-serif" font-size="28" font-weight="600" fill="#2c3e50">
    PM Assistant - Layered Architecture (Team 14)
  </text>
  <text x="700" y="55" text-anchor="middle" font-family="Segoe UI, Arial, sans-serif" font-size="14" font-weight="400" fill="#6c757d">
    Layered Architecture: Presentation + Business Logic + AI/ML + Data + Infrastructure
  </text>
  
  <!-- Presentation Layer -->
  <g id="presentation-layer">
    <rect x="50" y="75" width="1300" height="140" fill="url(#presentationGradient)" stroke="#3498db" stroke-width="3" rx="15" filter="url(#layerShadow)"/>
    <text x="75" y="100" font-family="Segoe UI, Arial, sans-serif" font-size="18" font-weight="600" fill="#2c3e50">Presentation Layer</text>
    
    <!-- Web Interface -->
    <rect x="80" y="115" width="200" height="85" fill="url(#componentGradient)" stroke="#3498db" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="90" y="135" font-family="Segoe UI, Arial, sans-serif" font-size="13" font-weight="600" fill="#2c3e50">🌐 Web Interface</text>
    <text x="90" y="150" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Flutter</text>
    <text x="90" y="165" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Voice Input (Web Speech)</text>
    <text x="90" y="180" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Chat Interface</text>
    
    <!-- Desktop App -->
    <rect x="300" y="115" width="200" height="85" fill="url(#componentGradient)" stroke="#3498db" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="310" y="135" font-family="Segoe UI, Arial, sans-serif" font-size="13" font-weight="600" fill="#2c3e50">💻 Desktop App</text>
    <text x="310" y="150" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Flutter</text>
    <text x="310" y="165" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Native Voice Input</text>
    <text x="310" y="180" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• System Tray</text>
    
    <!-- Mobile App -->
    <rect x="520" y="115" width="200" height="85" fill="url(#componentGradient)" stroke="#3498db" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="530" y="135" font-family="Segoe UI, Arial, sans-serif" font-size="13" font-weight="600" fill="#2c3e50">📱 Mobile App</text>
    <text x="530" y="150" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Flutter</text>
    <text x="530" y="165" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Voice Recognition</text>
    <text x="530" y="180" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Offline Mode</text>
    
    <!-- Admin Dashboard -->
    <rect x="740" y="115" width="200" height="85" fill="url(#componentGradient)" stroke="#e74c3c" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="750" y="135" font-family="Segoe UI, Arial, sans-serif" font-size="13" font-weight="600" fill="#2c3e50">⚙️ Admin Dashboard</text>
    <text x="750" y="150" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Performance Monitoring</text>
    <text x="750" y="165" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Model Management</text>
    <text x="750" y="180" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Prompt Configuration</text>
    
    <!-- API Gateway -->
    <rect x="960" y="115" width="200" height="85" fill="url(#componentGradient)" stroke="#9b59b6" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="970" y="135" font-family="Segoe UI, Arial, sans-serif" font-size="13" font-weight="600" fill="#2c3e50">🚪 API Gateway</text>
    <text x="970" y="150" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• FastAPI</text>
    <text x="970" y="165" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Rate Limiting</text>
    <text x="970" y="180" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Request Routing</text>
  </g>
  
  <!-- Business Logic Layer -->
  <g id="business-layer">
    <rect x="50" y="235" width="1300" height="190" fill="url(#businessGradient)" stroke="#27ae60" stroke-width="3" rx="15" filter="url(#layerShadow)"/>
    <text x="75" y="260" font-family="Segoe UI, Arial, sans-serif" font-size="18" font-weight="600" fill="#2c3e50">Business Logic Layer</text>
    
    <!-- Chat Controller -->
    <rect x="80" y="275" width="180" height="130" fill="url(#componentGradient)" stroke="#27ae60" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="90" y="295" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="600" fill="#2c3e50">💬 Chat Controller</text>
    <text x="90" y="310" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Session Management</text>
    <text x="90" y="325" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Context Tracking</text>
    <text x="90" y="340" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Conversation History</text>
    <text x="90" y="355" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• User Preferences</text>
    <text x="90" y="370" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Response Rating</text>
    <text x="90" y="385" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Voice Processing</text>
    
    <!-- NLP Processor -->
    <rect x="280" y="275" width="180" height="130" fill="url(#componentGradient)" stroke="#27ae60" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="290" y="295" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="600" fill="#2c3e50">🧠 NLP Processor</text>
    <text x="290" y="310" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Intent Classification</text>
    <text x="290" y="325" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Entity Extraction</text>
    <text x="290" y="340" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Sentiment Analysis</text>
    <text x="290" y="355" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Language Detection</text>
    <text x="290" y="370" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Input Validation</text>
    <text x="290" y="385" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Safety Filtering</text>
    
    <!-- Knowledge Manager -->
    <rect x="480" y="275" width="180" height="130" fill="url(#componentGradient)" stroke="#27ae60" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="490" y="295" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="600" fill="#2c3e50">📚 Knowledge Manager</text>
    <text x="490" y="310" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Search Queries</text>
    <text x="490" y="325" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Context Injection</text>
    <text x="490" y="340" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Response Generation</text>
    <text x="490" y="355" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Quality Scoring</text>
    <text x="490" y="370" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Content Validation</text>
    <text x="490" y="385" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Answer Ranking</text>
    
    <!-- Security Manager -->
    <rect x="680" y="275" width="180" height="130" fill="url(#componentGradient)" stroke="#e74c3c" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="690" y="295" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="600" fill="#2c3e50">🔒 Security Manager</text>
    <text x="690" y="310" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Input Sanitization</text>
    <text x="690" y="325" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Prompt Injection Defense</text>
    <text x="690" y="340" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Content Filtering</text>
    <text x="690" y="355" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Data Encryption</text>
    <text x="690" y="370" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Access Control</text>
    <text x="690" y="385" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Audit Logging</text>
    
    <!-- Analytics Engine -->
    <rect x="880" y="275" width="180" height="130" fill="url(#componentGradient)" stroke="#f39c12" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="890" y="295" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="600" fill="#2c3e50">📊 Analytics Engine</text>
    <text x="890" y="310" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Performance Metrics</text>
    <text x="890" y="325" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Usage Analytics</text>
    <text x="890" y="340" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Quality Scoring</text>
    <text x="890" y="355" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Error Tracking</text>
    <text x="890" y="370" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• User Behavior</text>
    <text x="890" y="385" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Reporting</text>
    
    <!-- Session Manager -->
    <rect x="1080" y="275" width="180" height="130" fill="url(#componentGradient)" stroke="#9b59b6" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="1090" y="295" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="600" fill="#2c3e50">🔄 Session Manager</text>
    <text x="1090" y="310" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Session Creation</text>
    <text x="1090" y="325" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Context Persistence</text>
    <text x="1090" y="340" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• State Management</text>
    <text x="1090" y="355" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Memory Optimization</text>
    <text x="1090" y="370" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Cleanup Tasks</text>
    <text x="1090" y="385" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Timeout Handling</text>
  </g>
  
  <!-- AI/ML Layer -->
  <g id="ai-layer">
    <rect x="50" y="445" width="1300" height="140" fill="url(#aiGradient)" stroke="#e67e22" stroke-width="3" rx="15" filter="url(#layerShadow)"/>
    <text x="75" y="470" font-family="Segoe UI, Arial, sans-serif" font-size="18" font-weight="600" fill="#2c3e50">AI/ML Processing Layer</text>
    
    <!-- Knowledge Base -->
    <rect x="80" y="485" width="280" height="85" fill="url(#componentGradient)" stroke="#e67e22" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="90" y="505" font-family="Segoe UI, Arial, sans-serif" font-size="13" font-weight="600" fill="#2c3e50">📖 Knowledge Base</text>
    <text x="90" y="520" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Q&amp;A Dataset Processing</text>
    <text x="90" y="535" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• PM Domain Knowledge</text>
    <text x="90" y="550" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Context Retrieval</text>
    <text x="90" y="565" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Answer Ranking</text>
    
    <!-- NLP Engine -->
    <rect x="380" y="485" width="280" height="85" fill="url(#componentGradient)" stroke="#e67e22" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="390" y="505" font-family="Segoe UI, Arial, sans-serif" font-size="13" font-weight="600" fill="#2c3e50">🤖 NLP Engine</text>
    <text x="390" y="520" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Intent Classification</text>
    <text x="390" y="535" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Entity Recognition</text>
    <text x="390" y="550" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Context Understanding</text>
    <text x="390" y="565" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Response Generation</text>
    
    <!-- Data Processing -->
    <rect x="680" y="485" width="280" height="85" fill="url(#componentGradient)" stroke="#e67e22" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="690" y="505" font-family="Segoe UI, Arial, sans-serif" font-size="13" font-weight="600" fill="#2c3e50">⚡ Data Processing</text>
    <text x="690" y="520" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Text Preprocessing</text>
    <text x="690" y="535" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Feature Extraction</text>
    <text x="690" y="550" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Data Validation</text>
    <text x="690" y="565" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Quality Assurance</text>
    
    <!-- ML Models -->
    <rect x="980" y="485" width="280" height="85" fill="url(#componentGradient)" stroke="#e67e22" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="990" y="505" font-family="Segoe UI, Arial, sans-serif" font-size="13" font-weight="600" fill="#2c3e50">🧮 ML Models</text>
    <text x="990" y="520" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Text Classification</text>
    <text x="990" y="535" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Similarity Matching</text>
    <text x="990" y="550" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Confidence Scoring</text>
    <text x="990" y="565" font-family="Segoe UI, Arial, sans-serif" font-size="10" fill="#495057">• Performance Optimization</text>
  </g>
  
  <!-- Data Layer -->
  <g id="data-layer">
    <rect x="50" y="610" width="1300" height="150" fill="url(#dataGradient)" stroke="#9b59b6" stroke-width="3" rx="15" filter="url(#layerShadow)"/>
    <text x="75" y="635" font-family="Segoe UI, Arial, sans-serif" font-size="18" font-weight="600" fill="#2c3e50">💾 Data Storage Layer</text>
    
    <!-- SQLite Database -->
    <rect x="80" y="650" width="200" height="90" fill="url(#componentGradient)" stroke="#9b59b6" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="90" y="670" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="600" fill="#2c3e50">🗄️ SQLite Database</text>
    <text x="90" y="685" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• User Sessions</text>
    <text x="90" y="700" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Conversation History</text>
    <text x="90" y="715" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• User Preferences</text>
    <text x="90" y="730" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Analytics Data</text>
    
    <!-- Vector Database -->
    <rect x="300" y="650" width="200" height="90" fill="url(#componentGradient)" stroke="#9b59b6" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="310" y="670" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="600" fill="#2c3e50">🔍 Vector Database</text>
    <text x="310" y="685" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• ChromaDB</text>
    <text x="310" y="700" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Document Embeddings</text>
    <text x="310" y="715" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Semantic Search</text>
    <text x="310" y="730" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Context Retrieval</text>
    
    <!-- Knowledge Base -->
    <rect x="520" y="650" width="200" height="90" fill="url(#componentGradient)" stroke="#9b59b6" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="530" y="670" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="600" fill="#2c3e50">📚 Knowledge Base</text>
    <text x="530" y="685" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• FAQ Documents</text>
    <text x="530" y="700" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Training Data</text>
    <text x="530" y="715" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Prompt Templates</text>
    <text x="530" y="730" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Domain Knowledge</text>
    
    <!-- File Storage -->
    <rect x="740" y="650" width="200" height="90" fill="url(#componentGradient)" stroke="#9b59b6" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="750" y="670" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="600" fill="#2c3e50">📁 File Storage</text>
    <text x="750" y="685" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Model Files</text>
    <text x="750" y="700" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Audio Files</text>
    <text x="750" y="715" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Configuration</text>
    <text x="750" y="730" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Logs &amp; Backups</text>
    
    <!-- Cache Layer -->
    <rect x="960" y="650" width="200" height="90" fill="url(#componentGradient)" stroke="#9b59b6" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="970" y="670" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="600" fill="#2c3e50">⚡ Cache Layer</text>
    <text x="970" y="685" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Redis (Optional)</text>
    <text x="970" y="700" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Memory Cache</text>
    <text x="970" y="715" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Model Cache</text>
    <text x="970" y="730" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Response Cache</text>
  </g>
  
  <!-- Infrastructure Layer -->
  <g id="infrastructure-layer">
    <rect x="50" y="780" width="1300" height="150" fill="url(#infraGradient)" stroke="#f39c12" stroke-width="3" rx="15" filter="url(#layerShadow)"/>
    <text x="75" y="805" font-family="Segoe UI, Arial, sans-serif" font-size="18" font-weight="600" fill="#2c3e50">🏗️ Infrastructure &amp; DevOps Layer</text>
    
    <!-- Docker Containers -->
    <rect x="80" y="820" width="200" height="90" fill="url(#componentGradient)" stroke="#3498db" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="90" y="840" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="600" fill="#2c3e50">🐳 Docker Containers</text>
    <text x="90" y="855" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• API Container</text>
    <text x="90" y="870" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• ML Model Container</text>
    <text x="90" y="885" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Database Container</text>
    <text x="90" y="900" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Web UI Container</text>
    
    <!-- Monitoring -->
    <rect x="300" y="820" width="200" height="90" fill="url(#componentGradient)" stroke="#27ae60" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="310" y="840" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="600" fill="#2c3e50">📊 Monitoring</text>
    <text x="310" y="855" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Prometheus</text>
    <text x="310" y="870" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Grafana</text>
    <text x="310" y="885" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Health Checks</text>
    <text x="310" y="900" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Alerting</text>
    
    <!-- Load Balancer -->
    <rect x="520" y="820" width="200" height="90" fill="url(#componentGradient)" stroke="#8e44ad" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="530" y="840" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="600" fill="#2c3e50">⚖️ Load Balancer</text>
    <text x="530" y="855" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• nginx/HAProxy</text>
    <text x="530" y="870" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• SSL Termination</text>
    <text x="530" y="885" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Request Routing</text>
    <text x="530" y="900" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Rate Limiting</text>
    
    <!-- Security -->
    <rect x="740" y="820" width="200" height="90" fill="url(#componentGradient)" stroke="#e74c3c" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="750" y="840" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="600" fill="#2c3e50">🛡️ Security</text>
    <text x="750" y="855" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Network Isolation</text>
    <text x="750" y="870" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Firewall Rules</text>
    <text x="750" y="885" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Data Encryption</text>
    <text x="750" y="900" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Audit Logging</text>
    
    <!-- CI/CD -->
    <rect x="960" y="820" width="200" height="90" fill="url(#componentGradient)" stroke="#f39c12" stroke-width="2" rx="8" filter="url(#dropShadow)"/>
    <text x="970" y="840" font-family="Segoe UI, Arial, sans-serif" font-size="12" font-weight="600" fill="#2c3e50">🚀 CI/CD Pipeline</text>
    <text x="970" y="855" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Git Version Control</text>
    <text x="970" y="870" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Automated Testing</text>
    <text x="970" y="885" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Docker Build</text>
    <text x="970" y="900" font-family="Segoe UI, Arial, sans-serif" font-size="9" fill="#495057">• Deployment</text>
  </g>
  
  <!-- Arrows and Connections -->
  <g id="connections" stroke="#7f8c8d" stroke-width="2" fill="none" opacity="0.7" marker-end="url(#arrowhead)">
    <!-- Presentation to Business -->
    <line x1="180" y1="215" x2="180" y2="230"/>
    <line x1="400" y1="215" x2="400" y2="230"/>
    <line x1="620" y1="215" x2="620" y2="230"/>
    <line x1="840" y1="215" x2="840" y2="230"/>
    
    <!-- Business to AI -->
    <line x1="220" y1="425" x2="220" y2="445"/>
    <line x1="520" y1="425" x2="520" y2="445"/>
    <line x1="820" y1="425" x2="820" y2="445"/>
    <line x1="1120" y1="425" x2="1120" y2="445"/>
    
    <!-- AI to Data -->
    <line x1="220" y1="590" x2="220" y2="610"/>
    <line x1="520" y1="590" x2="520" y2="610"/>
    <line x1="820" y1="590" x2="820" y2="610"/>
    <line x1="1120" y1="590" x2="1120" y2="610"/>
    
    <!-- Data to Infrastructure -->
    <line x1="180" y1="780" x2="180" y2="800"/>
    <line x1="400" y1="780" x2="400" y2="800"/>
    <line x1="620" y1="780" x2="620" y2="800"/>
    <line x1="840" y1="780" x2="840" y2="800"/>
    <line x1="1060" y1="780" x2="1060" y2="800"/>
  </g>
</svg>