import 'package:flutter/material.dart';
import '../../viewmodels/admin_dashboard_viewmodel.dart';
import '../../../core/constants/app_colors.dart';

/// Analytics dialog widget following Single Responsibility Principle
/// Handles only analytics display UI logic
class AnalyticsDialog extends StatefulWidget {
  final AdminDashboardViewModel adminViewModel;
  
  const AnalyticsDialog({super.key, required this.adminViewModel});

  static Future<void> show(BuildContext context, AdminDashboardViewModel adminViewModel) {
    return showDialog(
      context: context,
      builder: (context) => AnalyticsDialog(adminViewModel: adminViewModel),
    );
  }

  @override
  _AnalyticsDialogState createState() => _AnalyticsDialogState();
}

class _AnalyticsDialogState extends State<AnalyticsDialog> {
  Map<String, dynamic> analytics = {};
  bool isLoading = true;
  String? error;

  @override
  void initState() {
    super.initState();
    _loadAnalytics();
  }

  Future<void> _loadAnalytics() async {
    try {
      final result = await widget.adminViewModel.getAnalytics();
      
      if (result['success'] == true) {
        setState(() {
          analytics = result['data'] ?? {};
          isLoading = false;
        });
      } else {
        setState(() {
          error = result['message'] ?? 'Failed to load analytics';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        error = 'Error loading analytics: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.analytics, color: AppColors.primaryTeal),
          const SizedBox(width: 12),
          const Text('Analytics Dashboard'),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        height: 250, // Reduced height to prevent overflow
        child: isLoading
            ? const Center(child: CircularProgressIndicator())
            : error != null
                ? Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error_outline, color: AppColors.error, size: 48),
                      const SizedBox(height: 16),
                      Text(error!, textAlign: TextAlign.center),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            isLoading = true;
                            error = null;
                          });
                          _loadAnalytics();
                        },
                        child: const Text('Retry'),
                      ),
                    ],
                  )
                : SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'System analytics and performance metrics',
                          style: TextStyle(fontSize: 16, color: AppColors.textSecondary),
                        ),
                        const SizedBox(height: 20),
                        _buildAnalyticsCard('Total Sessions', analytics['total_sessions']?.toString() ?? '0'),
                        _buildAnalyticsCard('Total Messages', analytics['total_messages']?.toString() ?? '0'),
                        _buildAnalyticsCard('Active Users', analytics['active_users']?.toString() ?? '0'),
                        _buildAnalyticsCard('Avg Response Time', '${analytics['avg_response_time'] ?? 1.9}s'),
                        _buildAnalyticsCard('User Satisfaction', '${analytics['user_satisfaction'] ?? 85}%'),
                        const SizedBox(height: 16),
                        Text(
                          'Last Updated: ${DateTime.now().toString().substring(0, 16)}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Close'),
        ),
        if (!isLoading && error == null)
          TextButton(
            onPressed: _loadAnalytics,
            child: const Text('Refresh'),
          ),
      ],
    );
  }

  Widget _buildAnalyticsCard(String label, String value) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primaryTeal.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.primaryTeal.withOpacity(0.2)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: AppColors.primaryTeal,
            ),
          ),
        ],
      ),
    );
  }
}
