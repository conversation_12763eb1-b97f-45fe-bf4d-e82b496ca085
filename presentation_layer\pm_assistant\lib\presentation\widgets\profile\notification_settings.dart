import 'package:flutter/material.dart';
import 'notification_tile.dart';

/// Notification settings widget following Single Responsibility Principle
/// Handles only notification settings section UI logic
class NotificationSettings extends StatelessWidget {
  final bool emailNotifications;
  final bool pushNotifications;
  final Function(bool?) onEmailChanged;
  final Function(bool?) onPushChanged;

  const NotificationSettings({
    super.key,
    required this.emailNotifications,
    required this.pushNotifications,
    required this.onEmailChanged,
    required this.onPushChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        NotificationTile(
          title: 'Email Notifications',
          subtitle: 'Receive project updates via email',
          value: emailNotifications,
          onChanged: onEmailChanged,
          icon: Icons.email,
        ),
        SizedBox(height: 16),
        NotificationTile(
          title: 'Push Notifications',
          subtitle: 'Get instant alerts on your device',
          value: pushNotifications,
          onChanged: onPushChanged,
          icon: Icons.notifications,
        ),
      ],
    );
  }
}
