"""
Comprehensive test suite for the PM Chatbot API
"""
import pytest
import requests
import json
import time
import asyncio
from typing import Dict, List, Optional

class PMChatbotAPITester:
    """Test suite for PM Chatbot API"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session_id = None
        self.test_results = []
    
    def log_test(self, test_name: str, success: bool, message: str = "", data: Dict = None):
        """Log test results"""
        result = {
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": time.time(),
            "data": data
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        
        if data and not success:
            print(f"   Data: {json.dumps(data, indent=2)}")
    
    def test_api_health(self) -> bool:
        """Test API health endpoint"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                success = (
                    data.get('status') == 'healthy' and
                    data.get('rag_initialized') == True and
                    data.get('model_loaded') == True
                )
                self.log_test(
                    "API Health Check", 
                    success, 
                    f"Status: {data.get('status')}, Chunks: {data.get('chunks_count', 0)}",
                    data
                )
                return success
            else:
                self.log_test("API Health Check", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("API Health Check", False, f"Connection error: {e}")
            return False
    
    def test_root_endpoint(self) -> bool:
        """Test root endpoint"""
        try:
            response = requests.get(f"{self.base_url}/", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                success = "Project Management Chatbot API" in data.get('message', '')
                self.log_test("Root Endpoint", success, f"Message: {data.get('message')}")
                return success
            else:
                self.log_test("Root Endpoint", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Root Endpoint", False, f"Error: {e}")
            return False
    
    def test_session_creation(self) -> bool:
        """Test session creation"""
        try:
            payload = {
                "user_id": "test_user",
                "preferred_language": "en"
            }
            
            response = requests.post(
                f"{self.base_url}/sessions",
                params=payload,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                self.session_id = data.get('session_id')
                success = bool(self.session_id and len(self.session_id) > 0)
                self.log_test(
                    "Session Creation", 
                    success, 
                    f"Session ID: {self.session_id}",
                    data
                )
                return success
            else:
                self.log_test("Session Creation", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Session Creation", False, f"Error: {e}")
            return False
    
    def test_chat_basic(self, retries: int = 2, timeout: int = 60) -> bool:
        """Test basic chat functionality with retries"""
        if not self.session_id:
            self.log_test("Basic Chat", False, "No session ID available")
            return False
        
        for attempt in range(retries + 1):
            try:
                payload = {
                    "message": "What is a RACI matrix?",
                    "session_id": self.session_id,
                    "user_id": "test_user"
                }
                
                response = requests.post(
                    f"{self.base_url}/chat",
                    json=payload,
                    timeout=timeout
                )
                
                if response.status_code == 200:
                    data = response.json()
                    response_text = data.get('response', '')
                    success = (
                        'response' in data and
                        len(response_text) > 50 and
                        'RACI' in response_text and
                        data.get('language') == 'en' and
                        data.get('session_id') == self.session_id
                    )
                    self.log_test(
                        "Basic Chat", 
                        success, 
                        f"Response length: {len(response_text)}, Language: {data.get('language')}, Contains 'RACI': {'RACI' in response_text}"
                    )
                    return success
                else:
                    self.log_test("Basic Chat", False, f"HTTP {response.status_code}")
                    return False
                    
            except requests.exceptions.RequestException as e:
                self.log_test("Basic Chat", False, f"Attempt {attempt + 1} failed: {e}")
                if attempt < retries:
                    time.sleep(2)  # Wait before retry
                continue
        
        return False
    
    def test_multilingual_support(self, retries: int = 2, timeout: int = 60) -> bool:
        """Test multilingual support with retries"""
        if not self.session_id:
            return False
        
        test_queries = [
            ("Qu'est-ce qu'une matrice RACI ?", "fr", "RACI"),
            ("ما هي مصفوفة RACI؟", "ar", "RACI"),
            ("Explain Agile methodology", "en", "Agile")
        ]
        
        all_passed = True
        
        for query, expected_lang, expected_content in test_queries:
            for attempt in range(retries + 1):
                try:
                    payload = {
                        "message": query,
                        "session_id": self.session_id
                    }
                    
                    response = requests.post(
                        f"{self.base_url}/chat",
                        json=payload,
                        timeout=timeout
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        response_text = data.get('response', '')
                        success = (
                            data.get('language') == expected_lang and
                            expected_content.lower() in response_text.lower()
                        )
                        self.log_test(
                            f"Multilingual ({expected_lang})", 
                            success, 
                            f"Detected: {data.get('language')}, Contains '{expected_content}': {expected_content.lower() in response_text.lower()}"
                        )
                        all_passed = all_passed and success
                        break
                    else:
                        self.log_test(f"Multilingual ({expected_lang})", False, f"HTTP {response.status_code}")
                        all_passed = False
                        break
                        
                except requests.exceptions.RequestException as e:
                    self.log_test(f"Multilingual ({expected_lang})", False, f"Attempt {attempt + 1} failed: {e}")
                    if attempt < retries:
                        time.sleep(2)
                    else:
                        all_passed = False
                    continue
        
        return all_passed
    
    def test_chain_of_thought(self, retries: int = 2, timeout: int = 60) -> bool:
        """Test reasoning capability"""
        if not self.session_id:
            return False
        
        for attempt in range(retries + 1):
            try:
                payload = {
                    "message": "How do I calculate Cost Performance Index (CPI)?",
                    "session_id": self.session_id
                }
                
                response = requests.post(
                    f"{self.base_url}/chat",
                    json=payload,
                    timeout=timeout
                )
                
                if response.status_code == 200:
                    data = response.json()
                    response_text = data.get('response', '')
                    
                    success = (
                        "CPI" in response_text or "Cost Performance" in response_text and
                        len(response_text) > 200
                    )
                    
                    self.log_test(
                        "Reasoning", 
                        success, 
                        f"Contains CPI: {'CPI' in response_text or 'Cost Performance' in response_text}, Response length: {len(response_text)}"
                    )
                    return success
                else:
                    self.log_test("Reasoning", False, f"HTTP {response.status_code}")
                    return False
                    
            except requests.exceptions.RequestException as e:
                self.log_test("Reasoning", False, f"Attempt {attempt + 1} failed: {e}")
                if attempt < retries:
                    time.sleep(2)
                continue
        
        return False
    
    def test_references(self, retries: int = 2, timeout: int = 60) -> bool:
        """Test document references"""
        if not self.session_id:
            return False
        
        for attempt in range(retries + 1):
            try:
                payload = {
                    "message": "What are the PMBOK knowledge areas?",
                    "session_id": self.session_id
                }
                
                response = requests.post(
                    f"{self.base_url}/chat",
                    json=payload,
                    timeout=timeout
                )
                
                if response.status_code == 200:
                    data = response.json()
                    response_text = data.get('response', '')
                    
                    success = (
                        "**References**:" in response_text and
                        data.get('sources_count', 0) >= 0
                    )
                    
                    self.log_test(
                        "Document References", 
                        success, 
                        f"Contains References: {'**References**:' in response_text}, Sources: {data.get('sources_count', 0)}"
                    )
                    return success
                else:
                    self.log_test("Document References", False, f"HTTP {response.status_code}")
                    return False
                    
            except requests.exceptions.RequestException as e:
                self.log_test("Document References", False, f"Attempt {attempt + 1} failed: {e}")
                if attempt < retries:
                    time.sleep(2)
                continue
        
        return False

# Run tests
if __name__ == "__main__":
    tester = PMChatbotAPITester()
    tester.test_api_health()
    tester.test_root_endpoint()
    tester.test_session_creation()
    tester.test_chat_basic()
    tester.test_multilingual_support()
    tester.test_chain_of_thought()
    tester.test_references()