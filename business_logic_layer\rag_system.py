import os
import threading
import torch
import json
import pickle
import gzip
import re
from datetime import datetime
from typing import List, Tuple, Optional, Any, Dict
from dataclasses import dataclass
import logging
import faiss
import numpy as np
from sentence_transformers import SentenceTransformer
from langdetect import detect, DetectorFactory, LangDetectException
import PyPDF2
import shutil

# Ensure consistent language detection
DetectorFactory.seed = 0

@dataclass
class Document:
    """Simple document class to replace langchain Document"""
    page_content: str
    metadata: Dict[str, Any]

@dataclass
class ContextSource:
    """Structured context source information"""
    content: str
    source: str
    page: str
    section: str
    relevance_score: float
    chunk_id: int
    metadata: Dict[str, Any]

class ProjectManagementRAGSystem:
    """Enhanced RAG system for Project Management documents with admin capabilities"""
    
    def __init__(self, docs_folder: str = "fastapi/Docs"):
        self.docs_folder = os.path.abspath(docs_folder)
        self.vector_store_path = os.path.join(self.docs_folder, "pm_rag_store.pkl.gz")
        self.embedding_dim = 384
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.max_doc_chunk_size = 1200
        self.chunk_overlap = 200
        self.relevance_threshold = 0.25
        self.min_pm_keywords = 1
        self._lock = threading.RLock()
        
        self._setup_logging()
        self._setup_pm_knowledge()
        self._load_embedder()
        
        os.makedirs(self.docs_folder, exist_ok=True)
        
        try:
            self.faiss_index, self.rag_chunks = self._load_or_build_rag()
        except Exception as e:
            self.logger.error(f"Failed to initialize RAG system: {str(e)}")
            raise

    def _setup_logging(self):
        """Setup comprehensive logging system"""
        log_file = os.path.join(self.docs_folder, 'pm_rag.log')
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info("RAG System initialized")

    def _setup_pm_knowledge(self):
        """Setup comprehensive PM knowledge base with extended multilingual support"""
        self.pm_keywords = {
            'en': {
                'core': ['project', 'management', 'pm', 'pmbok', 'prince2', 'agile', 'scrum', 'kanban', 'methodology', 'framework', 'program', 'portfolio', 'pmp', 'waterfall', 'lean', 'hybrid', 'devops', 'safe', 'pmi', 'itil', 'six sigma', 'sustainability'],
                'processes': ['initiation', 'planning', 'execution', 'monitoring', 'controlling', 'closing', 'implementation', 'delivery', 'kickoff', 'closure', 'lifecycle', 'iteration', 'sprint', 'retrospective', 'daily standup', 'backlog grooming'],
                'knowledge_areas': ['scope', 'time', 'cost', 'quality', 'risk', 'procurement', 'stakeholder', 'integration', 'resource', 'communication', 'schedule', 'budget', 'hr', 'human resource', 'change management', 'governance', 'compliance', 'esg', 'risk appetite'],
                'tools': ['gantt', 'pert', 'cpm', 'wbs', 'raci', 'evm', 'earned value', 'critical path', 'kanban board', 'burndown', 'jira', 'trello', 'ms project', 'baseline', 'asana', 'monday.com', 'smartsheet', 'risk register', 'issue log', 'dependency map'],
                'roles': ['project manager', 'sponsor', 'stakeholder', 'team lead', 'scrum master', 'product owner', 'team member', 'pmo', 'program manager', 'portfolio manager', 'change manager', 'business analyst', 'agile coach'],
                'metrics': ['kpi', 'roi', 'npv', 'irr', 'payback', 'cpi', 'spi', 'cv', 'sv', 'performance', 'velocity', 'burn rate', 'esg metrics', 'customer satisfaction', 'csat']
            },
            'fr': {
                'core': ['projet', 'gestion', 'management', 'chef de projet', 'pmbok', 'prince2', 'agile', 'méthodologie', 'cadre', 'programme', 'portefeuille', 'pmp', 'cascade', 'lean', 'hybride', 'devops', 'safe', 'pmi', 'itil', 'six sigma', 'durabilité'],
                'processes': ['initialisation', 'planification', 'exécution', 'surveillance', 'contrôle', 'clôture', 'mise en œuvre', 'livraison', 'démarrage', 'fermeture', 'cycle de vie', 'itération', 'sprint', 'rétrospective', 'réunion quotidienne', 'toilettage du backlog'],
                'knowledge_areas': ['périmètre', 'temps', 'coût', 'qualité', 'risque', 'approvisionnement', 'parties prenantes', 'intégration', 'ressource', 'communication', 'calendrier', 'budget', 'rh', 'gestion du changement', 'gouvernance', 'conformité', 'esg', 'appétit pour le risque'],
                'tools': ['gantt', 'pert', 'chemin critique', 'sot', 'raci', 'valeur acquise', 'tableau kanban', 'burndown', 'jira', 'trello', 'ms project', 'ligne de base', 'asana', 'monday.com', 'smartsheet', 'registre des risques', 'journal des problèmes', 'carte de dépendance'],
                'roles': ['chef de projet', 'sponsor', 'partie prenante', 'chef d\'équipe', 'scrum master', 'propriétaire du produit', 'membre d\'équipe', 'pmo', 'gestionnaire de programme', 'gestionnaire de portefeuille', 'gestionnaire de changement', 'analyste d\'affaires', 'coach agile'],
                'metrics': ['icp', 'roi', 'van', 'tri', 'délai de récupération', 'icp', 'ipd', 'ec', 'ed', 'performance', 'vélocité', 'taux de combustion', 'métriques esg', 'satisfaction client', 'csat']
            },
            'ar': {
                'core': ['مشروع', 'إدارة', 'مدير مشروع', 'pmbok', 'prince2', 'أجايل', 'منهجية', 'إطار', 'برنامج', 'محفظة', 'pmp', 'شلال', 'لين', 'هجين', 'ديفوبس', 'سيف', 'pmi', 'itil', 'ستة سيجما', 'استدامة'],
                'processes': ['بدء', 'تخطيط', 'تنفيذ', 'مراقبة', 'تحكم', 'إغلاق', 'تنفيذ', 'تسليم', 'انطلاق', 'إنهاء', 'دورة حياة', 'تكرار', 'سباق', 'مراجعة', 'اجتماع يومي', 'تهيئة قائمة الأعمال'],
                'knowledge_areas': ['نطاق', 'وقت', 'تكلفة', 'جودة', 'مخاطر', 'شراء', 'أصحاب المصلحة', 'تكامل', 'موارد', 'اتصال', 'جدول', 'ميزانية', 'موارد بشرية', 'إدارة التغيير', 'حوكمة', 'امتثال', 'esg', 'رغبة في المخاطرة'],
                'tools': ['جانت', 'بيرت', 'المسار الحرج', 'هيكل تجزئة العمل', 'راسي', 'القيمة المكتسبة', 'لوحة كانبان', 'burndown', 'جيرا', 'تريلو', 'خط الأساس', 'أسانا', 'monday.com', 'smartsheet', 'سجل المخاطر', 'سجل المشكلات', 'خريطة التبعيات'],
                'roles': ['مدير مشروع', 'راعي', 'صاحب مصلحة', 'قائد فريق', 'سكرم ماستر', 'مالك المنتج', 'عضو فريق', 'مكتب إدارة المشاريع', 'مدير برنامج', 'مدير محفظة', 'مدير التغيير', 'محلل أعمال', 'مدرب أجايل'],
                'metrics': ['مؤشر أداء', 'عائد استثمار', 'صافي القيمة الحالية', 'معدل عائد داخلي', 'فترة استرداد', 'مؤشر أداء التكلفة', 'مؤشر أداء الجدول', 'الأداء', 'السرعة', 'معدل الاحتراق', 'مؤشرات esg', 'رضا العملاء', 'csat']
            }
        }

        self.pm_context_patterns = {
            'definition_request': [
                'what is', 'define', 'explain', 'ما هو', 'عرف', 'اشرح', 
                'qu\'est-ce que', 'définir', 'expliquer', 'meaning of', 'معنى', 'signification'
            ],
            'process_request': [
                'how to', 'process', 'steps', 'procedure', 'كيف', 'عملية', 'خطوات', 
                'comment', 'processus', 'étapes', 'workflow', 'سير العمل', 'flux de travail'
            ],
            'calculation_request': [
                'calculate', 'formula', 'equation', 'احسب', 'معادلة', 'calculer', 
                'formule', 'compute', 'حساب', 'calcul'
            ],
            'comparison_request': [
                'compare', 'difference', 'vs', 'versus', 'قارن', 'فرق', 
                'comparer', 'différence', 'contrast', 'مقارنة', 'contraste'
            ],
            'best_practice_request': [
                'best practice', 'recommendation', 'suggest', 'أفضل ممارسة', 'توصية', 
                'اقترح', 'meilleure pratique', 'recommandation', 'guideline', 
                'إرشاد', 'directive'
            ],
            'stakeholder_engagement': [
                'stakeholder engagement', 'manage stakeholders', 'إدارة أصحاب المصلحة', 
                'gestion des parties prenantes', 'involve stakeholders', 
                'إشراك أصحاب المصلحة', 'impliquer les parties prenantes'
            ],
            'scaling_request': [
                'scale agile', 'agile at scale', 'safe framework', 'توسيع أجايل', 
                'cadre safe', 'large-scale agile', 'أجايل واسع النطاق', 'agile à grande échelle'
            ]
        }

    def _load_embedder(self):
        """Load sentence transformer for embeddings with error handling"""
        try:
            cache_folder = os.path.join(self.docs_folder, "embedding_cache")
            os.makedirs(cache_folder, exist_ok=True)
            
            self.embedder = SentenceTransformer(
                "sentence-transformers/all-MiniLM-L6-v2",
                cache_folder=cache_folder,
                device=self.device
            )
            self.logger.info(f"Embedder loaded successfully on {self.device}")
        except Exception as e:
            self.logger.error(f"Error loading embedder: {e}")
            raise

    def _load_or_build_rag(self) -> Tuple[Optional[faiss.Index], List[Document]]:
        """Load existing RAG or build new one with validation"""
        if os.path.exists(self.vector_store_path):
            try:
                with gzip.open(self.vector_store_path, 'rb') as f:
                    index, chunks = pickle.load(f)
                
                if (index is None or chunks is None or 
                    index.ntotal == 0 or len(chunks) == 0 or 
                    index.ntotal != len(chunks)):
                    self.logger.warning("Loaded RAG system is invalid or empty. Rebuilding...")
                    return self._build_rag_system()
                
                self.logger.info(f"RAG system loaded successfully: {len(chunks)} chunks, {index.ntotal} vectors")
                return index, chunks
                
            except Exception as e:
                self.logger.warning(f"Failed to load existing RAG: {e}. Rebuilding...")
        
        return self._build_rag_system()

    def _build_rag_system(self) -> Tuple[Optional[faiss.Index], List[Document]]:
        """Build RAG system from all documents"""
        with self._lock:
            self.logger.info("Building RAG system...")
            documents = self._load_all_documents()
            
            if not documents:
                self.logger.warning("No documents found to build RAG.")
                return None, []

            chunks = self._split_documents(documents)
            if not chunks:
                self.logger.warning("No chunks created from documents.")
                return None, []
                
            index = self._build_faiss_index(chunks)
            self._save_rag_system(index, chunks)
            
            self.logger.info(f"RAG system built successfully: {len(chunks)} chunks")
            return index, chunks

    def _load_all_documents(self) -> List[Document]:
        """Load all PDF documents from the docs folder"""
        if not os.path.exists(self.docs_folder):
            self.logger.warning(f"Docs folder '{self.docs_folder}' does not exist")
            return []
            
        pdf_files = [f for f in os.listdir(self.docs_folder) if f.lower().endswith('.pdf')]
        
        if not pdf_files:
            self.logger.warning(f"No PDF documents found in '{self.docs_folder}'")
            return []

        self.logger.info(f"Loading {len(pdf_files)} PDF documents from '{self.docs_folder}'...")
        all_documents = []
        
        for filename in pdf_files:
            try:
                documents = self._load_single_document(filename)
                all_documents.extend(documents)
                self.logger.info(f"Loaded {filename}: {len(documents)} pages")
            except Exception as e:
                self.logger.error(f"Error loading {filename}: {e}")
                
        return all_documents

    def _load_single_document(self, filename: str) -> List[Document]:
        """Load all pages of a single PDF document with enhanced metadata using PyPDF2"""
        filepath = os.path.join(self.docs_folder, filename)
        
        try:
            documents = []
            with open(filepath, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page_num, page in enumerate(pdf_reader.pages):
                    page_content = page.extract_text()
                    
                    if not page_content.strip():
                        continue
                    
                    pm_relevance = self._calculate_pm_relevance_score(page_content)
                    section = self._extract_section(page_content)
                    language = self._detect_chunk_language(page_content)
                    
                    metadata = {
                        'source': filename,
                        'file_path': filepath,
                        'file_type': 'PDF',
                        'loaded_at': datetime.now().isoformat(),
                        'page': str(page_num + 1),
                        'section': section,
                        'language': language,
                        'is_pm_relevant': pm_relevance >= self.min_pm_keywords,
                        'pm_relevance_score': pm_relevance,
                        'word_count': len(page_content.split()),
                        'char_count': len(page_content)
                    }
                    
                    doc = Document(page_content=page_content, metadata=metadata)
                    documents.append(doc)
            
            self.logger.info(f"Document {filename}: Loaded {len(documents)} pages")
            return documents
            
        except Exception as e:
            self.logger.error(f"Error loading {filename}: {e}")
            return []

    def _assess_pm_relevance(self, content: str) -> bool:
        """Assess PM relevance for metadata (not used for filtering)"""
        if not content or len(content.strip()) < 50:
            return False
            
        content_lower = content.lower()
        pm_keyword_count = 0
        
        for lang_keywords in self.pm_keywords.values():
            for category, keywords in lang_keywords.items():
                weight = 2 if category == 'core' else 1
                for keyword in keywords:
                    if keyword in content_lower:
                        pm_keyword_count += weight
        
        pm_patterns = [
            r'\bproject\s+management\b',
            r'\bpm\s+methodology\b',
            r'\bwork\s+breakdown\s+structure\b',
            r'\bcritical\s+path\b',
            r'\bearned\s+value\b',
            r'\bstakeholder\s+management\b',
            r'\bagile\s+framework\b',
            r'\bsprint\s+planning\b'
        ]
        
        for pattern in pm_patterns:
            if re.search(pattern, content_lower):
                pm_keyword_count += 3
        
        return pm_keyword_count >= self.min_pm_keywords

    def _split_documents(self, documents: List[Document]) -> List[Document]:
        """Split all documents into chunks with enhanced metadata using simple text splitting"""
        processed_chunks = []
        
        for doc in documents:
            chunks = self._split_text(doc.page_content)
            
            for i, chunk_text in enumerate(chunks):
                if not chunk_text.strip():
                    continue
                    
                pm_relevance = self._calculate_pm_relevance_score(chunk_text)
                section = self._extract_section(chunk_text)
                language = self._detect_chunk_language(chunk_text)
                
                # Create new metadata for chunk
                chunk_metadata = doc.metadata.copy()
                chunk_metadata.update({
                    'chunk_id': len(processed_chunks),
                    'chunk_length': len(chunk_text),
                    'word_count': len(chunk_text.split()),
                    'section': section,
                    'language': language,
                    'is_pm_relevant': pm_relevance >= self.min_pm_keywords,
                    'pm_relevance_score': pm_relevance,
                    'parent_document': doc.metadata.get('source', 'unknown'),
                    'chunk_index': i
                })
                
                chunk_doc = Document(page_content=chunk_text, metadata=chunk_metadata)
                processed_chunks.append(chunk_doc)
        
        return processed_chunks

    def _split_text(self, text: str) -> List[str]:
        """Simple text splitter to replace RecursiveCharacterTextSplitter"""
        if len(text) <= self.max_doc_chunk_size:
            return [text]
        
        chunks = []
        separators = ["\n\n", "\n", ". ", "! ", "? ", " ", ""]
        
        def split_by_separator(text: str, separators: List[str]) -> List[str]:
            if not separators or len(text) <= self.max_doc_chunk_size:
                return [text]
            
            separator = separators[0]
            remaining_separators = separators[1:]
            
            if separator not in text:
                return split_by_separator(text, remaining_separators)
            
            parts = text.split(separator)
            result = []
            current_chunk = ""
            
            for part in parts:
                potential_chunk = current_chunk + separator + part if current_chunk else part
                
                if len(potential_chunk) <= self.max_doc_chunk_size:
                    current_chunk = potential_chunk
                else:
                    if current_chunk:
                        result.append(current_chunk)
                    
                    if len(part) > self.max_doc_chunk_size:
                        # Recursively split the large part
                        sub_chunks = split_by_separator(part, remaining_separators)
                        result.extend(sub_chunks)
                        current_chunk = ""
                    else:
                        current_chunk = part
            
            if current_chunk:
                result.append(current_chunk)
            
            return result
        
        chunks = split_by_separator(text, separators)
        
        # Apply overlap
        if self.chunk_overlap > 0 and len(chunks) > 1:
            overlapped_chunks = []
            for i, chunk in enumerate(chunks):
                if i == 0:
                    overlapped_chunks.append(chunk)
                else:
                    # Add overlap from previous chunk
                    prev_chunk = chunks[i-1]
                    overlap = prev_chunk[-self.chunk_overlap:] if len(prev_chunk) > self.chunk_overlap else prev_chunk
                    overlapped_chunk = overlap + " " + chunk
                    overlapped_chunks.append(overlapped_chunk)
            chunks = overlapped_chunks
        
        return chunks

    def _detect_chunk_language(self, content: str) -> str:
        """Detect language of a text chunk"""
        try:
            return detect(content)
        except LangDetectException:
            if any('\u0600' <= char <= '\u06FF' for char in content):
                return 'ar'
            elif any('\u00C0' <= char <= '\u017F' for char in content):
                return 'fr'
            return 'en'

    def _calculate_pm_relevance_score(self, content: str) -> float:
        """Calculate PM relevance score for metadata"""
        content_lower = content.lower()
        total_score = 0.0
        max_possible_score = 0.0
        
        category_weights = {
            'core': 0.35,
            'processes': 0.25,
            'knowledge_areas': 0.20,
            'tools': 0.10,
            'roles': 0.05,
            'metrics': 0.05
        }
        
        for lang, lang_keywords in self.pm_keywords.items():
            for category, keywords in lang_keywords.items():
                if category in category_weights:
                    weight = category_weights[category]
                    found_keywords = sum(1 for keyword in keywords if keyword in content_lower)
                    if keywords:
                        category_score = (found_keywords / len(keywords)) * weight
                        total_score += category_score
                    max_possible_score += weight
        
        pm_patterns = [
            r'\bproject\s+management\b',
            r'\bpm\s+methodology\b',
            r'\bwork\s+breakdown\s+structure\b',
            r'\bcritical\s+path\s+method\b',
            r'\bstakeholder\s+engagement\b',
            r'\bagile\s+scaling\b',
            r'\brisk\s+appetite\b'
        ]
        
        pattern_bonus = sum(0.1 for pattern in pm_patterns if re.search(pattern, content_lower))
        total_score += min(pattern_bonus, 0.2)
        
        final_score = min(total_score / max_possible_score if max_possible_score > 0 else 0, 1.0)
        return final_score

    def _extract_section(self, content: str) -> str:
        """Enhanced section extraction with improved pattern matching"""
        lines = [line.strip() for line in content.split('\n') if line.strip()][:10]
        
        for line in lines:
            # Numbered sections (e.g., "1.2.3 Risk Management")
            if re.match(r'^\s*\d+[\.\d\s]*[A-Za-z\s]{5,100}', line):
                return line[:100].strip()
            
            # Headers with colons or specific PM terms
            if (':' in line or any(term in line.lower() for term in ['management', 'plan', 'strategy', 'إدارة', 'خطة', 'gestion', 'stratégie'])) and 5 < len(line.split(':')[0]) < 100:
                return line.split(':')[0].strip()[:100]
            
            # Title case, uppercase, or bolded headers
            if (line.istitle() or line.isupper() or re.match(r'^\*.*\*$', line)) and 5 < len(line) < 100:
                return line[:100].strip()
            
            # Chapter/section/part indicators
            if any(indicator in line.lower() for indicator in ['chapter', 'section', 'part', 'chapitre', 'section', 'partie', 'فصل', 'قسم', 'جزء']):
                return line[:100].strip()
        
        # Fallback to first meaningful line
        for line in lines:
            if len(line) > 10 and not line.isdigit():
                return line[:100].strip()
        
        return "General Content"

    def _build_faiss_index(self, chunks: List[Document]) -> Optional[faiss.Index]:
        """Build optimized FAISS index with error handling"""
        if not chunks:
            self.logger.warning("No chunks to build FAISS index.")
            return None
        
        texts = [chunk.page_content for chunk in chunks]
        self.logger.info(f"Generating embeddings for {len(texts)} chunks...")
        
        try:
            embeddings = self.embedder.encode(
                texts,
                convert_to_tensor=False,
                show_progress_bar=True,
                batch_size=16,
                normalize_embeddings=True,
                device=self.device
            ).astype('float32')
            
            index = faiss.IndexFlatIP(self.embedding_dim)
            index.add(embeddings)
            
            self.logger.info(f"FAISS index built successfully with {index.ntotal} vectors")
            return index
            
        except Exception as e:
            self.logger.error(f"Error building FAISS index: {e}")
            raise

    def _save_rag_system(self, index: Optional[faiss.Index], chunks: List[Document]):
        """Save RAG system with backup functionality"""
        try:
            if os.path.exists(self.vector_store_path):
                backup_path = f"{self.vector_store_path}.backup"
                shutil.copy2(self.vector_store_path, backup_path)
                self.logger.info("Created backup of existing RAG system")
            
            with gzip.open(self.vector_store_path, 'wb') as f:
                pickle.dump((index, chunks), f, protocol=pickle.HIGHEST_PROTOCOL)
            
            self.logger.info("RAG system saved successfully")
            
        except Exception as e:
            self.logger.error(f"Error saving RAG system: {e}")
            backup_path = f"{self.vector_store_path}.backup"
            if os.path.exists(backup_path):
                shutil.copy2(backup_path, self.vector_store_path)
                self.logger.info("Restored backup due to save failure")
            raise

    def add_document_to_rag(self, file_path: str) -> bool:
        """Add document to RAG system with atomic updates"""
        with self._lock:
            if not os.path.exists(file_path):
                self.logger.error(f"File not found: {file_path}")
                return False
            
            if not file_path.lower().endswith('.pdf'):
                self.logger.error(f"Unsupported file type: {file_path}")
                return False
            
            filename = os.path.basename(file_path)
            dest_path = os.path.join(self.docs_folder, filename)
            
            try:
                if os.path.exists(dest_path):
                    backup_path = f"{dest_path}.backup"
                    shutil.copy2(dest_path, backup_path)
                    self.logger.info(f"Created backup of existing document: {filename}")
                
                shutil.copy2(file_path, dest_path)
                self.logger.info(f"Document copied to: {dest_path}")
                
                old_index, old_chunks = self.faiss_index, self.rag_chunks
                
                try:
                    self.faiss_index, self.rag_chunks = self._build_rag_system()
                    self.logger.info(f"Document '{filename}' successfully added. RAG system updated with {len(self.rag_chunks)} total chunks.")
                    return True
                    
                except Exception as rebuild_error:
                    self.logger.error(f"Failed to rebuild RAG system: {rebuild_error}")
                    self.faiss_index, self.rag_chunks = old_index, old_chunks
                    if os.path.exists(dest_path):
                        os.remove(dest_path)
                    backup_path = f"{dest_path}.backup"
                    if os.path.exists(backup_path):
                        shutil.copy2(backup_path, dest_path)
                        os.remove(backup_path)
                    return False
                    
            except Exception as e:
                self.logger.error(f"Failed to add document '{filename}': {e}")
                return False

    def remove_document_from_rag(self, filename: str) -> bool:
        """Remove document and rebuild RAG system"""
        with self._lock:
            file_path = os.path.join(self.docs_folder, filename)
            
            if not os.path.exists(file_path):
                self.logger.error(f"Document not found: {filename}")
                return False
            
            try:
                backup_path = f"{file_path}.backup"
                shutil.copy2(file_path, backup_path)
                
                os.remove(file_path)
                self.logger.info(f"Document removed: {filename}")
                
                old_index, old_chunks = self.faiss_index, self.rag_chunks
                
                try:
                    self.faiss_index, self.rag_chunks = self._build_rag_system()
                    if os.path.exists(backup_path):
                        os.remove(backup_path)
                    self.logger.info(f"Document '{filename}' removed successfully. RAG system updated.")
                    return True
                    
                except Exception as rebuild_error:
                    self.logger.error(f"Failed to rebuild RAG after removal: {rebuild_error}")
                    shutil.copy2(backup_path, file_path)
                    os.remove(backup_path)
                    self.faiss_index, self.rag_chunks = old_index, old_chunks
                    return False
                    
            except Exception as e:
                self.logger.error(f"Failed to remove document '{filename}': {e}")
                return False

    def retrieve_context(self, query: str, language: str = "en", top_k: int = 4) -> List[ContextSource]:
        """Retrieve context with relevance scoring"""
        if self.faiss_index is None or not self.rag_chunks:
            self.logger.warning("RAG system not initialized or no chunks available")
            return []
        
        try:
            query_embedding = self.embedder.encode(
                [query],
                convert_to_tensor=False,
                normalize_embeddings=True,
                device=self.device
            ).astype('float32')
            
            search_k = min(top_k * 3, len(self.rag_chunks))
            distances, indices = self.faiss_index.search(query_embedding, search_k)
            
            context_sources = []
            seen_content = set()
            
            for score, idx in zip(distances[0], indices[0]):
                if len(context_sources) >= top_k:
                    break
                
                if score < self.relevance_threshold:
                    continue
                
                if 0 <= idx < len(self.rag_chunks):
                    chunk = self.rag_chunks[idx]
                    content_hash = hash(chunk.page_content[:200])
                    if content_hash in seen_content:
                        continue
                    seen_content.add(content_hash)
                    
                    context_source = ContextSource(
                        content=chunk.page_content,
                        source=chunk.metadata.get('source', 'Unknown Document'),
                        page=str(chunk.metadata.get('page', 'N/A')),
                        section=chunk.metadata.get('section', 'General Content'),
                        relevance_score=float(score),
                        chunk_id=int(chunk.metadata.get('chunk_id', idx)),
                        metadata=chunk.metadata
                    )
                    context_sources.append(context_source)
            
            context_sources.sort(key=lambda x: x.relevance_score, reverse=True)
            
            self.logger.info(f"Retrieved {len(context_sources)} relevant chunks for query in '{language}'")
            for i, source in enumerate(context_sources):
                self.logger.debug(f"Context {i+1}: {source.source} (Page {source.page}, Section {source.section}) - Score: {source.relevance_score:.3f}")
            
            return context_sources[:top_k]
            
        except Exception as e:
            self.logger.error(f"Error in context retrieval: {e}")
            return []

    def get_document_stats(self) -> Dict[str, Any]:
        """Get comprehensive document statistics"""
        try:
            if not self.rag_chunks:
                return {"total_chunks": 0, "documents": []}
            
            from collections import defaultdict
            doc_stats = defaultdict(lambda: {
                'chunks': 0,
                'total_words': 0,
                'avg_relevance': 0.0,
                'languages': set(),
                'pages': set()
            })
            
            for chunk in self.rag_chunks:
                source = chunk.metadata.get('source', 'Unknown')
                doc_stats[source]['chunks'] += 1
                doc_stats[source]['total_words'] += chunk.metadata.get('word_count', 0)
                doc_stats[source]['avg_relevance'] += chunk.metadata.get('pm_relevance_score', 0.0)
                doc_stats[source]['languages'].add(chunk.metadata.get('language', 'unknown'))
                doc_stats[source]['pages'].add(chunk.metadata.get('page', 'N/A'))
            
            processed_stats = []
            for doc_name, stats in doc_stats.items():
                if stats['chunks'] > 0:
                    stats['avg_relevance'] /= stats['chunks']
                stats['languages'] = list(stats['languages'])
                stats['pages'] = sorted(list(stats['pages']))
                stats['page_count'] = len(stats['pages'])
                
                processed_stats.append({
                    'document': doc_name,
                    **stats
                })
            
            return {
                "total_chunks": len(self.rag_chunks),
                "total_documents": len(processed_stats),
                "embedding_dimension": self.embedding_dim,
                "relevance_threshold": self.relevance_threshold,
                "documents": processed_stats
            }
            
        except Exception as e:
            self.logger.error(f"Error getting document stats: {e}")
            return {"error": str(e)}

    def search_in_documents(self, query: str, language: str = "en", top_k: int = 10) -> List[Dict[str, Any]]:
        """Search for specific content in documents"""
        try:
            context_sources = self.retrieve_context(query, language, top_k)
            
            results = []
            for source in context_sources:
                results.append({
                    "content": source.content[:500] + "..." if len(source.content) > 500 else source.content,
                    "source": source.source,
                    "page": source.page,
                    "section": source.section,
                    "relevance_score": source.relevance_score,
                    "metadata": {
                        "chunk_id": source.chunk_id,
                        "word_count": source.metadata.get('word_count', 0),
                        "language": source.metadata.get('language', 'unknown')
                    }
                })
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error searching documents: {e}")
            return []

    def _is_pm_related_query(self, query: str, language: str) -> bool:
        """Enhanced PM query detection"""
        query_lower = query.lower().strip()
        
        if language in self.pm_keywords:
            lang_keywords = self.pm_keywords[language]
            for category_keywords in lang_keywords.items():
                if any(keyword in query_lower for keyword in category_keywords):
                    return True
        
        if language != 'en':
            en_keywords = self.pm_keywords['en']
            for category_keywords in en_keywords.values():
                if any(keyword in query_lower for keyword in category_keywords):
                    return True
        
        pm_patterns = [
            r'\bproject\s+management\b',
            r'\bpm\s+related\b',
            r'\bproject\s+plan\b',
            r'\bwork\s+breakdown\b',
            r'\bstakeholder\s+engagement\b',
            r'\bagile\s+scaling\b'
        ]
        
        return any(re.search(pattern, query_lower) for pattern in pm_patterns)

    def rebuild_rag_system(self) -> bool:
        """Force rebuild of the entire RAG system"""
        with self._lock:
            try:
                self.logger.info("Force rebuilding RAG system...")
                self.faiss_index, self.rag_chunks = self._build_rag_system()
                self.logger.info("RAG system rebuilt successfully")
                return True
            except Exception as e:
                self.logger.error(f"Failed to rebuild RAG system: {e}")
                return False

    def get_system_info(self) -> Dict[str, Any]:
        """Get comprehensive system information"""
        return {
            "docs_folder": self.docs_folder,
            "vector_store_path": self.vector_store_path,
            "embedding_dimension": self.embedding_dim,
            "device": self.device,
            "chunk_size": self.max_doc_chunk_size,
            "chunk_overlap": self.chunk_overlap,
            "relevance_threshold": self.relevance_threshold,
            "min_pm_keywords": self.min_pm_keywords,
            "index_initialized": self.faiss_index is not None,
            "chunks_loaded": len(self.rag_chunks) if self.rag_chunks else 0,
            "vector_store_exists": os.path.exists(self.vector_store_path),
            "supported_languages": list(self.pm_keywords.keys())
        }