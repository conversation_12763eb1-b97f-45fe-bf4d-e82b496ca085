# PowerShell script for Docker production commands
# PM Assistant Production Helper

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("deploy", "stop", "restart", "logs", "status", "backup", "restore", "update")]
    [string]$Command,
    
    [string]$Service = "pm-assistant"
)

$ErrorActionPreference = "Stop"

function Write-Info {
    param([string]$Message)
    Write-Host "🚀 $Message" -ForegroundColor Cyan
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️ $Message" -ForegroundColor Yellow
}

switch ($Command) {
    "deploy" {
        Write-Info "Deploying to production..."
        
        # Check if .env.prod exists
        if (-not (Test-Path ".env.prod")) {
            Write-Error ".env.prod file not found! Please create it from .env.example"
            exit 1
        }
        
        # Build and deploy
        docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d --build
        Write-Success "Production deployment completed!"
        Write-Info "Application available at: https://your-domain.com"
    }
    
    "stop" {
        Write-Warning "Stopping production environment..."
        docker-compose -f docker-compose.prod.yml down
        Write-Success "Production environment stopped!"
    }
    
    "restart" {
        Write-Info "Restarting production service: $Service..."
        docker-compose -f docker-compose.prod.yml restart $Service
        Write-Success "Service $Service restarted!"
    }
    
    "logs" {
        Write-Info "Showing logs for $Service..."
        docker-compose -f docker-compose.prod.yml logs -f $Service
    }
    
    "status" {
        Write-Info "Checking production status..."
        docker-compose -f docker-compose.prod.yml ps
        Write-Info "Container health status:"
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    }
    
    "backup" {
        Write-Info "Creating production backup..."
        $timestamp = Get-Date -Format "yyyy-MM-dd-HH-mm-ss"
        $backupDir = "backups/$timestamp"
        
        New-Item -ItemType Directory -Path $backupDir -Force
        
        # Backup database
        docker-compose -f docker-compose.prod.yml exec postgres pg_dump -U pm_user pm_assistant > "$backupDir/database.sql"
        
        # Backup volumes
        docker run --rm -v pm_logs:/data -v ${PWD}/${backupDir}:/backup alpine tar czf /backup/logs.tar.gz -C /data .
        docker run --rm -v postgres_data:/data -v ${PWD}/${backupDir}:/backup alpine tar czf /backup/postgres.tar.gz -C /data .
        
        Write-Success "Backup created in $backupDir"
    }
    
    "restore" {
        Write-Warning "This will restore from the latest backup. Continue? (y/N)"
        $confirm = Read-Host
        if ($confirm -ne "y") {
            Write-Info "Restore cancelled."
            exit 0
        }
        
        $latestBackup = Get-ChildItem "backups" | Sort-Object Name -Descending | Select-Object -First 1
        if (-not $latestBackup) {
            Write-Error "No backups found!"
            exit 1
        }
        
        Write-Info "Restoring from backup: $($latestBackup.Name)"
        # Add restore logic here
        Write-Success "Restore completed!"
    }
    
    "update" {
        Write-Info "Updating production deployment..."
        docker-compose -f docker-compose.prod.yml pull
        docker-compose -f docker-compose.prod.yml up -d --build
        Write-Success "Production update completed!"
    }
}
