"""
Authentication Router
====================
Handles all authentication-related endpoints
"""

from fastapi import APIRouter, HTTPException, Depends, status
from typing import Dict, Any

from models.user_models import User<PERSON><PERSON>, UserLogin, User, TokenResponse
from services.auth_service import AuthService

router = APIRouter(prefix="/auth", tags=["authentication"])
auth_service = AuthService()


@router.post("/register", response_model=TokenResponse)
async def register_user(user_data: UserCreate) -> TokenResponse:
    """Register a new user"""
    try:
        result = await auth_service.register_user(user_data)
        return result
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Registration failed")


@router.post("/login", response_model=TokenResponse)
async def login_user(login_data: UserLogin) -> TokenResponse:
    """Authenticate user and return token"""
    try:
        result = await auth_service.login_user(login_data)
        return result
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Login failed")


@router.get("/me", response_model=User)
async def get_current_user_info(current_user: Dict[str, Any] = Depends(auth_service.get_current_user)) -> User:
    """Get current user information"""
    try:
        user = await auth_service.get_user_by_id(current_user["id"])
        return user
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")


@router.post("/logout")
async def logout_user(current_user: Dict[str, Any] = Depends(auth_service.get_current_user)) -> Dict[str, str]:
    """Logout current user"""
    try:
        await auth_service.logout_user(current_user["id"])
        return {"message": "Successfully logged out"}
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Logout failed")
