<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PM Virtual Assistant - UI/UX Mockup</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f8fafc;
            color: #334155;
            line-height: 1.6;
        }
        
        .mockup-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
        }
        
        .flow-section {
            margin-bottom: 60px;
        }
        
        .flow-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 30px;
            color: #1e293b;
            text-align: center;
        }
        
        .screens-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .screen {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
        }
        
        .screen:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
        }
        
        .screen-header {
            padding: 16px 20px;
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .screen-dots {
            display: flex;
            gap: 4px;
        }
        
        .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        
        .dot.red { background: #ef4444; }
        .dot.yellow { background: #f59e0b; }
        .dot.green { background: #10b981; }
        
        .screen-title {
            font-weight: 600;
            font-size: 14px;
            margin-left: auto;
        }
        
        .screen-content {
            padding: 24px;
            min-height: 400px;
        }
        
        /* Authentication Screens */
        .auth-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
            max-width: 280px;
            margin: 40px auto;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-label {
            font-weight: 500;
            font-size: 14px;
            color: #374151;
        }
        
        .form-input {
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563eb;
        }
        
        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
        }
        
        /* Dashboard Layouts */
        .dashboard {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 12px;
        }
        
        .dashboard-nav {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }
        
        .nav-card {
            padding: 20px;
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .nav-card:hover {
            border-color: #3b82f6;
            transform: translateY(-2px);
        }
        
        .nav-icon {
            width: 32px;
            height: 32px;
            margin: 0 auto 12px;
            background: #3b82f6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        /* Mobile Navigation */
        .mobile-nav {
            display: flex;
            justify-content: space-around;
            padding: 12px;
            background: #f1f5f9;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #6b7280;
            cursor: pointer;
        }
        
        .nav-item.active {
            color: #3b82f6;
        }
        
        .nav-item:hover {
            color: #3b82f6;
        }
        
        /* Chat Interface */
        .chat-container {
            display: flex;
            flex-direction: column;
            height: 400px;
        }
        
        .chat-header {
            padding: 16px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f9fafb;
        }
        
        .message {
            margin-bottom: 16px;
            display: flex;
            gap: 12px;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message-bubble {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 14px;
        }
        
        .message.user .message-bubble {
            background: #3b82f6;
            color: white;
        }
        
        .message.ai .message-bubble {
            background: white;
            border: 1px solid #e5e7eb;
        }
        
        .chat-input {
            padding: 16px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            gap: 12px;
        }
        
        .chat-input input {
            flex: 1;
            padding: 12px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        /* Admin Interfaces */
        .admin-panel {
            background: linear-gradient(135deg, #ff9800, #e65100);
            color: white;
        }
        
        .admin-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 16px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }
        
        /* Knowledge Base */
        .kb-upload {
            border: 2px dashed #d1d5db;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
        }
        
        .file-list {
            margin-top: 20px;
        }
        
        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 8px;
        }
        
        /* Flow Arrows */
        .flow-arrows {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0;
        }
        
        .arrow {
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-top: 20px solid #3b82f6;
            margin: 0 20px;
        }
        
        .flow-text {
            background: #3b82f6;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .lang-switch {
            display: flex;
            gap: 8px;
            background: #f3f4f6;
            padding: 4px;
            border-radius: 8px;
            margin: 16px 0;
        }
        
        .lang-btn {
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .lang-btn.active {
            background: #3b82f6;
            color: white;
        }
        
        @media (max-width: 768px) {
            .screens-grid {
                grid-template-columns: 1fr;
            }
            
            .dashboard-nav {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="mockup-container">
        <div class="header">
            <h1>PM Virtual Assistant - UI/UX Mockup</h1>
            <p>Complete User Journey & Interface Design</p>
        </div>
        
        <!-- Authentication Flow -->
        <div class="flow-section">
            <h2 class="flow-title">🔐 Authentication Flow</h2>
            <div class="screens-grid">
                <div class="screen">
                    <div class="screen-header">
                        <div class="screen-dots">
                            <div class="dot red"></div>
                            <div class="dot yellow"></div>
                            <div class="dot green"></div>
                        </div>
                        <div class="screen-title">Login Screen</div>
                    </div>
                    <div class="screen-content">
                        <div class="auth-form">
                            <h3 style="text-align: center; margin-bottom: 20px;">PM Assistant Login</h3>
                            <div class="form-group">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-input" placeholder="<EMAIL>">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Password</label>
                                <input type="password" class="form-input" placeholder="••••••••">
                            </div>
                            <button class="btn btn-primary">Sign In</button>
                            <button class="btn btn-secondary">Register</button>
                        </div>
                    </div>
                </div>

                <div class="screen">
                    <div class="screen-header">
                        <div class="screen-dots">
                            <div class="dot red"></div>
                            <div class="dot yellow"></div>
                            <div class="dot green"></div>
                        </div>
                        <div class="screen-title">Role Selection</div>
                    </div>
                    <div class="screen-content">
                        <h3 style="text-align: center; margin-bottom: 30px;">Select Your Role</h3>
                        <div style="display: flex; flex-direction: column; gap: 20px; max-width: 280px; margin: 0 auto;">
                            <div class="nav-card" style="padding: 24px;">
                                <div class="nav-icon" style="background: #2196F3;">👤</div>
                                <h4>Regular User</h4>
                                <p style="font-size: 12px; color: #6b7280; margin-top: 8px;">Access chat interface and basic features</p>
                            </div>
                            <div class="nav-card" style="padding: 24px;">
                                <div class="nav-icon" style="background: #FF9800;">⚙️</div>
                                <h4>Admin User</h4>
                                <p style="font-size: 12px; color: #6b7280; margin-top: 8px;">Full system access and management tools</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Flow Arrow -->
        <div class="flow-arrows">
            <div class="flow-text">User Authentication Complete</div>
            <div class="arrow"></div>
        </div>

        <!-- Regular User Flow -->
        <div class="flow-section">
            <h2 class="flow-title">👤 Regular User Dashboard & Features</h2>
            <div class="screens-grid">
                <div class="screen">
                    <div class="screen-header">
                        <div class="screen-dots">
                            <div class="dot red"></div>
                            <div class="dot yellow"></div>
                            <div class="dot green"></div>
                        </div>
                        <div class="screen-title">Regular Dashboard (Mobile)</div>
                    </div>
                    <div class="screen-content">
                        <div class="dashboard">
                            <div class="mobile-nav">
                                <div class="nav-item active">
                                    <div style="font-size: 24px;">💬</div>
                                    <span>Chat</span>
                                </div>
                                <div class="nav-item">
                                    <div style="font-size: 24px;">📋</div>
                                    <span>History</span>
                                </div>
                                <div class="nav-item">
                                    <div style="font-size: 24px;">⚙️</div>
                                    <span>Profile</span>
                                </div>
                            </div>
                            <div class="dashboard-header">
                                <div>
                                    <h3>Welcome back, John!</h3>
                                    <p style="opacity: 0.9; font-size: 14px;">Your PM Assistant is ready</p>
                                </div>
                            </div>
                            <div class="dashboard-nav">
                                <div class="nav-card">
                                    <div class="nav-icon">💬</div>
                                    <h4>Chat</h4>
                                </div>
                                <div class="nav-card">
                                    <div class="nav-icon">📋</div>
                                    <h4>History</h4>
                                </div>
                                <div class="nav-card">
                                    <div class="nav-icon">⚙️</div>
                                    <h4>Profile</h4>
                                </div>
                            </div>
                            <div class="lang-switch">
                                <button class="lang-btn active">EN</button>
                                <button class="lang-btn">FR</button>
                                <button class="lang-btn">AR</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="screen">
                    <div class="screen-header">
                        <div class="screen-dots">
                            <div class="dot red"></div>
                            <div class="dot yellow"></div>
                            <div class="dot green"></div>
                        </div>
                        <div class="screen-title">Chat Interface</div>
                    </div>
                    <div class="screen-content">
                        <div class="chat-container">
                            <div class="chat-header">
                                <h4>PM Assistant Chat</h4>
                                <div style="display: flex; gap: 8px;">
                                    <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">New Chat</button>
                                    <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">EN</button>
                                </div>
                            </div>
                            <div class="chat-messages">
                                <div class="message ai">
                                    <div class="message-bubble">
                                        Hello! I'm your PM Assistant. How can I help you with your project management tasks today?
                                    </div>
                                </div>
                                <div class="message user">
                                    <div class="message-bubble">
                                        Can you help me create a project timeline for a mobile app development?
                                    </div>
                                </div>
                                <div class="message ai">
                                    <div class="message-bubble">
                                        I'd be happy to help you create a project timeline for mobile app development. Let me break this down into key phases...
                                    </div>
                                </div>
                            </div>
                            <div class="chat-input">
                                <input type="text" placeholder="Type your message...">
                                <button class="btn btn-primary">Send</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="screen">
                    <div class="screen-header">
                        <div class="screen-dots">
                            <div class="dot red"></div>
                            <div class="dot yellow"></div>
                            <div class="dot green"></div>
                        </div>
                        <div class="screen-title">Chat History</div>
                    </div>
                    <div class="screen-content">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                            <h4>Conversation History</h4>
                            <input type="text" class="form-input" placeholder="Search conversations..." style="width: 200px; margin: 0;">
                        </div>
                        <div style="display: flex; flex-direction: column; gap: 12px;">
                            <div class="file-item">
                                <div>
                                    <div style="font-weight: 500;">Mobile App Timeline Discussion</div>
                                    <div style="font-size: 12px; color: #6b7280;">2 hours ago • 15 messages</div>
                                </div>
                                <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">View</button>
                            </div>
                            <div class="file-item">
                                <div>
                                    <div style="font-weight: 500;">Agile Best Practices</div>
                                    <div style="font-size: 12px; color: #6b7280;">Yesterday • 8 messages</div>
                                </div>
                                <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">View</button>
                            </div>
                            <div class="file-item">
                                <div>
                                    <div style="font-weight: 500;">Risk Management Framework</div>
                                    <div style="font-size: 12px; color: #6b7280;">3 days ago • 22 messages</div>
                                </div>
                                <button class="btn btn-secondary" style="padding: 6px 12px; font-size: 12px;">View</button>
                            </div>
                        </div>
                        <button class="btn btn-primary" style="margin-top: 20px; width: 100%;">Export All Conversations</button>
                    </div>
                </div>

                <div class="screen">
                    <div class="screen-header">
                        <div class="screen-dots">
                            <div class="dot red"></div>
                            <div class="dot yellow"></div>
                            <div class="dot green"></div>
                        </div>
                        <div class="screen-title">Profile Settings</div>
                    </div>
                    <div class="screen-content">
                        <h4 style="margin-bottom: 20px;">Profile Settings</h4>
                        <div style="display: flex; flex-direction: column; gap: 20px;">
                            <div class="form-group">
                                <label class="form-label">Display Name</label>
                                <input type="text" class="form-input" value="John Smith">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Language Preference</label>
                                <select class="form-input">
                                    <option>English</option>
                                    <option>Français</option>
                                    <option>العربية</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Theme</label>
                                <div class="lang-switch">
                                    <button class="lang-btn active">Light</button>
                                    <button class="lang-btn">Dark</button>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Notifications</label>
                                <div style="display: flex; gap: 16px; margin-top: 8px;">
                                    <label style="display: flex; align-items: center; gap: 8px; font-size: 14px;">
                                        <input type="checkbox" checked> Email notifications
                                    </label>
                                    <label style="display: flex; align-items: center; gap: 8px; font-size: 14px;">
                                        <input type="checkbox"> Push notifications
                                    </label>
                                </div>
                            </div>
                            <button class="btn btn-primary">Save Changes</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Flow Arrow -->
        <div class="flow-arrows">
            <div class="flow-text">Admin Access</div>
            <div class="arrow"></div>
        </div>

        <!-- Admin Flow -->
        <div class="flow-section">
            <h2 class="flow-title">⚙️ Admin Dashboard & Management</h2>
            <div class="screens-grid">
                <div class="screen">
                    <div class="screen-header">
                        <div class="screen-dots">
                            <div class="dot red"></div>
                            <div class="dot yellow"></div>
                            <div class="dot green"></div>
                        </div>
                        <div class="screen-title">Admin Dashboard (Mobile)</div>
                    </div>
                    <div class="screen-content">
                        <div class="dashboard">
                            <div class="mobile-nav">
                                <div class="nav-item active">
                                    <div style="font-size: 24px;">💬</div>
                                    <span>Chat</span>
                                </div>
                                <div class="nav-item">
                                    <div style="font-size: 24px;">📚</div>
                                    <span>Knowledge Base</span>
                                </div>
                                <div class="nav-item">
                                    <div style="font-size: 24px;">👥</div>
                                    <span>Users</span>
                                </div>
                                <div class="nav-item">
                                    <div style="font-size: 24px;">📊</div>
                                    <span>Analytics</span>
                                </div>
                                <div class="nav-item">
                                    <div style="font-size: 24px;">⚙️</div>
                                    <span>Config</span>
                                </div>
                            </div>
                            <div class="dashboard-header admin-panel">
                                <div>
                                    <h3>Admin Dashboard</h3>
                                    <p style="opacity: 0.9; font-size: 14px;">System management & analytics</p>
                                </div>
                            </div>
                            <div class="dashboard-nav">
                                <div class="nav-card">
                                    <div class="nav-icon" style="background: #9C27B0;">💬</div>
                                    <h4>Chat</h4>
                                </div>
                                <div class="nav-card">
                                    <div class="nav-icon" style="background: #FF9800;">📚</div>
                                    <h4>Knowledge Base</h4>
                                </div>
                                <div class="nav-card">
                                    <div class="nav-icon" style="background: #FF9800;">👥</div>
                                    <h4>Users</h4>
                                </div>
                                <div class="nav-card">
                                    <div class="nav-icon" style="background: #FF9800;">📊</div>
                                    <h4>Analytics</h4>
                                </div>
                                <div class="nav-card">
                                    <div class="nav-icon" style="background: #FF9800;">⚙️</div>
                                    <h4>Config</h4>
                                </div>
                            </div>
                            <div class="admin-stats">
                                <div class="stat-card">
                                    <div class="stat-number">1,247</div>
                                    <div class="stat-label">Total Users</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">5,832</div>
                                    <div class="stat-label">Total Conversations</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">98.5%</div>
                                    <div class="stat-label">System Uptime</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="screen">
                    <div class="screen-header">
                        <div class="screen-dots">
                            <div class="dot red"></div>
                            <div class="dot yellow"></div>
                            <div class="dot green"></div>
                        </div>
                        <div class="screen-title">Knowledge Base Management</div>
                    </div>
                    <div class="screen-content">
                        <h4 style="margin-bottom: 20px;">Knowledge Base Management</h4>
                        <div class="kb-upload">
                            <div style="font-size: 48px; margin-bottom: 16px;">📁</div>
                            <h4>Upload Documents</h4>
                            <p style="color: #6b7280; margin: 8px 0;">Drag & drop or click to upload PDF, TXT, JSON files</p>
                            <button class="btn btn-primary">Choose Files</button>
                        </div>
                        <div class="file-list">
                            <h5 style="margin-bottom: 12px;">Current Documents</h5>
                            <div class="file-item">
                                <div>
                                    <div style="font-weight: 500;">📄 PM_Best_Practices.pdf</div>
                                    <div style="font-size: 12px; color: #6b7280;">2.3 MB • Processed • 150 vectors</div>
                                </div>
                                <div style="display: flex; gap: 8px;">
                                    <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">Edit</button>
                                    <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px; background: #ef4444; color: white;">Delete</button>
                                </div>
                            </div>
                            <div class="file-item">
                                <div>
                                    <div style="font-weight: 500;">📄 Agile_Framework.json</div>
                                    <div style="font-size: 12px; color: #6b7280;">850 KB • Processing... • 0 vectors</div>
                                </div>
                                <div style="display: flex; gap: 8px;">
                                    <div style="padding: 4px 8px; font-size: 12px; background: #f59e0b; color: white; border-radius: 4px;">Processing</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="screen">
                    <div class="screen-header">
                        <div class="screen-dots">
                            <div class="dot red"></div>
                            <div class="dot yellow"></div>
                            <div class="dot green"></div>
                        </div>
                        <div class="screen-title">User Management</div>
                    </div>
                    <div class="screen-content">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                            <h4>User Management</h4>
                            <button class="btn btn-primary" style="padding: 8px 16px; font-size: 12px;">+ Add User</button>
                        </div>
                        <div style="display: flex; flex-direction: column; gap: 12px;">
                            <div class="file-item">
                                <div>
                                    <div style="font-weight: 500;">John Smith</div>
                                    <div style="font-size: 12px; color: #6b7280;"><EMAIL> • Regular User • Last active: 2h ago</div>
                                </div>
                                <div style="display: flex; gap: 8px;">
                                    <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">View Activity</button>
                                    <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">Edit</button>
                                </div>
                            </div>
                            <div class="file-item">
                                <div>
                                    <div style="font-weight: 500;">Sarah Wilson</div>
                                    <div style="font-size: 12px; color: #6b7280;"><EMAIL> • Admin User • Last active: 5m ago</div>
                                </div>
                                <div style="display: flex; gap: 8px;">
                                    <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">View Activity</button>
                                    <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">Edit</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="screen">
                    <div class="screen-header">
                        <div class="screen-dots">
                            <div class="dot red"></div>
                            <div class="dot yellow"></div>
                            <div class="dot green"></div>
                        </div>
                        <div class="screen-title">Analytics Dashboard</div>
                    </div>
                    <div class="screen-content">
                        <h4 style="margin-bottom: 20px;">Analytics Dashboard</h4>
                        <div class="admin-stats">
                            <div class="stat-card">
                                <div class="stat-number">1,247</div>
                                <div class="stat-label">Active Users</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">85%</div>
                                <div class="stat-label">User Satisfaction</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">2.3s</div>
                                <div class="stat-label">Avg Response Time</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">99.9%</div>
                                <div class="stat-label">System Health</div>
                            </div>
                        </div>
                        <div style="margin-top: 20px;">
                            <h5 style="margin-bottom: 12px;">Analytics Options</h5>
                            <div class="dashboard-nav">
                                <div class="nav-card">
                                    <div class="nav-icon" style="background: #FF9800;">📈</div>
                                    <h4>Usage Stats</h4>
                                </div>
                                <div class="nav-card">
                                    <div class="nav-icon" style="background: #FF9800;">⚡</div>
                                    <h4>Model Performance</h4>
                                </div>
                                <div class="nav-card">
                                    <div class="nav-icon" style="background: #FF9800;">😊</div>
                                    <h4>User Satisfaction</h4>
                                </div>
                                <div class="nav-card">
                                    <div class="nav-icon" style="background: #FF9800;">🛠️</div>
                                    <h4>System Health</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="screen">
                    <div class="screen-header">
                        <div class="screen-dots">
                            <div class="dot red"></div>
                            <div class="dot yellow"></div>
                            <div class="dot green"></div>
                        </div>
                        <div class="screen-title">System Configuration</div>
                    </div>
                    <div class="screen-content">
                        <h4 style="margin-bottom: 20px;">System Configuration</h4>
                        <div style="display: flex; flex-direction: column; gap: 20px;">
                            <div class="form-group">
                                <label class="form-label">AI Model Settings</label>
                                <select class="form-input">
                                    <option>Default Model</option>
                                    <option>High Performance</option>
                                    <option>Low Latency</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">RAG Configuration</label>
                                <input type="text" class="form-input" value="Default Vector Size: 1536">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Security Settings</label>
                                <div style="display: flex; gap: 16px; margin-top: 8px;">
                                    <label style="display: flex; align-items: center; gap: 8px; font-size: 14px;">
                                        <input type="checkbox" checked> Two-Factor Auth
                                    </label>
                                    <label style="display: flex; align-items: center; gap: 8px; font-size: 14px;">
                                        <input type="checkbox"> IP Whitelisting
                                    </label>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Backup Settings</label>
                                <select class="form-input">
                                    <option>Daily Backup</option>
                                    <option>Weekly Backup</option>
                                    <option>Monthly Backup</option>
                                </select>
                            </div>
                            <button class="btn btn-primary">Save Configuration</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>