import 'package:flutter/material.dart';
import '../../viewmodels/admin_dashboard_viewmodel.dart';
import '../../../core/constants/app_colors.dart';

/// User management dialog widget following Single Responsibility Principle
/// Handles only user management UI logic
class UserManagementDialog extends StatefulWidget {
  final AdminDashboardViewModel adminViewModel;
  
  const UserManagementDialog({super.key, required this.adminViewModel});

  static Future<void> show(BuildContext context, AdminDashboardViewModel adminViewModel) {
    return showDialog(
      context: context,
      builder: (context) => UserManagementDialog(adminViewModel: adminViewModel),
    );
  }

  @override
  _UserManagementDialogState createState() => _UserManagementDialogState();
}

class _UserManagementDialogState extends State<UserManagementDialog> {
  List<dynamic> users = [];
  bool isLoading = true;
  String? error;

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    try {
      final result = await widget.adminViewModel.getAllUsers();
      
      if (result['success'] == true) {
        setState(() {
          users = result['users'] ?? [];
          isLoading = false;
        });
      } else {
        setState(() {
          error = result['message'] ?? 'Failed to load users';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        error = 'Error loading users: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.people, color: AppColors.primaryTeal),
          const SizedBox(width: 12),
          const Text('User Management'),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        height: 300, // Fixed height to prevent overflow
        child: isLoading
            ? const Center(child: CircularProgressIndicator())
            : error != null
                ? Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error_outline, color: AppColors.error, size: 48),
                      const SizedBox(height: 16),
                      Text(error!, textAlign: TextAlign.center),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            isLoading = true;
                            error = null;
                          });
                          _loadUsers();
                        },
                        child: const Text('Retry'),
                      ),
                    ],
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Total Users: ${users.length}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 16),
                      Expanded(
                        child: ListView.builder(
                          itemCount: users.length,
                          itemBuilder: (context, index) {
                            final user = users[index];
                            return Card(
                              child: ListTile(
                                leading: CircleAvatar(
                                  child: Text(
                                    (user['full_name'] ?? 'U').toString().substring(0, 1).toUpperCase(),
                                  ),
                                ),
                                title: Text(user['full_name'] ?? 'Unknown'),
                                subtitle: Text(user['email'] ?? 'No email'),
                                trailing: Chip(
                                  label: Text(
                                    user['role'] ?? 'user',
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                  backgroundColor: user['role'] == 'admin' 
                                      ? AppColors.primaryTeal.withOpacity(0.2)
                                      : AppColors.textSecondary.withOpacity(0.2),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Close'),
        ),
        if (!isLoading && error == null)
          TextButton(
            onPressed: _loadUsers,
            child: const Text('Refresh'),
          ),
      ],
    );
  }
}
