# PowerShell script for Docker development commands
# PM Assistant Development Helper

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("start", "stop", "restart", "logs", "shell", "test", "clean", "build")]
    [string]$Command,
    
    [string]$Service = "pm-assistant-dev"
)

$ErrorActionPreference = "Stop"

function Write-Info {
    param([string]$Message)
    Write-Host "🐳 $Message" -ForegroundColor Cyan
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

switch ($Command) {
    "start" {
        Write-Info "Starting development environment..."
        docker-compose -f docker-compose.dev.yml --env-file .env.dev up -d
        Write-Success "Development environment started!"
        Write-Info "API available at: http://localhost:8000"
        Write-Info "API docs at: http://localhost:8000/docs"
        Write-Info "Debug port: 5678"
    }
    
    "stop" {
        Write-Info "Stopping development environment..."
        docker-compose -f docker-compose.dev.yml down
        Write-Success "Development environment stopped!"
    }
    
    "restart" {
        Write-Info "Restarting development environment..."
        docker-compose -f docker-compose.dev.yml restart $Service
        Write-Success "Service $Service restarted!"
    }
    
    "logs" {
        Write-Info "Showing logs for $Service..."
        docker-compose -f docker-compose.dev.yml logs -f $Service
    }
    
    "shell" {
        Write-Info "Opening shell in $Service..."
        docker-compose -f docker-compose.dev.yml exec $Service /bin/bash
    }
    
    "test" {
        Write-Info "Running tests in development environment..."
        docker-compose -f docker-compose.dev.yml exec $Service python -m pytest business_logic_layer/tests/ -v
    }
    
    "clean" {
        Write-Info "Cleaning up Docker resources..."
        docker-compose -f docker-compose.dev.yml down -v
        docker system prune -f
        Write-Success "Cleanup completed!"
    }
    
    "build" {
        Write-Info "Building development images..."
        docker-compose -f docker-compose.dev.yml build --no-cache
        Write-Success "Build completed!"
    }
}
