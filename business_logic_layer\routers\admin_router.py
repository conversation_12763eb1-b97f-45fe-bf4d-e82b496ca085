"""
Admin Router
===========
Handles all admin-related endpoints
"""

from fastapi import APIRouter, HTTPException, Depends, status
from typing import Dict, Any, List

from models.user_models import User, UserSettings
from models.chat_models import AnalyticsData
from services.auth_service import AuthService
from services.session_service import SessionService

router = APIRouter(prefix="/admin", tags=["admin"])
auth_service = AuthService()
session_service = SessionService()


def require_admin(current_user: Dict[str, Any] = Depends(auth_service.get_current_user)) -> Dict[str, Any]:
    """Dependency to require admin role"""
    if current_user.get("role") != "admin":
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Admin access required")
    return current_user


@router.get("/users")
async def get_all_users(admin_user: Dict[str, Any] = Depends(require_admin)) -> Dict[str, Any]:
    """Get all users (admin only)"""
    try:
        users = await auth_service.get_all_users()
        return {
            "success": True,
            "users": users,
            "total": len(users)
        }
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/sessions")
async def get_all_sessions(admin_user: Dict[str, Any] = Depends(require_admin)) -> List[Dict[str, Any]]:
    """Get all chat sessions (admin only)"""
    try:
        sessions = await session_service.get_all_sessions()
        return sessions
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/analytics")
async def get_analytics(admin_user: Dict[str, Any] = Depends(require_admin)) -> Dict[str, Any]:
    """Get system analytics (admin only)"""
    try:
        analytics = await session_service.get_analytics_data()
        return {
            "success": True,
            "data": analytics
        }
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/stats")
async def get_admin_stats(admin_user: Dict[str, Any] = Depends(require_admin)) -> Dict[str, Any]:
    """Get admin dashboard statistics"""
    try:
        # Get analytics data from session service
        analytics = await session_service.get_analytics_data()
        user_count = await auth_service.get_user_count()
        active_sessions = await session_service.get_active_session_count()
        
        return {
            "success": True,
            "stats": {
                "total_users": user_count,
                "total_conversations": analytics.get("total_sessions", 0),
                "uptime_percentage": 98.5,  # Placeholder
                "active_sessions": active_sessions,
                "total_messages": analytics.get("total_messages", 0)
            }
        }
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/system-health")
async def get_system_health(admin_user: Dict[str, Any] = Depends(require_admin)) -> Dict[str, Any]:
    """Get system health status (admin only)"""
    try:
        health_data = {
            "status": "healthy",
            "uptime": "system_uptime_placeholder",
            "memory_usage": "memory_usage_placeholder",
            "active_sessions": await session_service.get_active_session_count(),
            "total_users": await auth_service.get_user_count(),
        }
        return {
            "success": True,
            "data": health_data
        }
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
