import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';
import 'suggestion_chips.dart';

/// Chat empty state widget following Single Responsibility Principle
/// Handles only empty state display UI logic
class ChatEmptyState extends StatelessWidget {
  final Function(String) onSuggestionTap;

  const ChatEmptyState({
    super.key,
    required this.onSuggestionTap,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Assistant Image
          Container(
            height: 150,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.asset(
                'assets/images/assistant.png',
                width: 200,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 200,
                    height: 150,
                    decoration: BoxDecoration(
                      color: AppColors.primaryTeal.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(40),
                    ),
                    child: Icon(
                      Icons.assistant,
                      size: 60,
                      color: AppColors.primaryTeal,
                    ),
                  );
                },
              ),
            ),
          ),
          SizedBox(height: 24),
          Text(
            'Start a conversation',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: 12),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 40),
            child: Text(
              'Ask me anything about project management, agile practices, or team coordination',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
                height: 1.5,
              ),
            ),
          ),
          SizedBox(height: 24),
          SuggestionChips(
            onSuggestionTap: onSuggestionTap,
          ),
        ],
      ),
    );
  }
}
