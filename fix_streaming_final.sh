#!/bin/bash

# Final Fix for Streaming - Use Working Method
echo "🚀 Final Fix for Streaming - Use Working Method"
echo "==============================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Final streaming fix applied:"
echo ""
echo "🔧 Changes made:"
echo "   ✅ Updated stream_ai_response to use working generate_ai_response method"
echo "   ✅ Stream response word-by-word for real-time effect"
echo "   ✅ Proper chunk format with content"
echo "   ✅ Error handling with fallback"
echo "   ✅ Flutter timeout increased to 60 seconds"
echo ""

print_status "Rebuilding backend with final streaming fix..."

# Stop current container
docker-compose -f docker-compose.vps.yml stop pm-assistant

# Rebuild with no cache
print_status "Rebuilding backend..."
docker-compose -f docker-compose.vps.yml build --no-cache pm-assistant

# Start container
print_status "Starting fixed container..."
docker-compose -f docker-compose.vps.yml up -d pm-assistant

# Wait for startup
print_status "Waiting for startup..."
sleep 30

# Check health
print_status "Checking health..."
for i in {1..10}; do
    if curl -f http://localhost:8000/health &>/dev/null; then
        print_success "✅ Service is healthy!"
        break
    else
        print_status "Waiting for service... (attempt $i/10)"
        sleep 5
    fi
    
    if [ $i -eq 10 ]; then
        print_error "❌ Health check failed"
        docker-compose -f docker-compose.vps.yml logs --tail=20 pm-assistant
        exit 1
    fi
done

# Get fresh token
print_status "Getting fresh authentication token..."
TOKEN_RESPONSE=$(curl -s -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "admin123"}')

TOKEN=$(echo $TOKEN_RESPONSE | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
    print_error "❌ Failed to get authentication token"
    exit 1
fi

print_success "✅ Got fresh token"

# Test both endpoints
print_status "Testing both endpoints..."
echo ""

# Test /chat/send (known working)
print_status "1. Testing /chat/send (known working)..."
send_response=$(timeout 60s curl -s -X POST "http://localhost:8000/chat/send" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"message": "test"}')

if echo "$send_response" | grep -q "response"; then
    print_success "✅ /chat/send works"
    echo "Response length: $(echo "$send_response" | wc -c) chars"
else
    print_error "❌ /chat/send failed"
    echo "Response: $send_response"
fi

echo ""

# Test /chat/stream (fixed)
print_status "2. Testing /chat/stream (fixed)..."
stream_response=$(timeout 60s curl -s -X POST "http://localhost:8000/chat/stream" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"message": "what is agile?"}' | head -20)

echo "Stream response:"
echo "$stream_response"
echo ""

# Count chunks with actual content
content_chunks=$(echo "$stream_response" | grep -o '"chunk":"[^"]\+' | grep -v '"chunk":""' | wc -l)
print_status "Chunks with content: $content_chunks"

if [ "$content_chunks" -gt 0 ]; then
    print_success "✅ STREAMING IS NOW WORKING!"
    echo ""
    echo "Sample content chunks:"
    echo "$stream_response" | grep '"chunk":"[^"]\+' | grep -v '"chunk":""' | head -3
    echo ""
    print_success "🎉 Flutter app should now receive streaming responses!"
else
    print_warning "⚠️ Still getting empty chunks"
    
    # Check logs for errors
    print_status "Checking logs for errors..."
    docker logs pm-assistant-vps | grep -E "(ERROR|Exception|Traceback)" | tail -5
fi

echo ""
print_status "Checking streaming logs..."
docker logs pm-assistant-vps | grep -E "(🚀|✅|🏁)" | tail -10

echo ""
if [ "$content_chunks" -gt 0 ]; then
    print_success "🎉 FINAL STREAMING FIX SUCCESSFUL!"
    echo ""
    echo "✅ Backend streaming endpoint works"
    echo "✅ Flutter app has increased timeout (60s)"
    echo "✅ Proper chunk format implemented"
    echo "✅ Real-time word-by-word streaming"
    echo ""
    echo "📱 Flutter app should now work perfectly!"
    echo ""
    echo "🧪 Test in Flutter app:"
    echo "   1. Ask any question"
    echo "   2. Should see real-time streaming"
    echo "   3. Should receive complete structured response"
else
    print_error "🚨 STREAMING STILL HAS ISSUES"
    echo ""
    echo "❌ Backend may have model loading issues"
    echo "🔍 Check if model is properly loaded"
    echo "🔍 Verify generate_ai_response method works"
    echo ""
    echo "🧪 Manual debug:"
    echo "   docker logs pm-assistant-vps | grep -i model"
    echo "   docker logs pm-assistant-vps | grep -i error"
fi

echo ""
echo "📋 Access Information:"
echo "   Backend API: http://54.38.33.180:8000"
echo "   Streaming: http://54.38.33.180:8000/chat/stream"
echo "   Send: http://54.38.33.180:8000/chat/send"
echo "   Health: http://54.38.33.180:8000/health"
