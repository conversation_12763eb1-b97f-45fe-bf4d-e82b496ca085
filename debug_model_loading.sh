#!/bin/bash

# Debug Model Loading Issues
echo "🔍 Debugging Model Loading Issues"
echo "================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Checking model loading issues..."

# Check if MessageService is being imported
print_status "1. Checking MessageService import..."
docker exec pm-assistant-vps python -c "
try:
    from services.message_service import MessageService
    print('✅ MessageService import successful')
except Exception as e:
    print(f'❌ MessageService import failed: {e}')
"

# Check model file access from inside container
print_status "2. Checking model file access..."
docker exec pm-assistant-vps python -c "
import os
model_path = '/app/business_logic_layer/models/qwen2.5-7b-pm-assistant.q4_k_m.gguf'
if os.path.exists(model_path):
    size = os.path.getsize(model_path) / (1024**3)
    print(f'✅ Model file exists: {size:.2f} GB')
else:
    print('❌ Model file not accessible')
"

# Check llama-cpp-python installation
print_status "3. Checking llama-cpp-python..."
docker exec pm-assistant-vps python -c "
try:
    from llama_cpp import Llama
    print('✅ llama-cpp-python import successful')
except Exception as e:
    print(f'❌ llama-cpp-python import failed: {e}')
"

# Try to manually initialize MessageService
print_status "4. Testing MessageService initialization..."
docker exec pm-assistant-vps python -c "
import sys
sys.path.append('/app/business_logic_layer')
try:
    from services.message_service import MessageService
    print('Creating MessageService instance...')
    service = MessageService()
    print(f'✅ MessageService created, model loaded: {service.model is not None}')
except Exception as e:
    print(f'❌ MessageService initialization failed: {e}')
    import traceback
    traceback.print_exc()
"

# Check server startup logs for any hidden errors
print_status "5. Checking for hidden startup errors..."
docker logs pm-assistant-vps 2>&1 | grep -i "traceback\|exception\|error" | head -10

# Force a chat request to trigger model loading
print_status "6. Testing chat request to trigger model loading..."
response=$(curl -s -X POST http://localhost:8000/chat \
    -H "Content-Type: application/json" \
    -d '{"message": "test"}')

echo "Chat response: $response"

# Check logs after chat request
print_status "7. Checking logs after chat request..."
docker logs pm-assistant-vps | tail -20

# Check health endpoint for model status
print_status "8. Checking health endpoint..."
health=$(curl -s http://localhost:8000/health)
echo "Health response: $health"

echo ""
print_status "Debug complete. If model is not loading:"
echo "1. Check if MessageService import works"
echo "2. Check if llama-cpp-python is properly installed"
echo "3. Check if model file is accessible"
echo "4. Look for any hidden exceptions in logs"
