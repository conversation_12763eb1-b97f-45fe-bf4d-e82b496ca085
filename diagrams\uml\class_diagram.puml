@startuml ProjectManagementChatbot_ClassDiagram

!theme plain
skinparam classAttributeIconSize 0
skinparam backgroundColor #FFFFFF
skinparam class {
    BackgroundColor #E6F3FF
    BorderColor #0066CC
    ArrowColor #0066CC
}

package "User Interface" {
    class ChatInterface {
        +askQuestion()
        +receiveResponse()
        +viewHistory()
    }
}

package "Core System" {
    class ChatbotEngine {
        +processUserQuery()
        +generateResponse()
        +manageConversation()
    }
    
    class ProjectKnowledgeBase {
        +searchAnswers()
        +getProjectAdvice()
        +findRelevantInfo()
    }
    
    class UserSession {
        +trackConversation()
        +maintainContext()
        +storeHistory()
    }
}

package "Data & Content" {
    class QADatabase {
        +storeQuestions()
        +storeAnswers()
        +categorizeContent()
    }
    
    class ProjectManagementContent {
        +planningGuidance()
        +riskManagement()
        +timeManagement()
        +stakeholderAdvice()
        +costControl()
    }
}

package "Business Categories" {
    enum PMKnowledgeAreas {
        PROJECT_PLANNING
        RISK_MANAGEMENT
        TIME_MANAGEMENT
        STAKEHOLDER_MANAGEMENT
        COST_MANAGEMENT
        GENERAL_PM
    }
}

' Key Business Relationships
ChatInterface --> ChatbotEngine : "User asks questions"
ChatbotEngine --> ProjectKnowledgeBase : "Searches for answers"
ChatbotEngine --> UserSession : "Maintains context"
ProjectKnowledgeBase --> QADatabase : "Retrieves content"
QADatabase --> ProjectManagementContent : "Contains PM knowledge"
ProjectManagementContent --> PMKnowledgeAreas : "Organized by domain"

' Business Notes
note top of ChatbotEngine : "Main system that understands\nquestions and provides\nproject management advice"

note right of ProjectKnowledgeBase : "Contains all project\nmanagement expertise\nand best practices"

note bottom of ProjectManagementContent : "Expert knowledge covering\nall key PM domains"

@enduml
