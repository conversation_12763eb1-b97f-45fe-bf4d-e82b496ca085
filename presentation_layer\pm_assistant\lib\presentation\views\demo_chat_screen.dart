import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../widgets/common/yonnovia_header.dart';
import '../widgets/common/yonnovia_text_field.dart';
import '../widgets/chat/demo_empty_state.dart';
import '../widgets/chat/demo_message.dart';

class DemoChatScreen extends StatefulWidget {
  final Map<String, String>? conversation;
  
  const DemoChatScreen({super.key, this.conversation});

  @override
  _DemoChatScreenState createState() => _DemoChatScreenState();
}

class _DemoChatScreenState extends State<DemoChatScreen> {
  final TextEditingController _controller = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _hasText = false;
  
  List<Map<String, dynamic>> _demoMessages = [];

  @override
  void initState() {
    super.initState();
    _controller.addListener(() {
      setState(() {
        _hasText = _controller.text.isNotEmpty;
      });
    });
    _loadDemoMessages();
  }

  @override
  void dispose() {
    _controller.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _loadDemoMessages() {
    // Load demo messages based on conversation type
    final title = widget.conversation?['title'] ?? 'Demo Conversation';
    
    if (title.contains('Mobile App')) {
      _demoMessages = [
        {
          'text': 'Hello! I need help with our mobile app timeline. We\'re behind schedule.',
          'isUser': true,
          'timestamp': '2:30 PM'
        },
        {
          'text': 'I understand your concern about the mobile app timeline. Let me help you analyze the current situation and create an actionable plan. Can you tell me what specific areas are causing delays?',
          'isUser': false,
          'timestamp': '2:31 PM'
        },
        {
          'text': 'The main issues are with the UI design reviews and backend API development. Both teams seem to be working in silos.',
          'isUser': true,
          'timestamp': '2:33 PM'
        },
        {
          'text': 'I see the issue. This is a common challenge in mobile development. Here\'s what I recommend:\n\n1. **Sync Sessions**: Schedule daily 15-minute sync meetings between UI and backend teams\n2. **API-First Design**: Have backend team create API documentation before UI team builds interfaces\n3. **Parallel Development**: Use mock APIs so UI development can continue while backend is being built\n\nWould you like me to help you create a revised timeline with these improvements?',
          'isUser': false,
          'timestamp': '2:34 PM'
        },
        {
          'text': 'Yes, that would be great! How do we implement the API-first approach?',
          'isUser': true,
          'timestamp': '2:36 PM'
        },
      ];
    } else if (title.contains('Agile')) {
      _demoMessages = [
        {
          'text': 'What are the best practices for implementing Agile in a traditional organization?',
          'isUser': true,
          'timestamp': '1:15 PM'
        },
        {
          'text': 'Great question! Transitioning to Agile in a traditional organization requires a thoughtful approach. Here are the key best practices:\n\n🎯 **Start Small**\n- Begin with pilot projects\n- Choose teams willing to embrace change\n\n📋 **Focus on Core Principles**\n- Daily standups\n- Sprint planning\n- Retrospectives\n\n🤝 **Leadership Support**\n- Get buy-in from management\n- Provide Agile training\n\nWhich area would you like to focus on first?',
          'isUser': false,
          'timestamp': '1:16 PM'
        },
        {
          'text': 'We should start with sprint planning. Our current planning process takes weeks.',
          'isUser': true,
          'timestamp': '1:18 PM'
        },
        {
          'text': 'Excellent choice! Sprint planning is often the most impactful place to start. Here\'s a streamlined approach:\n\n⏰ **Time-boxed Planning** (2-4 hours max)\n1. Sprint Goal Definition (30 min)\n2. Backlog Refinement (60 min)\n3. Task Estimation (60 min)\n4. Commitment & Questions (30 min)\n\n📊 **Key Tools**\n- Story points for estimation\n- Definition of Done criteria\n- Sprint backlog board\n\nWould you like me to help you design your first sprint planning session?',
          'isUser': false,
          'timestamp': '1:19 PM'
        },
      ];
    } else if (title.contains('Risk Management')) {
      _demoMessages = [
        {
          'text': 'How do I create a comprehensive risk management framework for our software project?',
          'isUser': true,
          'timestamp': '10:30 AM'
        },
        {
          'text': 'Creating a robust risk management framework is crucial for project success. Let me guide you through a comprehensive approach:\n\n🎯 **Risk Identification Framework**\n\n1. **Technical Risks**\n   - Technology limitations\n   - Integration challenges\n   - Performance issues\n\n2. **Resource Risks**\n   - Team availability\n   - Skill gaps\n   - Budget constraints\n\n3. **External Risks**\n   - Vendor dependencies\n   - Regulatory changes\n   - Market conditions\n\nWhich category concerns you most?',
          'isUser': false,
          'timestamp': '10:31 AM'
        },
        {
          'text': 'I\'m most concerned about technical risks, especially integration challenges with third-party APIs.',
          'isUser': true,
          'timestamp': '10:33 AM'
        },
        {
          'text': 'Smart to focus on technical integration risks! Here\'s a targeted framework for API integration risks:\n\n⚠️ **Risk Assessment Matrix**\n\n**High Impact Risks:**\n- API deprecation (Mitigation: Version monitoring)\n- Rate limiting (Mitigation: Caching strategy)\n- Authentication changes (Mitigation: OAuth 2.0)\n\n**Medium Impact Risks:**\n- Response time degradation\n- Data format changes\n- Service downtime\n\n🛡️ **Mitigation Strategies:**\n1. Circuit breaker pattern\n2. Fallback mechanisms\n3. Regular health checks\n4. SLA monitoring\n\nWould you like me to help you create a specific action plan for any of these risks?',
          'isUser': false,
          'timestamp': '10:34 AM'
        },
      ];
    } else {
      // Default demo messages
      _demoMessages = [
        {
          'text': 'Hello! Welcome to the PM Assistant demo.',
          'isUser': false,
          'timestamp': 'Now'
        },
        {
          'text': 'This is a demonstration of how conversations look in our chat interface.',
          'isUser': false,
          'timestamp': 'Now'
        },
        {
          'text': 'You can type messages and see how they appear in the chat.',
          'isUser': false,
          'timestamp': 'Now'
        },
      ];
    }
    
    setState(() {});
    
    // Auto-scroll to bottom
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final conversationTitle = widget.conversation?['title'] ?? 'Demo Chat';
    final messageCount = widget.conversation?['messages'] ?? '${_demoMessages.length} messages';
    
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Chat Header
            YonnoviaHeader(
              title: conversationTitle,
              subtitle: messageCount,
              onBackPressed: () => Navigator.pop(context),
              actions: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.primaryTeal.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AppColors.primaryTeal.withOpacity(0.4),
                    ),
                  ),
                  child: Text(
                    'DEMO',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SizedBox(width: 8),
                IconButton(
                  icon: Icon(Icons.refresh, color: Colors.white),
                  onPressed: () {
                    setState(() {
                      _loadDemoMessages();
                    });
                  },
                ),
              ],
            ),
            
            // Chat Messages
            Expanded(
              child: Container(
                color: AppColors.background,
                child: _demoMessages.isEmpty ? DemoEmptyState() : ListView.builder(
                  controller: _scrollController,
                  padding: EdgeInsets.all(20),
                  itemCount: _demoMessages.length,
                  itemBuilder: (context, index) {
                    final message = _demoMessages[index];
                    return DemoMessage(
                      text: message['text'],
                      isUser: message['isUser'],
                      timestamp: message['timestamp'],
                    );
                  },
                ),
              ),
            ),
            
            // Chat Input
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.surface,
                border: Border(
                  top: BorderSide(color: AppColors.borderLight),
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.shadowLight,
                    blurRadius: 8,
                    offset: Offset(0, -2),
                  ),
                ],
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: Container(
                      constraints: BoxConstraints(maxHeight: 120),
                      child: YonnoviaTextField(
                        controller: _controller,
                        hintText: 'Try asking about project management...',
                        maxLines: null,
                      ),
                    ),
                  ),
                  SizedBox(width: 12),
                  // Send Button
                  Container(
                    height: 48,
                    width: 48,
                    child: Material(
                      color: _hasText 
                          ? AppColors.primaryTeal 
                          : AppColors.gray400,
                      borderRadius: BorderRadius.circular(24),
                      elevation: 2,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(24),
                        onTap: _hasText ? () => _sendMessage() : null,
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24),
                            gradient: _hasText 
                                ? AppColors.primaryGradient
                                : null,
                          ),
                          child: Icon(
                            Icons.send,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _sendMessage() {
    if (_controller.text.isNotEmpty) {
      final userMessage = _controller.text;
      setState(() {
        _demoMessages.add({
          'text': userMessage,
          'isUser': true,
          'timestamp': 'Now'
        });
      });
      _controller.clear();
      
      // Simulate AI response after a short delay
      Future.delayed(Duration(milliseconds: 1500), () {
        setState(() {
          _demoMessages.add({
            'text': _generateDemoResponse(userMessage),
            'isUser': false,
            'timestamp': 'Now'
          });
        });
        
        // Auto-scroll to bottom
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
      
      // Auto-scroll to bottom immediately for user message
      Future.delayed(Duration(milliseconds: 100), () {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    }
  }

  String _generateDemoResponse(String userMessage) {
    final message = userMessage.toLowerCase();
    
    if (message.contains('sprint') || message.contains('agile')) {
      return 'Great question about Agile! Sprint planning is crucial for project success. I recommend starting with 2-week sprints and focusing on clear user stories. Would you like me to help you structure your next sprint?';
    } else if (message.contains('risk') || message.contains('problem')) {
      return 'Risk management is essential for any project. I suggest creating a risk register with probability and impact assessments. Common risks include scope creep, resource constraints, and technical challenges. What specific risks are you concerned about?';
    } else if (message.contains('team') || message.contains('communication')) {
      return 'Team communication is the foundation of successful project management. Consider implementing daily standups, regular retrospectives, and clear communication channels. Tools like Slack or Microsoft Teams can help maintain transparency.';
    } else if (message.contains('budget') || message.contains('cost')) {
      return 'Budget management requires careful planning and monitoring. I recommend using earned value management techniques and tracking actual vs. planned costs weekly. Would you like help setting up a budget tracking system?';
    } else if (message.contains('timeline') || message.contains('schedule')) {
      return 'Timeline management is critical for project delivery. Use techniques like critical path analysis and buffer time for unexpected delays. Consider breaking down large tasks into smaller, manageable chunks. What\'s your current timeline challenge?';
    } else {
      return 'Thanks for your question! As your PM Assistant, I\'m here to help with project management challenges including planning, risk management, team coordination, and delivery optimization. Feel free to ask about any specific project management topic!';
    }
  }
}
