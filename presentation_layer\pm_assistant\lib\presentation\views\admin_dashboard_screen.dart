import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../viewmodels/auth_viewmodel.dart';
import '../viewmodels/admin_dashboard_viewmodel.dart';
import '../../core/constants/app_colors.dart';
import '../widgets/common/yonnovia_header.dart';
import '../widgets/common/yonnovia_card.dart';
import '../widgets/common/yonnovia_button.dart';
import '../widgets/common/logout_dialog.dart';
import '../widgets/admin/stat_card.dart';
import '../widgets/admin/management_card.dart';
import '../widgets/admin/system_health_dialog.dart';
import '../widgets/admin/knowledge_base_dialog.dart';
import '../widgets/admin/user_management_dialog.dart';
import '../widgets/admin/analytics_dialog.dart';
import '../widgets/admin/system_config_dialog.dart';
import 'chat_screen.dart';

class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  _AdminDashboardScreenState createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  AdminDashboardViewModel? _adminViewModel;

  @override
  void initState() {
    super.initState();
    // AdminViewModel will be initialized in build method when we have access to AuthViewModel
  }

  @override
  void dispose() {
    _adminViewModel?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authViewModel = context.watch<AuthViewModel>();
    final user = authViewModel.user;
    final userName = user?.name ?? 'Admin';
    
    // Initialize AdminDashboardViewModel with the same ApiGateway instance
    if (_adminViewModel == null) {
      _adminViewModel = AdminDashboardViewModel(authViewModel.apiGateway);
      // Load dashboard data when screen initializes
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _adminViewModel!.loadDashboardData();
      });
    }
    
    return ChangeNotifierProvider<AdminDashboardViewModel>.value(
      value: _adminViewModel!,
      child: Consumer<AdminDashboardViewModel>(
        builder: (context, adminViewModel, child) {
          return Scaffold(
            backgroundColor: AppColors.background,
            body: SafeArea(
              child: Column(
                children: [
                  // Header
                  YonnoviaHeader(
                    title: 'Admin Dashboard',
                    subtitle: 'System management & analytics',
                    showBackButton: false,
                    actions: [
                      IconButton(
                        icon: adminViewModel.isLoading 
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Icon(Icons.refresh, color: Colors.white),
                        onPressed: adminViewModel.isLoading ? null : () => adminViewModel.refresh(),
                        tooltip: 'Refresh Dashboard',
                      ),
                      IconButton(
                        icon: const Icon(Icons.logout, color: Colors.white),
                        onPressed: () => LogoutDialog.show(context),
                      ),
                    ],
                  ),
                  
                  // Error display
                  if (adminViewModel.error != null)
                    Container(
                      width: double.infinity,
                      margin: const EdgeInsets.all(16),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppColors.error.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: AppColors.error.withOpacity(0.3)),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.error_outline, color: AppColors.error, size: 20),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              adminViewModel.error!,
                              style: TextStyle(color: AppColors.error, fontSize: 14),
                            ),
                          ),
                          IconButton(
                            icon: Icon(Icons.close, color: AppColors.error, size: 18),
                            onPressed: () => adminViewModel.clearError(),
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                        ],
                      ),
                    ),
                  
                  // Main content
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        children: [
                          // Welcome Card with Image
                          YonnoviaCard(
                            child: Column(
                              children: [
                                Container(
                                  height: 120,
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(12),
                                    child: Container(
                                      color: AppColors.primaryTeal.withOpacity(0.1),
                                      child: Center(
                                        child: Icon(
                                          Icons.admin_panel_settings,
                                          size: 60,
                                          color: AppColors.primaryTeal,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 20),
                                Text(
                                  'Welcome, $userName',
                                  style: TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.textPrimary,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Manage your PM Assistant system and monitor performance',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          
                          const SizedBox(height: 20),
                          
                          // System Statistics
                          YonnoviaCard(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Icon(Icons.analytics, color: AppColors.primaryTeal, size: 24),
                                    const SizedBox(width: 12),
                                    Text(
                                      'System Overview',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.textPrimary,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 20),
                                GridView.count(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  crossAxisCount: 3,
                                  crossAxisSpacing: 12,
                                  mainAxisSpacing: 12,
                                  childAspectRatio: 1.2,
                                  children: [
                                    StatCard(
                                      number: adminViewModel.isLoading ? '...' : adminViewModel.totalUsers.toString(),
                                      label: 'Total Users',
                                      icon: Icons.people,
                                    ),
                                    StatCard(
                                      number: adminViewModel.isLoading ? '...' : adminViewModel.totalConversations.toString(),
                                      label: 'Conversations',
                                      icon: Icons.chat,
                                    ),
                                    StatCard(
                                      number: adminViewModel.isLoading ? '...' : '${adminViewModel.uptimePercentage.toStringAsFixed(1)}%',
                                      label: 'Uptime',
                                      icon: Icons.check_circle,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          
                          const SizedBox(height: 20),
                          
                          // Management Tools
                          YonnoviaCard(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Icon(Icons.build, color: AppColors.primaryTeal, size: 24),
                                    const SizedBox(width: 12),
                                    Text(
                                      'Management Tools',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.textPrimary,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 20),
                                GridView.count(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  crossAxisCount: 2,
                                  crossAxisSpacing: 12,
                                  mainAxisSpacing: 12,
                                  childAspectRatio: 1.5,
                                  children: [
                                    ManagementCard(
                                      title: 'Chat System',
                                      subtitle: 'Manage conversations',
                                      icon: Icons.chat,
                                      color: AppColors.primaryTeal,
                                      onTap: () => Navigator.push(
                                        context,
                                        MaterialPageRoute(builder: (_) => const ChatScreen()),
                                      ),
                                    ),
                                    ManagementCard(
                                      title: 'Knowledge Base',
                                      subtitle: 'Upload documents',
                                      icon: Icons.library_books,
                                      color: AppColors.secondaryGreen,
                                      onTap: () => KnowledgeBaseDialog.show(context),
                                    ),
                                    ManagementCard(
                                      title: 'User Management',
                                      subtitle: 'Manage users',
                                      icon: Icons.people,
                                      color: AppColors.info,
                                      onTap: () => UserManagementDialog.show(context, _adminViewModel!),
                                    ),
                                    ManagementCard(
                                      title: 'System Config',
                                      subtitle: 'Configure settings',
                                      icon: Icons.settings,
                                      color: AppColors.warning,
                                      onTap: () => SystemConfigDialog.show(context),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          
                          const SizedBox(height: 20),
                          
                          // Quick Actions
                          Row(
                            children: [
                              Expanded(
                                child: YonnoviaButton(
                                  text: 'View Analytics',
                                  onPressed: () => AnalyticsDialog.show(context, _adminViewModel!),
                                  icon: Icons.analytics,
                                  isSecondary: true,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: YonnoviaButton(
                                  text: 'System Health',
                                  onPressed: () => SystemHealthDialog.show(context, _adminViewModel!),
                                  icon: Icons.health_and_safety,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
