#!/bin/bash

# Fix Model Response Issues
echo "🔧 Fixing Model Response Issues"
echo "==============================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Applying fixes for model response issues..."
echo ""
echo "🔧 Fixes applied:"
echo "   ✅ Increased max_tokens to 400"
echo "   ✅ Raised temperature to 0.3 for better generation"
echo "   ✅ Increased top_p to 0.8 for variety"
echo "   ✅ Reduced repeat_penalty to 1.3"
echo "   ✅ Relaxed stopping conditions"
echo "   ✅ Added debug logging"
echo ""

print_status "Restarting PM Assistant with fixes..."

# Check which docker compose file exists
if [ -f "docker-compose.vps.yml" ]; then
    COMPOSE_FILE="docker-compose.vps.yml"
    CONTAINER_NAME="pm-assistant"
elif [ -f "docker-compose.dev.yml" ]; then
    COMPOSE_FILE="docker-compose.dev.yml"
    CONTAINER_NAME="pm-assistant-dev"
else
    print_error "No docker-compose file found!"
    exit 1
fi

print_status "Using $COMPOSE_FILE..."

# Restart the container
docker-compose -f $COMPOSE_FILE restart $CONTAINER_NAME

print_status "Waiting for restart..."
sleep 20

# Check health
print_status "Checking health..."
for i in {1..10}; do
    if curl -f http://localhost:8000/health &>/dev/null; then
        print_success "✅ Service is healthy!"
        break
    else
        print_status "Waiting for service... (attempt $i/10)"
        sleep 3
    fi
    
    if [ $i -eq 10 ]; then
        print_error "❌ Health check failed"
        print_status "Checking logs..."
        docker-compose -f $COMPOSE_FILE logs --tail=20 $CONTAINER_NAME
        exit 1
    fi
done

# Test response generation
print_status "Testing response generation..."
echo ""
echo "🧪 Test 1: Simple question"
response=$(curl -s -X POST http://localhost:8000/chat \
    -H "Content-Type: application/json" \
    -d '{"message": "What is project management?"}')

if echo "$response" | grep -q "Thinking"; then
    print_success "✅ Model is generating structured responses!"
else
    print_warning "⚠️ Response may still have issues"
    echo "Response preview: $(echo "$response" | head -c 200)..."
fi

echo ""
echo "🧪 Test 2: Streaming test"
stream_response=$(timeout 15s curl -s -X POST http://localhost:8000/chat/stream \
    -H "Content-Type: application/json" \
    -d '{"message": "Explain agile methodology"}' | head -10)

if echo "$stream_response" | grep -q "data:"; then
    print_success "✅ Streaming is working!"
    
    # Count non-empty chunks
    non_empty_chunks=$(echo "$stream_response" | grep -c '"content":"[^"]\+')
    print_status "Non-empty chunks received: $non_empty_chunks"
    
    if [ "$non_empty_chunks" -gt 0 ]; then
        print_success "✅ Model is generating content!"
    else
        print_warning "⚠️ Still receiving empty chunks"
    fi
else
    print_warning "⚠️ Streaming test inconclusive"
fi

echo ""
print_status "Checking recent logs for debug information..."
docker-compose -f $COMPOSE_FILE logs --tail=30 $CONTAINER_NAME | grep -E "(🔍|🛑|ERROR|WARNING)" | tail -10

echo ""
print_success "🎉 Fix deployment completed!"
echo ""
echo "📋 What was fixed:"
echo "   • Relaxed generation parameters for better output"
echo "   • Reduced aggressive stopping conditions"
echo "   • Added debug logging to track issues"
echo "   • Increased token limits for complete responses"
echo ""
echo "🔧 If issues persist:"
echo "   1. Check logs: docker-compose -f $COMPOSE_FILE logs -f $CONTAINER_NAME"
echo "   2. Look for debug messages starting with 🔍"
echo "   3. Monitor stopping conditions with 🛑"
echo "   4. Verify model file is properly loaded"
echo ""
echo "🧪 Test commands:"
echo "   curl -X POST http://localhost:8000/chat -H 'Content-Type: application/json' -d '{\"message\": \"test\"}'"
echo "   curl -X POST http://localhost:8000/chat/stream -H 'Content-Type: application/json' -d '{\"message\": \"test\"}'"
