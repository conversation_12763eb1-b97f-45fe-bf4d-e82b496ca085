"""
Initialize FAISS Vector Store with Sample PM Documents
=====================================================
Creates initial FAISS vector store with project management documents.
"""

import os
import sys
import logging
import pickle
import gzip
import numpy as np

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import faiss
    from sentence_transformers import SentenceTransformer
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False
    print("❌ FAISS or sentence-transformers not available")

# Sample PM documents
SAMPLE_DOCUMENTS = [
    {
        "title": "Project Management Fundamentals",
        "content": """
Project management is the application of knowledge, skills, tools, and techniques to project activities to meet project requirements. A project is a temporary endeavor undertaken to create a unique product, service, or result.

Key Project Management Processes:
1. Initiating: Defining and authorizing the project
2. Planning: Establishing the scope and refining objectives
3. Executing: Coordinating people and resources
4. Monitoring and Controlling: Tracking progress and performance
5. Closing: Finalizing all activities

Project Constraints (Triple Constraint):
- Scope: What work will be done
- Time: When the project will be finished
- Cost: How much the project will cost

Project Manager Responsibilities:
- Define project scope and objectives
- Develop project plans and schedules
- Manage project resources and budget
- Communicate with stakeholders
- Monitor and control project progress
- Manage risks and issues
- Ensure quality deliverables
        """,
        "category": "fundamentals",
        "metadata": {"source": "PMBOK Guide", "pages": "1-25"}
    },
    {
        "title": "Agile Project Management",
        "content": """
Agile project management is an iterative approach to delivering a project throughout its life cycle. Agile projects are characterized by many incremental deliveries.

Agile Principles:
1. Individuals and interactions over processes and tools
2. Working software over comprehensive documentation
3. Customer collaboration over contract negotiation
4. Responding to change over following a plan

Scrum Framework:
- Sprint: Time-boxed iteration (1-4 weeks)
- Sprint Planning: Define work for upcoming sprint
- Daily Scrum: 15-minute daily synchronization
- Sprint Review: Demonstrate completed work
- Sprint Retrospective: Reflect and improve

Scrum Roles:
- Product Owner: Defines product vision and priorities
- Scrum Master: Facilitates process and removes impediments
- Development Team: Delivers the product increment

Agile Artifacts:
- Product Backlog: Prioritized list of features
- Sprint Backlog: Work selected for current sprint
- Product Increment: Potentially shippable product
        """,
        "category": "methodologies",
        "metadata": {"source": "Agile Manifesto", "pages": "1-15"}
    },
    {
        "title": "Risk Management in Projects",
        "content": """
Project risk management includes the processes of conducting risk management planning, identification, analysis, response planning, response implementation, and monitoring risk on a project.

Risk Management Process:
1. Plan Risk Management: Define how to conduct risk activities
2. Identify Risks: Determine which risks may affect the project
3. Perform Qualitative Risk Analysis: Prioritize risks for further analysis
4. Perform Quantitative Risk Analysis: Numerically analyze risk effects
5. Plan Risk Responses: Develop options to enhance opportunities and reduce threats
6. Implement Risk Responses: Execute agreed-upon risk response plans
7. Monitor Risks: Track identified risks and identify new risks

Risk Response Strategies:
For Threats:
- Avoid: Eliminate the threat
- Transfer: Shift impact to third party
- Mitigate: Reduce probability or impact
- Accept: Acknowledge risk without action

For Opportunities:
- Exploit: Ensure opportunity occurs
- Share: Allocate ownership to third party
- Enhance: Increase probability or impact
- Accept: Take advantage if it occurs

Risk Register Components:
- Risk ID and description
- Risk category and source
- Probability and impact assessment
- Risk score and ranking
- Response strategy and actions
- Risk owner and status
        """,
        "category": "risk-management",
        "metadata": {"source": "PMBOK Guide", "pages": "397-459"}
    },
    {
        "title": "Stakeholder Management",
        "content": """
Project stakeholder management includes the processes required to identify the people, groups, or organizations that could impact or be impacted by the project.

Stakeholder Identification:
- Project sponsor and customer
- Project team members
- Functional managers
- End users and beneficiaries
- Suppliers and vendors
- Government agencies
- Community groups

Stakeholder Analysis:
1. Identify all stakeholders
2. Analyze stakeholder interests and expectations
3. Assess stakeholder influence and impact
4. Classify stakeholders by power and interest
5. Develop stakeholder engagement strategies

Stakeholder Engagement Levels:
- Unaware: Unaware of project and potential impacts
- Resistant: Aware but resistant to change
- Neutral: Aware but neither supportive nor resistant
- Supportive: Aware and supportive of project
- Leading: Aware and actively engaged in project success

Communication Methods:
- Interactive: Real-time multidirectional exchange
- Push: Sent to specific recipients
- Pull: Recipients access content at their discretion

Stakeholder Register:
- Identification information
- Assessment information
- Stakeholder classification
- Engagement strategy
        """,
        "category": "stakeholder-management",
        "metadata": {"source": "PMBOK Guide", "pages": "503-530"}
    },
    {
        "title": "Quality Management in Projects",
        "content": """
Project quality management includes the processes for incorporating the organization's quality policy regarding planning, managing, and controlling project and product quality requirements.

Quality Management Processes:
1. Plan Quality Management: Identify quality requirements and standards
2. Manage Quality: Translate quality management plan into executable activities
3. Control Quality: Monitor and record quality activities results

Quality vs Grade:
- Quality: Degree to which characteristics fulfill requirements
- Grade: Category assigned to products with same functional use

Cost of Quality:
- Prevention Costs: Training, equipment, time to do it right
- Appraisal Costs: Testing, inspections, reviews
- Internal Failure Costs: Rework, scrap before delivery
- External Failure Costs: Liabilities, warranty work, lost business

Quality Tools and Techniques:
- Cause and Effect Diagrams (Fishbone)
- Control Charts
- Flowcharts
- Histograms
- Pareto Charts
- Scatter Diagrams
- Statistical Sampling
- Inspection and Reviews

Quality Metrics:
- Defect density
- Failure rate
- Availability
- Reliability
- Test coverage
- Customer satisfaction
        """,
        "category": "quality-management",
        "metadata": {"source": "PMBOK Guide", "pages": "271-311"}
    }
]

def init_vector_store():
    """Initialize vector store with sample documents"""
    logging.basicConfig(level=logging.INFO)
    
    try:
        # Initialize RAG service
        rag_service = RAGService()
        
        # Add sample documents
        for doc in SAMPLE_DOCUMENTS:
            success = rag_service.add_document(
                content=doc["content"],
                source=f"{doc['category']}/{doc['title']}",
                metadata={
                    "title": doc["title"],
                    "category": doc["category"],
                    "description": f"Sample document about {doc['title']}",
                    "content_type": "text/plain",
                    "uploaded_by": "system",
                    **doc["metadata"]
                }
            )
            
            if success:
                print(f"✅ Added document: {doc['title']}")
            else:
                print(f"❌ Failed to add document: {doc['title']}")
        
        # Print statistics
        stats = rag_service.get_vector_store_stats()
        print(f"\n📊 Vector Store Statistics:")
        print(f"   Total Documents: {stats['total_documents']}")
        print(f"   Total Chunks: {stats['total_chunks']}")
        print(f"   Vector Store Size: {stats['vector_store_size_mb']:.2f} MB")
        print(f"   Embedding Model Loaded: {stats['embedding_model_loaded']}")
        
        print("\n🎉 Vector store initialized successfully!")
        
    except Exception as e:
        print(f"❌ Error initializing vector store: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    init_vector_store()
