# Development Dockerfile for PM Assistant
# Optimized for development with hot reload and debugging capabilities

FROM python:3.10-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    libopenblas-dev \
    git \
    curl \
    vim \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create development user
RUN groupadd -r devuser && useradd -r -g devuser -s /bin/bash devuser

# Set working directory
WORKDIR /app

# Install Python dependencies
COPY business_logic_layer/requirements.txt /tmp/requirements.txt
RUN pip install --no-cache-dir --upgrade pip setuptools wheel
RUN pip install --no-cache-dir -r /tmp/requirements.txt

# Install development dependencies
RUN pip install --no-cache-dir \
    llama-cpp-python==0.2.85 \
    debugpy \
    ipython \
    jupyter \
    pytest-cov \
    black \
    isort \
    flake8 \
    mypy

# Create necessary directories
RUN mkdir -p /app/business_logic_layer/Docs /app/business_logic_layer/models \
    && chown -R devuser:devuser /app

# Switch to development user
USER devuser

# Set environment variables for development
ENV PYTHONPATH=/app/business_logic_layer
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV DEVELOPMENT=1
ENV LOG_LEVEL=DEBUG

# Expose ports (8000 for app, 5678 for debugger)
EXPOSE 8000 5678

# Default command with reload for development
CMD ["python", "-m", "uvicorn", "business_logic_layer.server_clean:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
