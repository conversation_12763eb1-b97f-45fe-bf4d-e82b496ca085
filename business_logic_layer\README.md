# Business Logic Layer - PM Assistant Backend

## 🎯 Overview

The Business Logic Layer implements **Domain-Driven Design (DDD)** principles with clean separation of concerns, providing PM consultation through well-structured, maintainable, and scalable domain services.

## 🏗️ Domain-Driven Architecture

This layer follows **Domain-Driven Design** patterns with clear domain boundaries:

### Domain Architecture Components
- **Domain Models**: Core business entities and value objects
- **Domain Services**: Business logic implementation
- **Application Routers**: FastAPI route handlers and controllers
- **Infrastructure Services**: External integrations and data persistence

### Domain-Driven Principles Applied
- ✅ **Domain Modeling**: Rich domain models with business logic
- ✅ **Bounded Contexts**: Clear domain boundaries and responsibilities
- ✅ **Domain Services**: Complex business operations
- ✅ **Repository Pattern**: Data access abstraction
- ✅ **Dependency Injection**: Loose coupling between components

### Core Domain Services
- **Authentication Service**: User management, role-based access control, JWT token handling, and security controls
- **Session Service**: Conversation management and history tracking
- **Document Service**: File processing, PDF extraction, and content indexing
- **Message Service**: Message processing and response generation
- **API Chat Integration Service**: External AI service integration and orchestration

### Features
- **Role-Based Access**: Support for admin and regular user roles, with role selection during registration
- **Admin Dashboard**: Comprehensive admin interface with real-time analytics, user management, and system health monitoring
- **Performance Analytics**: Average response time tracking (1.9s), user satisfaction metrics, and system performance indicators
- **System Health Monitoring**: Real-time status monitoring of database, API server, authentication, and chat systems
- **Document Processing**: Support for PDF, TXT, and other document formats
- **Session Management**: Persistent chat sessions with message history and analytics
- **Testing Support**: In-memory data storage for easy testing and development

## 📁 Domain-Driven Project Structure

```
business_logic_layer/
├── server.py                    # Main FastAPI application
├── main.py                      # Alternative entry point
├── minimal_business_server.py   # Lightweight testing server
├── models/                      # Domain models and entities
│   ├── user_models.py           # User domain models
│   ├── chat_models.py           # Chat and conversation models
│   ├── analytics_models.py      # Analytics and metrics models
│   └── __init__.py              # Models package initialization
├── routers/                     # Application layer - API routes
│   ├── auth_router.py           # Authentication endpoints
│   ├── chat_router.py           # Chat and messaging endpoints
│   ├── admin_router.py          # Administrative endpoints
│   └── __init__.py              # Routers package initialization
├── services/                    # Domain services - Business logic
│   ├── auth_service.py          # Authentication domain service
│   ├── session_service.py       # Session management service
│   ├── message_service.py       # Message processing service
│   ├── document_service.py      # Document processing service
│   ├── api_chat_integration_service.py  # AI integration service
│   └── auth_service_refactored.py       # Refactored auth service
├── config.py                    # Configuration management
├── rag_system.py               # RAG integration service
├── conversation_cache.py       # Caching service
└── requirements.txt            # Dependencies
```

## 🚀 Getting Started

### Prerequisites

- **Python 3.9+**

### Installation

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Start the Domain-Driven Architecture Server**
   ```bash
   # Main domain-driven server
   python server.py
   
   # OR alternative entry point
   python main.py
   
   # OR minimal testing server
   python minimal_business_server.py
   
   # Access API documentation
   http://localhost:8000/docs
   
   # Health check
   http://localhost:8000/health
   ```

## 🔧 Domain-Driven Components

### 1. Application Layer (FastAPI Routers)

**Authentication Router** (`routers/auth_router.py`)
- User registration and login endpoints
- JWT token management
- Role-based access control
- Request/response validation

**Chat Router** (`routers/chat_router.py`)
- Message processing endpoints
- Session management endpoints
- AI integration coordination
- Response formatting

**Admin Router** (`routers/admin_router.py`)
- Administrative endpoints with role-based access control
- User management and analytics
- System health monitoring with real-time metrics
- Performance analytics with response time tracking
- Request/response validation and admin authorization

### 2. Domain Layer (Models & Services)

**Domain Models** (`models/`)
- **User Models** (`user_models.py`): User entities, roles, authentication
- **Chat Models** (`chat_models.py`): Conversation entities, messages, sessions
- **Analytics Models** (`analytics_models.py`): Metrics, performance data

**Domain Services** (`services/`)
- **AuthService** (`auth_service.py`): User authentication business logic with JWT token management and role-based access
- **SessionService** (`session_service.py`): Conversation management with analytics data including response time metrics
- **MessageService** (`message_service.py`): Message processing logic
- **DocumentService** (`document_service.py`): Document processing
- **API Chat Integration Service** (`api_chat_integration_service.py`): AI service coordination

### 3. Infrastructure Layer

**External Integrations**
- AI service integrations
- Database abstractions
- Configuration management
- Caching infrastructure

## 📡 Domain API Endpoints

### Health & Documentation
- `GET /` - Interactive API documentation (Swagger UI)
- `GET /health` - System health check

### Authentication Domain (`/auth`)
- `POST /auth/register` - Register new user with role assignment  
- `POST /auth/login` - Authenticate and receive JWT token
- `GET /auth/me` - Get current user profile and permissions

### Session Management (`/session`)
- `POST /session/create` - Create new chat session
- `GET /session/list` - Get user's chat sessions
- `GET /session/{session_id}` - Get specific session details
- `DELETE /session/{session_id}` - Delete session and history

### Message Processing (`/message`)
- `POST /message/send` - Send message and get AI response
- `GET /message/history/{session_id}` - Get conversation history
- `POST /message/feedback` - Provide feedback on AI responses

### Document Management (`/documents`)
- `POST /documents/upload` - Upload and process documents
- `GET /documents/list` - Get user's document library
- `GET /documents/{doc_id}` - Retrieve specific document
- `DELETE /documents/{doc_id}` - Remove document from system

### Administration (`/admin`)
- `GET /admin/users` - User management and user listing (admin only)  
- `GET /admin/sessions` - System-wide session analytics (admin only)
- `GET /admin/stats` - Admin dashboard statistics (admin only)
- `GET /admin/analytics` - System analytics with performance metrics (admin only)
- `GET /admin/system-health` - System health monitoring (admin only)

## 🧪 Testing Domain-Driven Architecture

```bash
# Run the domain-driven server
python server.py

# Test endpoints with curl
curl http://localhost:8000/health
curl http://localhost:8000/

# Test authentication flow
curl -X POST http://localhost:8000/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test123","full_name":"Test User"}'

curl -X POST http://localhost:8000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test123"}'
```

**Domain-Driven Design Benefits**:
- ✅ **Domain Focus**: Business logic is central and well-modeled
- ✅ **Bounded Contexts**: Clear domain boundaries
- ✅ **Testable**: Domain services can be unit tested independently
- ✅ **Maintainable**: Rich domain models with encapsulated business rules
- ✅ **Scalable**: Domain-based organization supports growth
- ✅ **Extensible**: Easy to add new domain features
- `POST /auth/register` - User registration
- `POST /auth/login` - User authentication
- `GET /health` - Health monitoring

### 2. RAG System (`rag_system.py`)

Advanced Retrieval-Augmented Generation implementation:

#### **Features**:
- **Document Processing**: PDF, text, and structured document support
- **Vector Embeddings**: Sentence-BERT based embeddings
- **FAISS Integration**: High-performance similarity search
- **Multi-language Knowledge**: PM terminology in EN/FR/AR
- **Dynamic Updates**: Real-time document indexing

#### **Knowledge Areas**:
- **Core PM**: PMBOK, PRINCE2, Agile, Scrum, Kanban
- **Processes**: Initiation, Planning, Execution, Monitoring, Closing
- **Tools**: Gantt, PERT, CPM, WBS, RACI, EVM
- **Metrics**: KPI, ROI, NPV, CPI, SPI, ESG metrics

### 3. Conversation Cache (`conversation_cache.py`)

Sophisticated session and conversation management:

#### **Capabilities**:
- **Thread-Safe Storage**: Concurrent conversation handling
- **Session Persistence**: Automatic save/restore
- **Memory Management**: Configurable session limits
- **Clarification Tracking**: Context-aware follow-up handling
- **Metadata Enrichment**: Comprehensive conversation analytics

### 4. Configuration (`config.py`)

Centralized configuration management:

#### **Settings Categories**:
- **API Configuration**: Host, port, CORS settings
- **Model Settings**: Model paths, inference parameters
- **RAG Settings**: Embedding dimensions, chunk sizes
- **Cache Settings**: Session limits, timeouts
- **Logging**: Comprehensive logging configuration

## 🤖 AI Integration

### Model Architecture

```
User Query → API Gateway → Authentication Service → AI Chat Service → RAG Service → Response
              ↓                     ↓                    ↓             ↓
        Rate Limiting    →    Session Service    →   Document Service   →   Context Enrichment
```

**Domain-Driven Communication Flow:**
1. **Application Layer (Routers)**: Routes requests to appropriate domain services
2. **Authentication Service**: Validates user credentials and sessions using domain models
3. **Session Service**: Manages conversation state using chat domain models
4. **Message Service**: Processes queries using domain business logic
5. **RAG Service**: Retrieves relevant knowledge from document domain
6. **Document Service**: Handles file processing using document domain models

### Supported Models
- **Qwen 2.5-7B**: Fine-tuned for project management
- **Custom PM Models**: Specialized PM assistants
- **Multilingual Support**: Cross-language understanding

### Response Quality
- **Context-Aware**: Retrieves relevant PM documents
- **Methodology-Specific**: Applies appropriate PM frameworks
- **Actionable Advice**: Practical, implementable recommendations

## 📊 API Documentation

### Authentication Endpoints

#### Register User
```http
POST /auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "any_password",
  "full_name": "Admin User",
  "role": "admin"
}
```
*Note: Role can be "admin" or "user". Admin role provides access to administrative features.*

#### Login User
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "any_password"
}
```

### Admin Endpoints (Requires Admin Role)

#### Get Admin Statistics
```http
GET /admin/stats
Authorization: Bearer <jwt_token>
```

#### Get System Analytics
```http
GET /admin/analytics
Authorization: Bearer <jwt_token>
```

#### Get System Health
```http
GET /admin/system-health
Authorization: Bearer <jwt_token>
```

#### Get All Users
```http
GET /admin/users
Authorization: Bearer <jwt_token>
```

### Chat Endpoints

#### Send Message
```http
POST /chat
Content-Type: application/json

{
  "message": "How do I create a project timeline?",
  "session_id": "optional_session_id",
  "user_id": "optional_user_id"
}
```

#### Create Session
```http
POST /sessions
Content-Type: application/json

{
  "user_id": "user_identifier",
  "project_context": "optional_context"
}
```

### Document Management

#### Upload Document
```http
POST /upload-document
Content-Type: multipart/form-data

file: [binary_file_data]
document_type: "pm_guide"
```

## 🔍 Response Format

### Standard Chat Response
```json
{
  "response": "AI-generated PM advice",
  "session_id": "session_identifier",
  "language": "detected_language",
  "context_sources": [
    {
      "source": "PMBOK_Guide.pdf",
      "content": "relevant_excerpt",
      "relevance_score": 0.95,
      "page": "page_number"
    }
  ],
  "methodology": "agile",
  "confidence": 0.92
}
```

### Admin Analytics Response
```json
{
  "success": true,
  "data": {
    "total_users": 5,
    "total_sessions": 12,
    "total_messages": 48,
    "active_users_today": 3,
    "avg_response_time": 1.9,
    "user_satisfaction": 85,
    "average_session_length": 4.0,
    "most_active_hours": [9, 10, 11, 14, 15, 16]
  }
}
```

### System Health Response
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "uptime": "system_uptime_placeholder",
    "memory_usage": "memory_usage_placeholder",
    "average_response_time": "1.9s"
  }
}
```

### Error Response
```json
{
  "error": "Error description",
  "error_code": "ERROR_CODE",
  "details": "Additional error details"
}
```

## 🛠️ Development

### Running Tests
```bash
# API endpoint testing
python API_test.py

# Client integration testing
python client_example.py
```

### Adding New Features

1. **New Endpoints**: Add to routers in `routers/` directory
2. **Domain Models**: Add to `models/` directory
3. **Domain Services**: Add to `services/` directory
4. **RAG Enhancement**: Modify `rag_system.py`
5. **Cache Features**: Extend `conversation_cache.py`
6. **Configuration**: Update `config.py`

### Performance Optimization

#### Model Optimization
- **Quantization**: Use Q4_K_M or Q5_K_M models
- **GPU Acceleration**: Enable CUDA for faster inference
- **Batch Processing**: Implement request batching

#### RAG Optimization
- **Index Caching**: Persistent FAISS indices
- **Chunk Size Tuning**: Optimize for your documents
- **Embedding Caching**: Reuse computed embeddings

## 🔒 Security Features

### Authentication
- **Password Hashing**: SHA-256 with salt
- **Session Management**: Secure session tokens
- **Input Validation**: Comprehensive request validation

### Data Protection
- **CORS Configuration**: Configurable origin restrictions
- **Request Limits**: Rate limiting and size restrictions
- **Sanitization**: Input cleaning and validation

## 📈 Monitoring & Logging

### Logging Features
- **Comprehensive Logging**: Request/response tracking
- **Performance Metrics**: Response time monitoring (avg 1.9s)
- **Error Tracking**: Detailed error logging
- **Usage Analytics**: Session and user analytics
- **Admin Monitoring**: System health and performance dashboards

### Health Monitoring
```http
GET /health
```
Returns:
```json
{
  "status": "healthy",
  "service": "PM Assistant API",
  "timestamp": "2025-08-05T10:00:00Z",
  "model_status": "loaded",
  "rag_status": "ready",
  "cache_status": "active"
}
```

### Admin Health Monitoring
```http
GET /admin/system-health
Authorization: Bearer <jwt_token>
```
Returns detailed system health with component status and performance metrics.

## 🌐 Multi-language Support

### Supported Languages
- **English**: Native support with full PM terminology
- **French**: Comprehensive PM terminology in French
- **Arabic**: PM concepts in Arabic

### Language Detection
- **Automatic Detection**: Queries automatically detected
- **Response Language**: Responses in detected language
- **Fallback**: English fallback for unsupported languages

## 📚 Knowledge Base Management

### Document Types
- **PM Guides**: PMBOK, PRINCE2, Agile guides
- **Templates**: Project templates and forms
- **Case Studies**: Real-world PM examples
- **Best Practices**: Industry standards and practices

### Adding Documents
1. Place documents in `Docs/` folder
2. Restart server for automatic indexing
3. Or use upload endpoint for dynamic addition

## 🚀 Deployment

### Production Deployment
```bash
# Install production server
pip install gunicorn

# Run with gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker main:app --bind 0.0.0.0:8000
```

### Docker Deployment
```dockerfile
FROM python:3.9-slim
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . /app
WORKDIR /app
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Environment Variables
```bash
API_HOST=0.0.0.0
API_PORT=8000
MODEL_PATH=/path/to/model.gguf
DOCS_FOLDER=/path/to/docs
```

## 🔧 Troubleshooting

### Common Issues

#### Model Loading Issues
- **Memory**: Ensure 8GB+ RAM available
- **Path**: Verify model file path in config
- **Format**: Ensure GGUF format model

#### RAG Issues
- **Documents**: Check document formats (PDF, TXT)
- **Embeddings**: Verify sentence-transformers installation
- **FAISS**: Ensure FAISS-CPU or FAISS-GPU installed

#### Performance Issues
- **GPU**: Enable GPU acceleration if available
- **Threads**: Adjust n_threads in config
- **Model Size**: Use smaller quantized models

## 📋 Configuration Reference

### Model Settings
```python
MODEL_PATH = "path/to/model.gguf"
LLM_MAX_TOKENS = 800
LLM_TEMPERATURE = 0.7
LLM_N_CTX = 2048
```

### RAG Settings
```python
EMBEDDING_DIM = 384
MAX_DOC_CHUNK_SIZE = 800
CHUNK_OVERLAP = 150
RELEVANCE_THRESHOLD = 0.5
```

### Cache Settings
```python
MAX_SESSIONS = 1000
MAX_MESSAGES_PER_SESSION = 50
SESSION_TIMEOUT_HOURS = 12
```

## 📞 Support

For technical support or questions:
- Check logs in `Docs/pm_rag.log`
- Review configuration in `config.py`
- Test endpoints with `API_test.py`
- Monitor health endpoint

## 🎯 Next Steps

### Planned Enhancements
1. **Domain Event Sourcing**: Implement domain events for better decoupling
2. **CQRS Pattern**: Separate command and query responsibilities
3. **Advanced Domain Models**: Rich domain entities with more business logic
4. **Domain Validation**: Enhanced business rule validation in domain layer
5. **Aggregate Root Pattern**: Implement aggregate boundaries for data consistency
6. **Repository Pattern**: Abstract data access with repository implementations
7. **Domain-Driven Testing**: Domain-focused unit and integration tests
8. **Bounded Context Integration**: Better integration between domain contexts

---

**Business Logic Layer Status**: ✅ **Production Ready**
**Domain-Driven Design**: ✅ **Fully Implemented**
**Multi-language Support**: ✅ **EN/FR/AR**
**RAG System**: ✅ **Document-Enhanced**
