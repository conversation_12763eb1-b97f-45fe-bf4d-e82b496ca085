import json
import sys
import os
import tempfile
import logging
import hashlib
from typing import Any, Dict, List, Optional, Generator
from dataclasses import dataclass
from datetime import datetime
from langdetect import detect, LangDetectException
from llama_cpp import <PERSON>lama
import uuid
import re
from fastapi import FastAPI, HTTPException, UploadFile, File
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
import shutil
from conversation_cache import EnhancedConversationCache
from rag_system import ProjectManagementRAGSystem

# Configure logging
logging.basicConfig(
    filename="business_logic_layer/Docs/pm_rag.log",
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

@dataclass
class ContextSource:
    source: str
    content: str
    relevance_score: float
    page: Optional[str] = None
    section: Optional[str] = None
    document_type: Optional[str] = None

class ProfessionalPMAssistant:
    def __init__(self):
        # Fixed path with raw string to avoid escape sequence warning
        self.model_path = "business_logic_layer/models/qwen2.5-7b-pm-assistant.q4_k_m.gguf"
        self.current_session_id = None
        self.current_language = "en"
        self.rag_system = ProjectManagementRAGSystem(docs_folder="business_logic_layer/Docs")
        self.conversation_cache = EnhancedConversationCache()
        self._setup_multilingual_responses()
        self._load_specialized_model()
        self.system_prompt = """You are an expert project management assistant with comprehensive knowledge of project management methodologies (e.g., PMBOK, PRINCE2, Agile) and best practices. For each query, autonomously identify and apply relevant methodologies based on the query and provided context.

For project management queries, output ONLY the following format:

**Thinking**

2. Analyze the query: <Summarize the query>
3. Review context: <Summarize relevant context from provided sources or general knowledge>
4. Explain components: <Key components, steps, or concepts>
5. Describe benefits: <Benefits or outcomes of the solution>

**Answer**

1- <Step 1 or key aspect>: <Explanation>
2- <Step 2 or key aspect>: <Explanation>
3- <Step 3 or key aspect>: <Explanation>
4- <Step 4 or key aspect>: <Explanation>

**Source**

[Source 1, Pages: X-Y, Section: Z]
[Source 2, Pages: X-Y, Section: Z]
[Source 3, Pages: X-Y, Section: Z]
[Source 4, Pages: X-Y, Section: Z]

Formatting Rules:
1. Each section (**Thinking**, **Answer**, **Source**) must be separated by exactly one blank line.
2. In **Thinking** section: Each numbered point (2, 3, 4, 5) must end with a line break.
3. In **Answer** section: Use numbered steps (1-, 2-, etc.) for each point, with a blank line after each step.
4. In **Source** section: Each source must be on a separate line in square brackets [].
5. Use clear, professional language suitable for business environments.
6. Respond in the same language as the query (English, French, or Arabic).
7. If a query is not strictly about project management, still provide a helpful answer by framing it from a PM perspective (do not refuse or say out of scope).
8. For clarification requests (e.g., "I didn't understand" or "explain more"), provide a simplified explanation of the last non-clarification query:
   - For the first clarification (count=1), use intermediate language with examples and simplified terms.
   - For the second or more clarifications (count≥2), use beginner-friendly language with step-by-step breakdowns, real-world examples, and definitions for technical terms.
   - Include a polite acknowledgment (e.g., "Thank you for requesting clarification. Let me explain further...").
   - If no prior query exists, prompt the user to provide a specific question."""

    def _setup_multilingual_responses(self):
        self.responses = {
            "greetings": {
                "en": "Hello! How can I assist you with project management today?",
                "fr": "Bonjour ! Comment puis-je vous aider avec la gestion de projet aujourd'hui ?",
                "ar": "مرحبًا! كيف يمكنني مساعدتك في إدارة المشاريع اليوم؟"
            },
            "out_of_scope": {
                "en": "I'll help from a project management angle. Here is a PM-oriented answer to your question.",
                "fr": "Je vais répondre sous l'angle de la gestion de projet. Voici une réponse orientée GP à votre question.",
                "ar": "سأساعد من منظور إدارة المشاريع. إليك إجابة موجهة لإدارة المشاريع على سؤالك."
            },
            "clarification_request": {
                "en": "Thank you for requesting clarification. Please provide the original project management question or ask a new one for a detailed explanation.",
                "fr": "Merci de demander une clarification. Veuillez fournir la question initiale de gestion de projet ou poser une nouvelle question pour une explication détaillée.",
                "ar": "شكرًا لطلب التوضيح. يرجى تقديم السؤال الأصلي حول إدارة المشاريع أو طرح سؤال جديد للحصول على شرح مفصل."
            }
        }

    def _detect_language(self, text: str) -> str:
        try:
            detected = detect(text)
            if detected in ["en", "fr", "ar"]:
                return detected
        except LangDetectException:
            pass
        if any('\u0600' <= char <= '\u06FF' for char in text):
            return "ar"
        return "en"

    def _is_pm_related_query(self, query: str, language: str) -> bool:
        pm_keywords = {
            "en": [
                # Core PM terms
                "project", "management", "planning", "execution", "monitoring", "controlling", "closing", "initiation",
                "team", "cost", "budget", "expense", "financials", "schedule", "timeline", "gantt", "milestone",
                "due date", "deadline", "scope", "wbs", "work breakdown", "deliverable", "requirements", "charter",

                # Risk & Quality
                "risk", "threat", "opportunity", "mitigation", "contingency", "quality", "standards", "assurance",
                "control", "metrics", "kpi", "performance", "baseline", "variance", "tolerance",

                # Stakeholders & Communication
                "stakeholder", "sponsor", "client", "customer", "user", "communication", "report", "meeting",
                "presentation", "documentation", "feedback", "approval", "sign-off", "governance", "oversight",

                # Methodologies & Frameworks
                "agile", "scrum", "sprint", "kanban", "backlog", "iteration", "safe", "scaled agile", "lean",
                "waterfall", "prince2", "pmbok", "pmi", "pmp", "capm", "acp", "psm", "csm", "devops",

                # Resources & Organization
                "resource", "personnel", "staff", "equipment", "materials", "procurement", "vendor", "supplier",
                "contract", "sow", "statement of work", "raci", "responsibility", "accountability", "matrix",

                # Financial & Value
                "evm", "earned value", "cpi", "spi", "cost performance", "schedule performance", "roi", "npv",
                "payback", "benefit", "value", "investment", "funding", "cash flow", "burn rate",

                # Process & Integration
                "integration", "coordination", "dependency", "critical path", "pert", "cpm", "float", "slack",
                "change", "modification", "change request", "configuration", "version", "release",

                # Leadership & Soft Skills
                "leadership", "motivation", "morale", "conflict", "resolution", "negotiation", "facilitation",
                "team building", "coaching", "mentoring", "delegation", "empowerment", "culture",

                # Issues & Problems
                "issue", "problem", "impediment", "blocker", "escalation", "troubleshooting", "root cause",
                "corrective action", "preventive action", "lessons learned", "retrospective", "post-mortem",

                # Portfolio & Program
                "portfolio", "program", "multi-project", "pmo", "office", "governance", "strategy", "alignment",
                "prioritization", "roadmap", "vision", "mission", "objectives", "goals", "success criteria",

                # Tools & Techniques
                "dashboard", "reporting", "tracking", "monitoring", "status", "progress", "update", "forecast",
                "estimation", "sizing", "effort", "duration", "capacity", "utilization", "allocation"
            ],
            "fr": [
                # Termes PM de base
                "projet", "gestion", "planification", "exécution", "surveillance", "contrôle", "clôture", "initiation",
                "équipe", "coût", "budget", "dépense", "finances", "calendrier", "échéancier", "gantt", "jalon",
                "échéance", "date limite", "portée", "sot", "structure découpage", "livrable", "exigences", "charte",

                # Risque & Qualité
                "risque", "menace", "opportunité", "atténuation", "contingence", "qualité", "normes", "assurance",
                "contrôle", "métriques", "indicateur", "performance", "référence", "écart", "tolérance",

                # Parties prenantes & Communication
                "partie prenante", "commanditaire", "client", "utilisateur", "communication", "rapport", "réunion",
                "présentation", "documentation", "retour", "approbation", "validation", "gouvernance", "supervision",

                # Méthodologies & Cadres
                "agile", "scrum", "sprint", "kanban", "backlog", "itération", "safe", "agile à échelle", "lean",
                "cascade", "prince2", "pmbok", "pmi", "pmp", "capm", "acp", "psm", "csm", "devops",

                # Ressources & Organisation
                "ressource", "personnel", "équipement", "matériel", "approvisionnement", "fournisseur",
                "contrat", "énoncé travaux", "raci", "responsabilité", "imputabilité", "matrice",

                # Financier & Valeur
                "valeur acquise", "cpi", "spi", "performance coût", "performance calendrier", "roi", "van",
                "retour investissement", "bénéfice", "valeur", "investissement", "financement", "flux trésorerie",

                # Processus & Intégration
                "intégration", "coordination", "dépendance", "chemin critique", "pert", "cpm", "marge",
                "changement", "modification", "demande changement", "configuration", "version", "livraison",

                # Leadership & Compétences
                "leadership", "motivation", "moral", "conflit", "résolution", "négociation", "facilitation",
                "renforcement équipe", "coaching", "mentorat", "délégation", "autonomisation", "culture",

                # Problèmes & Questions
                "problème", "obstacle", "bloqueur", "escalade", "dépannage", "cause racine",
                "action corrective", "action préventive", "leçons apprises", "rétrospective", "post-mortem",

                # Portefeuille & Programme
                "portefeuille", "programme", "multi-projet", "bureau projet", "gouvernance", "stratégie", "alignement",
                "priorisation", "feuille route", "vision", "mission", "objectifs", "buts", "critères succès"
            ],
            "ar": [
                # مصطلحات إدارة المشاريع الأساسية
                "مشروع", "إدارة", "تخطيط", "تنفيذ", "مراقبة", "تحكم", "إغلاق", "بدء",
                "فريق", "تكلفة", "ميزانية", "نفقة", "مالية", "جدولة", "جدول زمني", "غانت", "معلم",
                "موعد نهائي", "نطاق", "هيكل تجزئة العمل", "مخرجات", "متطلبات", "ميثاق",

                # المخاطر والجودة
                "مخاطر", "تهديد", "فرصة", "تخفيف", "طوارئ", "جودة", "معايير", "ضمان",
                "تحكم", "مقاييس", "مؤشر أداء", "أداء", "خط أساس", "انحراف", "تسامح",

                # أصحاب المصلحة والتواصل
                "أصحاب المصلحة", "راعي", "عميل", "زبون", "مستخدم", "تواصل", "تقرير", "اجتماع",
                "عرض", "توثيق", "تغذية راجعة", "موافقة", "تصديق", "حوكمة", "إشراف",

                # المنهجيات والأطر
                "رشيق", "سكروم", "سباق", "كانبان", "قائمة أعمال", "تكرار", "سيف", "رشيق متدرج", "نحيف",
                "شلال", "برينس2", "دليل المعرفة", "معهد إدارة المشاريع", "محترف إدارة مشاريع",

                # الموارد والتنظيم
                "مورد", "أفراد", "معدات", "مواد", "تزويد", "مورد", "مقاول",
                "عقد", "بيان عمل", "راسي", "مسؤولية", "مساءلة", "مصفوفة",

                # المالية والقيمة
                "قيمة مكتسبة", "مؤشر أداء التكلفة", "مؤشر أداء الجدول", "عائد استثمار", "قيمة حالية صافية",
                "استرداد", "فائدة", "قيمة", "استثمار", "تمويل", "تدفق نقدي",

                # العملية والتكامل
                "تكامل", "تنسيق", "تبعية", "مسار حرج", "بيرت", "طريقة المسار الحرج", "هامش",
                "تغيير", "تعديل", "طلب تغيير", "تكوين", "إصدار", "تسليم",

                # القيادة والمهارات
                "قيادة", "تحفيز", "معنويات", "نزاع", "حل", "تفاوض", "تسهيل",
                "بناء فريق", "تدريب", "إرشاد", "تفويض", "تمكين", "ثقافة",

                # المشاكل والقضايا
                "مشكلة", "عائق", "حاجز", "تصعيد", "استكشاف أخطاء", "سبب جذري",
                "إجراء تصحيحي", "إجراء وقائي", "دروس مستفادة", "مراجعة", "تشريح",

                # المحفظة والبرنامج
                "محفظة", "برنامج", "متعدد المشاريع", "مكتب إدارة مشاريع", "حوكمة", "استراتيجية", "محاذاة",
                "ترتيب أولويات", "خارطة طريق", "رؤية", "مهمة", "أهداف", "غايات", "معايير نجاح"
            ]
        }
        query_lower = query.lower().strip()
        keywords = pm_keywords.get(language, pm_keywords["en"])
        return any(keyword in query_lower for keyword in keywords)

    def _is_clarification_query(self, query: str, language: str) -> bool:
        clarification_phrases = {
            "en": [
                "explain more", "clarify", "elaborate", "tell me more", "more details",
                "i didn't understand", "i dont understand", "i don't understand",
                "simplify", "break it down", "make it simpler", "not clear", "unclear",
                "can you explain", "what do you mean", "i'm confused", "confusing",
                "more information", "expand on", "go deeper", "in detail"
            ],
            "fr": [
                "expliquer plus", "expliquez davantage", "clarifier", "élaborer",
                "dites-moi en plus", "plus de détails", "je n'ai pas compris",
                "je ne comprends pas", "simplifier", "détailler", "rendre plus simple",
                "pas clair", "peu clair", "pouvez-vous expliquer", "que voulez-vous dire",
                "je suis confus", "déroutant", "plus d'informations", "développer"
            ],
            "ar": [
                "اشرح أكثر", "وضح", "فصل", "أخبرني المزيد", "تفاصيل أكثر",
                "لم أفهم", "لا أفهم", "بسط", "افصل ذلك", "اجعله أبسط",
                "غير واضح", "يمكنك أن تشرح", "ماذا تقصد", "أنا مشوش",
                "محير", "المزيد من المعلومات", "توسع في", "بالتفصيل"
            ]
        }
        query_lower = query.lower().strip()
        return any(phrase in query_lower for phrase in clarification_phrases.get(language, []))

    def _load_specialized_model(self):
        logging.info(f"Loading fine-tuned Project Management model from {self.model_path}")
        try:
            self.model = Llama(
                model_path=self.model_path,
                n_ctx=2048,
                n_threads=8,
                n_gpu_layers=-1 if 'cuda' in self.model_path else 0
            )
            logging.info("Fine-tuned PM model loaded successfully")
        except Exception as e:
            logging.error(f"Error loading model: {str(e)}")
            raise

    def _format_response(self, raw_response: str, references: List[str]) -> str:
        lines = raw_response.strip().split('\n')
        formatted_lines = []
        current_section = None
        answer_count = 0

        for line in lines:
            line = line.strip()
            if not line:
                formatted_lines.append("")
                continue

            if line.startswith("**Thinking**"):
                current_section = "thinking"
                formatted_lines.append(line)
            elif line.startswith("**Answer**"):
                current_section = "answer"
                formatted_lines.append("")
                formatted_lines.append(line)
            elif line.startswith("**Source"):
                break
            else:
                if current_section == "thinking":
                    if re.match(r'^\d+\.', line):
                        formatted_lines.append(line)
                        formatted_lines.append("")
                    else:
                        formatted_lines.append(line)
                elif current_section == "answer":
                    if line.startswith('-') or re.match(r'^\d+-', line):
                        answer_count += 1
                        if line.startswith('-'):
                            line = f"{answer_count}- {line[2:].strip()}"
                        formatted_lines.append(line)
                        formatted_lines.append("")
                    else:
                        formatted_lines.append(line)
                else:
                    formatted_lines.append(line)

        formatted_lines.append("")
        formatted_lines.append("**Source**")
        for ref in references:
            formatted_lines.append(f"[{ref}]")

        return '\n'.join(formatted_lines)

    def start_new_session(self, user_id: str = None, preferred_language: str = "en") -> str:
        self.current_session_id = self.conversation_cache.create_session(user_id, preferred_language)
        self.current_language = preferred_language
        return self.current_session_id

    def query(self, user_input: str, session_id: str = None, user_id: str = None) -> Dict[str, Any]:
        if session_id:
            self.current_session_id = session_id
        if not self.current_session_id:
            self.start_new_session(user_id, self.current_language)

        language = self._detect_language(user_input)
        self.current_language = language
        self.conversation_cache.add_message(self.current_session_id, "user", user_input, {"language": language})

        greetings = ["hello", "hi", "hey", "bonjour", "salut", "مرحبا", "أهلا"]
        if any(greeting in user_input.lower() for greeting in greetings):
            response = self.responses["greetings"][language]
            self.conversation_cache.add_message(
                self.current_session_id, "assistant", response,
                {"topic": "greeting", "language": language, "sources_count": 0}
            )
            return {
                "response": response,
                "session_id": self.current_session_id,
                "language": language,
                "sources_count": 0
            }

        is_clarification = self._is_clarification_query(user_input, language)
        clarification_count = self.conversation_cache.get_clarification_count(self.current_session_id)

        if is_clarification:
            original_user_query = self.conversation_cache.get_last_user_query(self.current_session_id, exclude_clarifications=True)
            if not original_user_query:
                response = self.responses["clarification_request"][language]
                self.conversation_cache.add_message(
                    self.current_session_id, "assistant", response,
                    {"topic": "clarification_no_prev_msg", "language": language, "sources_count": 0}
                )
                return {
                    "response": response,
                    "session_id": self.current_session_id,
                    "language": language,
                    "sources_count": 0
                }

            # Temporarily disabled RAG system
            context_sources = [] if self.rag_system is None else self.rag_system.retrieve_context(original_user_query, language, top_k=6)
            prompt_query = original_user_query
            acknowledgment = self.responses["clarification_request"][language].split('.')[0] + "."

            if clarification_count >= 2:
                instruction = (
                    f"{acknowledgment} Provide an extremely simple and detailed explanation of '{original_user_query}'. "
                    f"Use beginner-friendly language, provide step-by-step breakdowns, include real-world examples, "
                    f"and explain all technical terms in simple words. Assume the user has no prior project management knowledge."
                )
            else:
                instruction = (
                    f"{acknowledgment} Provide a more detailed and clearer explanation of '{original_user_query}'. "
                    f"Use simpler terms, include specific examples from the context, and break down complex concepts into intermediate-level explanations."
                )
        else:
            if not self._is_pm_related_query(user_input, language):
                logging.info("ℹ️ Lenient mode: proceeding to answer from PM perspective (no out_of_scope block)")
            # Temporarily disabled RAG system
            context_sources = [] if self.rag_system is None else self.rag_system.retrieve_context(user_input, language, top_k=4)
            prompt_query = user_input
            instruction = (
                f"Answer the project management query: '{user_input}' using relevant methodologies "
                f"and provided context. Ensure professional, clear explanations with proper formatting."
            )

        formatted_context = "No relevant context found." if not context_sources else "\n".join([
            f"Source: {source.source}, Pages: {source.page}, Section: {source.section}\nContent: {source.content}"
            for source in context_sources
        ])

        conversation_history = self.conversation_cache.get_context(self.current_session_id, max_messages=6)

        user_message = f"""**Instruction**: {instruction}

**Query**: {prompt_query}

**Context Sources**:

{formatted_context}

**Conversation History**:

{conversation_history}"""

        prompt = f"<|im_start|>system\n{self.system_prompt}<|im_end|>\n<|im_start|>user\n{user_message}<|im_end|>\n<|im_start|>assistant\n"

        try:
            output = self.model(
                prompt,
                max_tokens=2500 if is_clarification else 1800,
                temperature=0.4 if is_clarification else 0.6,
                top_p=0.9,
                echo=False
            )
            generated_text = output['choices'][0]['text'].strip()
        except Exception as e:
            logging.error(f"Error generating response: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error generating response: {str(e)}")

        references_list = [
            f"{source.source}, Pages: {source.page}, Section: {source.section}"
            for source in context_sources
        ][:4] if context_sources else ["General Project Management Knowledge"]

        formatted_response = self._format_response(generated_text, references_list)

        self.conversation_cache.add_message(
            self.current_session_id, "assistant", formatted_response,
            {
                "topic": "pm_query_clarification" if is_clarification else "pm_query",
                "language": language,
                "sources_count": len(references_list),
                "references": references_list,
                "clarification_count": clarification_count + 1 if is_clarification else 0,
                "is_clarification": is_clarification
            }
        )

        return {
            "response": formatted_response,
            "session_id": self.current_session_id,
            "language": language,
            "sources_count": len(references_list)
        }

    def stream_query(self, user_input: str, session_id: str = None, user_id: str = None) -> Generator[Dict[str, str], None, None]:
        if session_id:
            self.current_session_id = session_id
        if not self.current_session_id:
            self.start_new_session(user_id, self.current_language)

        language = self._detect_language(user_input)
        self.current_language = language
        self.conversation_cache.add_message(self.current_session_id, "user", user_input, {"language": language})

        greetings = ["hello", "hi", "hey", "bonjour", "salut", "مرحبا", "أهلا"]
        if any(greeting in user_input.lower() for greeting in greetings):
            response = self.responses["greetings"][language]
            self.conversation_cache.add_message(
                self.current_session_id, "assistant", response,
                {"topic": "greeting", "language": language, "sources_count": 0}
            )
            yield {"chunk": response + "\n"}
            return

        is_clarification = self._is_clarification_query(user_input, language)
        clarification_count = self.conversation_cache.get_clarification_count(self.current_session_id)

        if is_clarification:
            original_user_query = self.conversation_cache.get_last_user_query(self.current_session_id, exclude_clarifications=True)
            if not original_user_query:
                response = self.responses["clarification_request"][language]
                self.conversation_cache.add_message(
                    self.current_session_id, "assistant", response,
                    {"topic": "clarification_no_prev_msg", "language": language, "sources_count": 0}
                )
                yield {"chunk": response + "\n"}
                return

            # Temporarily disabled RAG system
            context_sources = [] if self.rag_system is None else self.rag_system.retrieve_context(original_user_query, language, top_k=6)
            prompt_query = original_user_query
            acknowledgment = self.responses["clarification_request"][language].split('.')[0] + "."

            if clarification_count >= 2:
                instruction = (
                    f"{acknowledgment} Provide an extremely simple and detailed explanation of '{original_user_query}'. "
                    f"Use beginner-friendly language, provide step-by-step breakdowns, include real-world examples, "
                    f"and explain all technical terms in simple words. Assume the user has no prior project management knowledge."
                )
            else:
                instruction = (
                    f"{acknowledgment} Provide a more detailed and clearer explanation of '{original_user_query}'. "
                    f"Use simpler terms, include specific examples from the context, and break down complex concepts into intermediate-level explanations."
                )
        else:
            if not self._is_pm_related_query(user_input, language):
                logging.info("ℹ️ Lenient streaming mode: continue answering (no out_of_scope)")

            # Temporarily disabled RAG system
            context_sources = [] if self.rag_system is None else self.rag_system.retrieve_context(user_input, language, top_k=4)
            prompt_query = user_input
            instruction = (
                f"Answer the project management query: '{user_input}' using relevant methodologies "
                f"and provided context. Ensure professional, clear explanations with proper formatting."
            )

        yield {"status": "thinking", "message": "Analyzing your PM question..."}

        formatted_context = "No relevant context found." if not context_sources else "\n".join([
            f"Source: {source.source}, Pages: {source.page}, Section: {source.section}\nContent: {source.content}"
            for source in context_sources
        ])

        yield {"status": "processing", "message": f"Found {len(context_sources)} relevant sources. Generating response..."}

        conversation_history = self.conversation_cache.get_context(self.current_session_id, max_messages=6)

        user_message = f"""**Instruction**: {instruction}

**Query**: {prompt_query}

**Context Sources**:

{formatted_context}

**Conversation History**:

{conversation_history}"""

        prompt = f"<|im_start|>system\n{self.system_prompt}<|im_end|>\n<|im_start|>user\n{user_message}<|im_end|>\n<|im_start|>assistant\n"

        try:
            stream = self.model(
                prompt,
                max_tokens=2500 if is_clarification else 1800,
                temperature=0.4 if is_clarification else 0.6,
                top_p=0.9,
                stream=True,
                echo=False
            )

            generated_text_chunks = []
            for chunk in stream:
                text = chunk['choices'][0]['text']
                generated_text_chunks.append(text)
                yield {"chunk": text}

            generated_content = "".join(generated_text_chunks)

            references_list = [
                f"{source.source}, Pages: {source.page}, Section: {source.section}"
                for source in context_sources
            ][:4] if context_sources else ["General Project Management Knowledge"]

            formatted_response = self._format_response(generated_content, references_list)

            self.conversation_cache.add_message(
                self.current_session_id, "assistant", formatted_response,
                {
                    "topic": "pm_query_clarification" if is_clarification else "pm_query",
                    "language": language,
                    "sources_count": len(references_list),
                    "references": references_list,
                    "clarification_count": clarification_count + 1 if is_clarification else 0,
                    "is_clarification": is_clarification
                }
            )

            yield {
                "completed": True,
                "session_id": self.current_session_id,
                "language": language,
                "sources_count": len(references_list)
            }
        except Exception as e:
            logging.error(f"Error streaming response: {str(e)}")
            yield {"chunk": f"Error: {str(e)}\n"}

# Simple User Management System
class UserManager:
    def __init__(self):
        self.users_db = {}  # Simple in-memory store {email: user_data}
        self.user_sessions = {}  # {user_id: session_id}
        
    def _hash_password(self, password: str) -> str:
        """Simple password hashing using SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def register_user(self, email: str, password: str, name: str, role: str = None) -> Dict[str, Any]:
        """Register a new user"""
        if email in self.users_db:
            return {"success": False, "message": "User already exists"}
        
        user_id = str(uuid.uuid4())
        
        # Role is passed from the request (user selection from dropdown)
        role = role if role else "regular"  # default to regular if no role specified
        
        user_data = {
            "user_id": user_id,
            "email": email,
            "name": name,
            "password_hash": self._hash_password(password),
            "role": role,
            "created_at": datetime.now().isoformat()
        }
        
        self.users_db[email] = user_data
        return {
            "success": True, 
            "message": "User registered successfully",
            "user_id": user_id,
            "name": name,
            "email": email,
            "role": role
        }
    
    def login_user(self, email: str, password: str) -> Dict[str, Any]:
        """Authenticate user and return user info"""
        if email not in self.users_db:
            return {"success": False, "message": "User not found"}
        
        user_data = self.users_db[email]
        if user_data["password_hash"] != self._hash_password(password):
            return {"success": False, "message": "Invalid password"}
        
        # Create or get session for user
        user_id = user_data["user_id"]
        if user_id not in self.user_sessions:
            session_id = assistant.start_new_session(user_id)
            self.user_sessions[user_id] = session_id
        else:
            session_id = self.user_sessions[user_id]
        
        return {
            "success": True,
            "message": "Login successful",
            "user_id": user_id,
            "name": user_data["name"],
            "email": email,
            "role": user_data.get("role", "regular"),
            "session_id": session_id
        }
    
    def get_user_session(self, user_id: str) -> Optional[str]:
        """Get user's current session ID"""
        return self.user_sessions.get(user_id)
    
    def get_user_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user data by user_id"""
        for email, user_data in self.users_db.items():
            if user_data["user_id"] == user_id:
                return {
                    "user_id": user_data["user_id"],
                    "email": email,
                    "name": user_data["name"],
                    "role": user_data.get("role", "regular"),
                    "created_at": user_data["created_at"]
                }
        return None

# FastAPI App Setup
app = FastAPI(title="Professional PM Assistant API", version="2.0.0")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
assistant = ProfessionalPMAssistant()
user_manager = UserManager()

# Pydantic Models
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    user_id: Optional[str] = None

class SessionRequest(BaseModel):
    user_id: Optional[str] = None
    preferred_language: str = "en"

class SessionDeleteRequest(BaseModel):
    session_id: str

# Authentication Models
class LoginRequest(BaseModel):
    email: str
    password: str

class RegisterRequest(BaseModel):
    email: str
    password: str
    name: str
    role: Optional[str] = "regular"  # Added role field with default value

class AuthResponse(BaseModel):
    success: bool
    message: str
    user_id: Optional[str] = None
    name: Optional[str] = None
    email: Optional[str] = None
    role: Optional[str] = None  # Added role field
    session_id: Optional[str] = None

class User(BaseModel):
    user_id: str
    email: str
    name: str
    role: str
    created_at: str

# Authentication Endpoints
@app.post("/auth/register", response_model=AuthResponse)
async def register(request: RegisterRequest):
    """Register a new user"""
    try:
        result = user_manager.register_user(request.email, request.password, request.name, request.role)
        if result["success"]:
            # Create a session for the new user
            session_id = assistant.start_new_session(result["user_id"])
            result["session_id"] = session_id
            user_manager.user_sessions[result["user_id"]] = session_id
            
        return AuthResponse(**result)
    except Exception as e:
        logging.error(f"Error during registration: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Registration failed: {str(e)}")

@app.post("/auth/login", response_model=AuthResponse)
async def login(request: LoginRequest):
    """Authenticate user and return session info"""
    try:
        result = user_manager.login_user(request.email, request.password)
        return AuthResponse(**result)
    except Exception as e:
        logging.error(f"Error during login: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Login failed: {str(e)}")

@app.get("/auth/me")
async def get_current_user(user_id: str):
    """Get current user information - Missing endpoint that frontend was calling"""
    try:
        user_data = user_manager.get_user_by_id(user_id)
        if not user_data:
            raise HTTPException(status_code=404, detail="User not found")
        
        session_id = user_manager.get_user_session(user_id)
        
        return {
            "success": True,
            "user_id": user_data["user_id"],
            "email": user_data["email"],
            "name": user_data["name"],
            "role": user_data["role"],
            "session_id": session_id,
            "created_at": user_data["created_at"]
        }
    except Exception as e:
        logging.error(f"Error getting current user: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting user info: {str(e)}")

@app.get("/auth/conversations/{user_id}")
async def get_user_conversations(user_id: str):
    """Get all conversations for a specific user"""
    try:
        session_id = user_manager.get_user_session(user_id)
        if not session_id:
            return {"conversations": [], "message": "No active session found for user"}
        
        conversation = assistant.conversation_cache.get_conversation(session_id)
        if not conversation:
            return {"conversations": [], "message": "No conversation history found"}
        
        # Format conversations to match frontend expectations
        formatted_conversations = []
        for i, msg in enumerate(conversation):
            if msg.get("role") == "user":
                # Look for the assistant's response
                assistant_response = ""
                if i + 1 < len(conversation) and conversation[i + 1].get("role") == "assistant":
                    assistant_response = conversation[i + 1].get("content", "")
                
                formatted_conversations.append({
                    "id": str(i // 2 + 1),
                    "message": msg.get("content", ""),
                    "response": assistant_response,
                    "timestamp": msg.get("timestamp", datetime.now().isoformat())
                })
        
        return {
            "conversations": formatted_conversations,
            "total": len(formatted_conversations),
            "session_id": session_id
        }
    except Exception as e:
        logging.error(f"Error getting user conversations: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving conversations: {str(e)}")

# API Endpoints
@app.post("/sessions")
async def create_session(request: SessionRequest):
    try:
        session_id = assistant.start_new_session(request.user_id, request.preferred_language)
        return {
            "session_id": session_id,
            "status": "success",
            "preferred_language": request.preferred_language,
            "user_id": request.user_id or "anonymous"
        }
    except Exception as e:
        logging.error(f"Error creating session: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating session: {str(e)}")

@app.post("/chat")
async def chat(request: ChatRequest):
    try:
        response = assistant.query(request.message, request.session_id, request.user_id)
        return response
    except Exception as e:
        logging.error(f"Error processing chat request: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing chat request: {str(e)}")

@app.post("/chat/stream")
async def chat_stream(request: ChatRequest):
    def generate():
        try:
            for chunk in assistant.stream_query(request.message, request.session_id, request.user_id):
                yield f"data: {json.dumps(chunk)}\n\n"
        except Exception as e:
            logging.error(f"Error streaming chat response: {str(e)}")
            yield f"data: {{'error': 'Error streaming response: {str(e)}'}}\n\n"
    return StreamingResponse(generate(), media_type="text/event-stream")

@app.post("/admin/upload-document")
async def upload_document(file: UploadFile = File(...)):
    try:
        if not file.filename.lower().endswith('.pdf'):
            logging.warning(f"Invalid file type uploaded: {file.filename}")
            raise HTTPException(status_code=400, detail="Only PDF files are supported")
        
        temp_dir = tempfile.gettempdir()
        temp_file_path = os.path.join(temp_dir, file.filename)
        
        logging.info(f"Saving uploaded file to {temp_file_path}")
        try:
            with open(temp_file_path, "wb") as buffer:
                content = await file.read()
                if not content:
                    logging.error("Uploaded file is empty")
                    raise HTTPException(status_code=400, detail="Uploaded file is empty")
                buffer.write(content)
        except IOError as e:
            logging.error(f"Error writing to temporary file {temp_file_path}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error saving file: {str(e)}")

        logging.info(f"Processing PDF with RAG system: {file.filename}")
        try:
            # Temporarily disabled RAG system
            if assistant.rag_system is None:
                success = False
                logging.warning("RAG system is disabled - document upload not available")
            else:
                success = assistant.rag_system.add_document_to_rag(temp_file_path)
        except Exception as e:
            logging.error(f"Error processing PDF {file.filename}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error processing PDF: {str(e)}")

        if os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
                logging.info(f"Temporary file {temp_file_path} deleted")
            except OSError as e:
                logging.warning(f"Error deleting temporary file {temp_file_path}: {str(e)}")

        if success:
            logging.info(f"Document {file.filename} successfully added to knowledge base")
            return {
                "status": "success",
                "message": f"Document '{file.filename}' successfully uploaded and added to knowledge base",
                "filename": file.filename,
                "total_chunks": len(assistant.rag_system.rag_chunks)
            }
        else:
            logging.error(f"Failed to process document {file.filename} in RAG system")
            raise HTTPException(status_code=500, detail=f"Failed to process document '{file.filename}'")
    except Exception as e:
        if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
                logging.info(f"Temporary file {temp_file_path} deleted after error")
            except OSError as e:
                logging.warning(f"Error deleting temporary file {temp_file_path}: {str(e)}")
        logging.error(f"Error uploading document {file.filename if 'file' in locals() else 'unknown'}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error uploading document: {str(e)}")

@app.get("/admin/documents")
async def list_documents():
    try:
        from collections import defaultdict
        documents = defaultdict(int)
        document_details = []
        if assistant.rag_system.rag_chunks:
            for chunk in assistant.rag_system.rag_chunks:
                source = chunk.metadata.get('source', 'Unknown')
                documents[source] += 1
            seen_sources = set()
            for chunk in assistant.rag_system.rag_chunks:
                source = chunk.metadata.get('source', 'Unknown')
                if source not in seen_sources:
                    document_details.append({
                        "filename": source,
                        "chunks_count": documents[source],
                        "file_type": chunk.metadata.get('file_type', 'PDF'),
                        "loaded_at": chunk.metadata.get('loaded_at', 'Unknown')
                    })
                    seen_sources.add(source)
        logging.info("Retrieved document list")
        return {
            "total_documents": len(document_details),
            "total_chunks": len(assistant.rag_system.rag_chunks) if assistant.rag_system.rag_chunks else 0,
            "documents": document_details
        }
    except Exception as e:
        logging.error(f"Error listing documents: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error listing documents: {str(e)}")

@app.delete("/admin/documents/{filename}")
async def delete_document(filename: str):
    try:
        file_path = os.path.join(assistant.rag_system.docs_folder, filename)
        if not os.path.exists(file_path):
            logging.warning(f"Document not found for deletion: {file_path}")
            raise HTTPException(status_code=404, detail=f"Document '{filename}' not found")
        os.remove(file_path)
        assistant.rag_system.faiss_index, assistant.rag_system.rag_chunks = assistant.rag_system._build_rag_system()
        logging.info(f"Document {filename} deleted and RAG system updated")
        return {
            "status": "success",
            "message": f"Document '{filename}' successfully deleted and RAG system updated",
            "remaining_chunks": len(assistant.rag_system.rag_chunks)
        }
    except Exception as e:
        logging.error(f"Error deleting document {filename}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting document: {str(e)}")

@app.get("/conversations/{session_id}")
async def get_conversation(session_id: str, limit: Optional[int] = None):
    try:
        conversation = assistant.conversation_cache.get_conversation(session_id)
        if not conversation:
            logging.warning(f"Conversation not found: {session_id}")
            raise HTTPException(status_code=404, detail="Conversation not found")
        if limit:
            conversation = conversation[-limit:]
        logging.info(f"Retrieved conversation {session_id}")
        return {
            "session_id": session_id,
            "total_messages": len(conversation),
            "messages": conversation
        }
    except Exception as e:
        logging.error(f"Error retrieving conversation {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving conversation: {str(e)}")

@app.delete("/conversations/{session_id}")
async def clear_conversation(session_id: str):
    try:
        if session_id not in assistant.conversation_cache.conversations:
            logging.warning(f"Conversation not found for clearing: {session_id}")
            raise HTTPException(status_code=404, detail="Conversation not found")
        assistant.conversation_cache.clear_conversation(session_id)
        logging.info(f"Conversation {session_id} cleared")
        return {
            "status": "success",
            "message": f"Conversation for session {session_id} cleared successfully. You can continue using this session."
        }
    except Exception as e:
        logging.error(f"Error clearing conversation {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error clearing conversation: {str(e)}")

@app.delete("/sessions/{session_id}")
async def delete_session(session_id: str):
    try:
        if session_id not in assistant.conversation_cache.conversations:
            logging.warning(f"Session {session_id} not found")
            raise HTTPException(status_code=404, detail=f"Session {session_id} not found")
        assistant.conversation_cache._remove_session(session_id)
        assistant.conversation_cache._save_conversations()
        if assistant.current_session_id == session_id:
            assistant.current_session_id = None
        logging.info(f"Session {session_id} deleted")
        return {
            "status": "success",
            "message": f"Your session {session_id} has been successfully deleted. All conversation history has been cleared. Feel free to start a new conversation!",
            "session_id": session_id,
            "language": assistant.current_language
        }
    except Exception as e:
        logging.error(f"Error deleting session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting session: {str(e)}")

@app.delete("/sessions")
async def delete_all_sessions():
    try:
        deleted_count = len(assistant.conversation_cache.conversations)
        for session_id in list(assistant.conversation_cache.conversations.keys()):
            assistant.conversation_cache._remove_session(session_id)
        assistant.conversation_cache._save_conversations()
        assistant.current_session_id = None
        logging.info(f"Deleted all sessions (count: {deleted_count})")
        return {
            "status": "success",
            "message": f"Successfully deleted {deleted_count} sessions",
            "deleted_count": deleted_count
        }
    except Exception as e:
        logging.error(f"Error deleting all sessions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting all sessions: {str(e)}")

@app.get("/sessions")
async def get_sessions():
    try:
        sessions = assistant.conversation_cache.get_active_sessions()
        logging.info(f"Retrieved {len(sessions)} active sessions")
        return {
            "active_sessions_count": len(sessions),
            "sessions": sessions
        }
    except Exception as e:
        logging.error(f"Error retrieving sessions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving sessions: {str(e)}")

@app.get("/health")
async def health_check():
    try:
        return {
            "status": "healthy",
            "rag_initialized": assistant.rag_system is not None,
            "model_loaded": assistant.model is not None,
            "chunks_count": len(assistant.rag_system.rag_chunks) if assistant.rag_system.rag_chunks else 0,
            "embedding_dimension": assistant.rag_system.embedding_dim,
            "relevance_threshold": assistant.rag_system.relevance_threshold,
            "sessions_active": len(assistant.conversation_cache.conversations)
        }
    except Exception as e:
        logging.error(f"Error checking health: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error checking health: {str(e)}")

@app.get("/admin/rag/stats")
async def rag_stats():
    try:
        from collections import defaultdict
        documents = defaultdict(int)
        language_distribution = defaultdict(int)
        if assistant.rag_system.rag_chunks:
            for chunk in assistant.rag_system.rag_chunks:
                documents[chunk.metadata.get('source', 'Unknown')] += 1
                lang = chunk.metadata.get('language', 'unknown')
                language_distribution[lang] += 1
        logging.info("Retrieved RAG stats")
        return {
            "total_chunks": len(assistant.rag_system.rag_chunks) if assistant.rag_system.rag_chunks else 0,
            "index_initialized": assistant.rag_system.faiss_index is not None,
            "docs_folder": assistant.rag_system.docs_folder,
            "embedding_dimension": assistant.rag_system.embedding_dim,
            "relevance_threshold": assistant.rag_system.relevance_threshold,
            "documents": dict(documents),
            "language_distribution": dict(language_distribution),
            "vector_store_exists": os.path.exists(assistant.rag_system.vector_store_path)
        }
    except Exception as e:
        logging.error(f"Error retrieving RAG stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving RAG stats: {str(e)}")

@app.get("/admin/sessions/stats")
async def session_stats():
    try:
        total_sessions = len(assistant.conversation_cache.conversations)
        active_sessions = sum(1 for conv in assistant.conversation_cache.conversations.values() if len(conv) > 0)
        logging.info("Retrieved session stats")
        return {
            "total_sessions": total_sessions,
            "active_sessions": active_sessions,
            "max_sessions": assistant.conversation_cache.max_sessions,
            "session_timeout_hours": assistant.conversation_cache.session_timeout / 3600
        }
    except Exception as e:
        logging.error(f"Error retrieving session stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving session stats: {str(e)}")

if __name__ == "__main__":
    print("Starting Professional PM Assistant API...")
    print(f"Docs folder: {assistant.rag_system.docs_folder}")
    print(f"Model path: {assistant.model_path}")
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=False)