FROM python:3.10-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    libopenblas-dev \
    git \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create user
RUN groupadd -r appuser && useradd -r -g appuser -s /bin/bash appuser

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY business_logic_layer/requirements.txt /tmp/requirements.txt
RUN pip install --no-cache-dir --upgrade pip setuptools wheel
RUN pip install --no-cache-dir -r /tmp/requirements.txt

# Install llama-cpp-python
RUN pip install --no-cache-dir llama-cpp-python==0.2.85

# Copy application code (excluding large model files)
COPY business_logic_layer/ /app/business_logic_layer/
COPY docker-compose*.yml /app/
COPY *.md /app/

# Create necessary directories and set permissions
RUN mkdir -p /app/business_logic_layer/Docs /app/business_logic_layer/models \
    && mkdir -p /tmp/transformers_cache /tmp/huggingface \
    && chmod 777 /tmp/transformers_cache /tmp/huggingface \
    && chown -R appuser:appuser /app

# Switch to app user
USER appuser

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV TRANSFORMERS_CACHE=/tmp/transformers_cache
ENV HF_HOME=/tmp/huggingface

# Expose port
EXPOSE 8000

# Change to business_logic_layer directory and run server
WORKDIR /app/business_logic_layer
CMD ["python", "-m", "uvicorn", "server_clean:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
