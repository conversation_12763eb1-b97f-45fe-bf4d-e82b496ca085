import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';

/// Stat item widget following Single Responsibility Principle
/// Handles only statistics display UI logic for chat history
class HistoryStatItem extends StatelessWidget {
  final String label;
  final String value;
  final IconData icon;

  const HistoryStatItem({
    super.key,
    required this.label,
    required this.value,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Icon(icon, color: AppColors.primaryTeal, size: 20),
        SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }
}
