#!/bin/bash

# Fix Streaming Endpoint for Flutter App
echo "🔧 Fixing Streaming Endpoint for Flutter App"
echo "============================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Issue: Flutter app uses /chat/stream but gets empty chunks"
echo ""
echo "🔧 Fix applied:"
echo "   ✅ Updated chat router to handle chunk format correctly"
echo "   ✅ Extract chunk content from response dictionary"
echo "   ✅ Send non-empty chunks only"
echo "   ✅ Use 'chunk' key that Flutter expects"
echo ""

print_status "Rebuilding backend with streaming fix..."

# Stop current container
docker-compose -f docker-compose.vps.yml stop pm-assistant

# Rebuild with no cache
print_status "Rebuilding with streaming fixes..."
docker-compose -f docker-compose.vps.yml build --no-cache pm-assistant

# Start container
print_status "Starting fixed container..."
docker-compose -f docker-compose.vps.yml up -d pm-assistant

# Wait for startup
print_status "Waiting for startup..."
sleep 30

# Check health
print_status "Checking health..."
for i in {1..10}; do
    if curl -f http://localhost:8000/health &>/dev/null; then
        print_success "✅ Service is healthy!"
        break
    else
        print_status "Waiting for service... (attempt $i/10)"
        sleep 5
    fi
    
    if [ $i -eq 10 ]; then
        print_error "❌ Health check failed"
        docker-compose -f docker-compose.vps.yml logs --tail=20 pm-assistant
        exit 1
    fi
done

# Get fresh token
print_status "Getting fresh authentication token..."
TOKEN_RESPONSE=$(curl -s -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "admin123"}')

TOKEN=$(echo $TOKEN_RESPONSE | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
    print_error "❌ Failed to get authentication token"
    exit 1
fi

print_success "✅ Got fresh token"

# Test streaming endpoint (what Flutter uses)
print_status "Testing /chat/stream endpoint (Flutter uses this)..."
echo ""
stream_response=$(timeout 30s curl -s -X POST "http://localhost:8000/chat/stream" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"message": "what is agile?"}' | head -15)

echo "Stream response:"
echo "$stream_response"
echo ""

# Count non-empty chunks
non_empty_chunks=$(echo "$stream_response" | grep -o '"chunk":"[^"]\+' | wc -l)
print_status "Non-empty chunks received: $non_empty_chunks"

if [ "$non_empty_chunks" -gt 0 ]; then
    print_success "✅ Streaming is now working! Flutter should receive content."
    echo ""
    echo "Sample chunks:"
    echo "$stream_response" | grep '"chunk":"[^"]\+' | head -3
else
    print_warning "⚠️ Still receiving empty chunks"
    echo ""
    print_status "Testing /chat/send endpoint (known working)..."
    send_response=$(timeout 30s curl -s -X POST "http://localhost:8000/chat/send" \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer $TOKEN" \
      -d '{"message": "test"}')
    
    if echo "$send_response" | grep -q "response"; then
        print_success "✅ /chat/send still works"
        echo "Send response preview: $(echo "$send_response" | head -c 200)..."
    else
        print_error "❌ Both endpoints have issues"
    fi
fi

echo ""
print_status "Checking recent logs for streaming debug info..."
docker logs pm-assistant-vps | grep -E "(stream|chunk|token)" | tail -10

echo ""
if [ "$non_empty_chunks" -gt 0 ]; then
    print_success "🎉 STREAMING FIX SUCCESSFUL!"
    echo ""
    echo "✅ Flutter app should now receive streaming responses"
    echo "✅ Non-empty chunks are being sent"
    echo "✅ Proper chunk format implemented"
    echo ""
    echo "📱 Flutter app should now work properly!"
else
    print_warning "🔧 STREAMING FIX NEEDS MORE WORK"
    echo ""
    echo "⚠️ Still getting empty chunks"
    echo "🔍 Check logs for model loading issues"
    echo "🔍 Verify model is generating content"
    echo ""
    echo "🧪 Manual test commands:"
    echo "   curl -X POST http://localhost:8000/chat/stream -H 'Content-Type: application/json' -H 'Authorization: Bearer $TOKEN' -d '{\"message\": \"test\"}'"
    echo "   docker logs pm-assistant-vps | tail -20"
fi

echo ""
echo "📋 Access Information:"
echo "   Backend API: http://************:8000"
echo "   Streaming: http://************:8000/chat/stream"
echo "   Send: http://************:8000/chat/send"
