import 'package:flutter/material.dart';
import '../common/yonnovia_button.dart';

/// Knowledge base dialog widget following Single Responsibility Principle
/// Handles only knowledge base management UI logic
class KnowledgeBaseDialog extends StatelessWidget {
  const KnowledgeBaseDialog({super.key});

  static Future<void> show(BuildContext context) {
    return showDialog(
      context: context,
      builder: (context) => const KnowledgeBaseDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Knowledge Base Management'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text('Upload and manage documents for the AI assistant.'),
          const Si<PERSON><PERSON><PERSON>(height: 16),
          Yonnov<PERSON><PERSON><PERSON><PERSON>(
            text: 'Upload Documents',
            onPressed: () {},
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Close'),
        ),
      ],
    );
  }
}
