{"cells": [{"cell_type": "markdown", "id": "f8861f32", "metadata": {}, "source": ["# Data Augmentation for Training Data\n", "\n", "This notebook performs data augmentation on training data using WordNet synonym replacement to increase the diversity of the training dataset."]}, {"cell_type": "markdown", "id": "da545482", "metadata": {}, "source": ["## Import Required Libraries\n", "\n", "Import all necessary libraries for data augmentation including nlpaug and nltk."]}, {"cell_type": "code", "execution_count": null, "id": "7daf7cd8", "metadata": {}, "outputs": [], "source": ["import json\n", "from tqdm import tqdm\n", "import nlpaug.augmenter.word as naw\n", "import nltk\n", "import os"]}, {"cell_type": "markdown", "id": "daf3ff9f", "metadata": {}, "source": ["## Download and Setup NLTK Resources\n", "\n", "Ensure all required NLTK resources are available for the augmentation process."]}, {"cell_type": "code", "execution_count": null, "id": "92df5974", "metadata": {}, "outputs": [], "source": ["# Ensure required NLTK resources are available\n", "try:\n", "    # Try new NLTK tagger (since v3.8.1)\n", "    nltk.data.find('taggers/averaged_perceptron_tagger_eng/averaged_perceptron_tagger_eng.weights.json')\n", "except LookupError:\n", "    try:\n", "        nltk.download('averaged_perceptron_tagger_eng')\n", "    except:\n", "        pass"]}, {"cell_type": "code", "execution_count": null, "id": "b1c626cd", "metadata": {}, "outputs": [], "source": ["try:\n", "    # Try old NLTK tagger (for older nlpaug/nltk)\n", "    nltk.data.find('taggers/averaged_perceptron_tagger/averaged_perceptron_tagger.pickle')\n", "except LookupError:\n", "    try:\n", "        nltk.download('averaged_perceptron_tagger')\n", "    except:\n", "        pass"]}, {"cell_type": "code", "execution_count": null, "id": "f5d744c8", "metadata": {}, "outputs": [], "source": ["try:\n", "    nltk.data.find('corpora/wordnet')\n", "except LookupError:\n", "    nltk.download('wordnet')"]}, {"cell_type": "code", "execution_count": null, "id": "68c424b3", "metadata": {}, "outputs": [], "source": ["try:\n", "    nltk.data.find('corpora/omw-1.4')\n", "except LookupError:\n", "    nltk.download('omw-1.4')"]}, {"cell_type": "markdown", "id": "1383a993", "metadata": {}, "source": ["## Initialize the Augmentation Model\n", "\n", "Create a WordNet synonym augmentation model for lightweight data augmentation."]}, {"cell_type": "code", "execution_count": null, "id": "aab98ba6", "metadata": {}, "outputs": [], "source": ["# Use WordNet synonym augmentation (very lightweight)\n", "syn_aug = naw.SynonymAug(aug_src='wordnet')"]}, {"cell_type": "markdown", "id": "7244f6a8", "metadata": {}, "source": ["## Set Input and Output Paths\n", "\n", "Define the paths for input and output data files."]}, {"cell_type": "code", "execution_count": null, "id": "158e18e9", "metadata": {}, "outputs": [], "source": ["input_path = \"data/formatted_data_en.jsonl\"\n", "output_path = \"data/augmented_data_en_2.jsonl\""]}, {"cell_type": "markdown", "id": "ec7560f7", "metadata": {}, "source": ["## Perform Data Augmentation\n", "\n", "Process the input file line by line, applying synonym augmentation to the instructions and saving the augmented data."]}, {"cell_type": "code", "execution_count": null, "id": "95395366", "metadata": {}, "outputs": [], "source": ["with open(input_path, \"r\", encoding=\"utf-8\") as infile, open(output_path, \"w\", encoding=\"utf-8\") as outfile:\n", "    for line in tqdm(infile):\n", "        try:\n", "            sample = json.loads(line.strip())\n", "            instr = sample.get(\"instruction\", \"\")\n", "            # Synonym augment the instruction\n", "            augmented_instr = syn_aug.augment(instr)\n", "            # Only write if the paraphrase is different\n", "            if augmented_instr != instr:\n", "                augmented_sample = sample.copy()\n", "                augmented_sample[\"instruction\"] = augmented_instr\n", "                outfile.write(json.dumps(augmented_sample, ensure_ascii=False) + \"\\n\")\n", "        except Exception as e:\n", "            print(\"Error augmenting line:\", e)"]}, {"cell_type": "markdown", "id": "4af942dd", "metadata": {}, "source": ["## Summary\n", "\n", "The data augmentation process is complete. The augmented data has been saved to the specified output file with synonym-replaced instructions to increase training data diversity."]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}