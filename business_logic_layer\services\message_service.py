"""
Enhanced Message Service
========================
Handles message processing and response generation with AI integration.
Optimized version with fast responses, proper structure, and multilingual support.
"""

import uuid
import re
import logging
import json
import os
import pickle
import gzip
import numpy as np
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List, Generator
from langdetect import detect, LangDetectException

from fastapi import HTTPException
from pydantic import BaseModel
from llama_cpp import Llama

# Import session service
from .session_service import SessionService

# Import for FAISS vector store
try:
    import faiss
    from sentence_transformers import SentenceTransformer
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False
    logging.warning("FAISS or sentence-transformers not available")

# Models
class MessageRequest(BaseModel):
    message: str
    session_id: Optional[str] = None

class MessageResponse(BaseModel):
    success: bool
    response: str
    session_id: str
    timestamp: datetime

class MessageService:
    def __init__(self):
        self.session_service = SessionService()
        self.model = None
        # Use absolute path in container
        self.model_path = "/app/business_logic_layer/models/qwen2.5-7b-pm-assistant.q4_k_m.gguf"
        self.current_language = "en"
        self.clarification_count = {}  # Track clarification requests per session
        # Fast mode: prioritize instant streaming over heavy RAG or long outputs
        self.fast_mode = True

        # FAISS Vector Store components
        self.vector_store_path = "/app/business_logic_layer/Docs/pm_rag_store.pkl.gz"
        self.embedding_model = None
        self.faiss_index = None
        self.document_chunks = []
        self.chunk_metadata = []

        # Load FAISS vector store
        self._load_vector_store()

        # Setup multilingual responses
        self._setup_multilingual_responses()

        # ENHANCED system prompt with mandatory RAG integration and multilingual support
        self.system_prompt = """You are an expert project management assistant. You MUST follow this EXACT structure for ALL responses.

**RESPONSE STRUCTURE (MANDATORY):**

**Thinking**
1. Analyze the query: [Summarize what the user is asking in 1 sentence]
2. Review RAG context: [Reference SPECIFIC information from provided documents - mention frameworks, page numbers, sections]
3. Key concepts: [List 2-3 main PM concepts to address]
4. Response approach: [Decide if answer needs paragraphs or bullet points based on complexity]

**Answer**
[Provide detailed response using either paragraphs for complex topics or bullet points for lists/steps]
- For complex explanations: Use 2-3 detailed paragraphs
- For processes/steps: Use numbered points or bullet points
- Always reference the RAG context in your explanations

**Source**
[Reference 1 from RAG context]
[Reference 2 from RAG context]

**CRITICAL RULES:**
1. ALWAYS use provided RAG context in Thinking section with specific document references
2. Follow the EXACT structure: Thinking → Answer → Source
3. Use the language of the query (French, Arabic, or English)
4. STOP IMMEDIATELY after Source section - NEVER continue writing
5. Keep responses focused and practical
6. Use only TOP 2 most relevant references
7. For French queries, respond completely in French
8. For Arabic queries, respond completely in Arabic
9. Answer format depends on question complexity:
   - Simple questions: Use bullet points or numbered lists
   - Complex explanations: Use detailed paragraphs
10. Maximum response length: 300 tokens for speed"""

        # Load AI model for intelligent responses
        logging.info("🤖 Initializing Enhanced PM Assistant Message Service...")
        self._load_model()

    def _setup_multilingual_responses(self):
        """Setup multilingual response templates"""
        self.responses = {
            "greetings": {
                "en": "Hello! How can I assist you with project management today?",
                "fr": "Bonjour ! Comment puis-je vous aider avec la gestion de projet aujourd'hui ?",
                "ar": "مرحبًا! كيف يمكنني مساعدتك في إدارة المشاريع اليوم؟"
            },
            "out_of_scope": {
                "en": "I'll help from a project management perspective. Here's a PM-oriented answer to your question.",
                "fr": "Je vais vous aider dans une perspective de gestion de projet. Voici une réponse orientée GP à votre question.",
                "ar": "سأساعدك من منظور إدارة المشاريع. إليك إجابة موجهة لإدارة المشاريع على سؤالك."
            },
            "thinking_templates": {
                "en": {
                    "analyze": "Analyzing the project management question about",
                    "review": "Reviewing RAG documents for relevant information on",
                    "concepts": "Key PM concepts to address:",
                    "approach": "Response approach: detailed explanation needed"
                },
                "fr": {
                    "analyze": "Analyse de la question de gestion de projet concernant",
                    "review": "Examen des documents RAG pour des informations pertinentes sur",
                    "concepts": "Concepts GP clés à aborder :",
                    "approach": "Approche de réponse : explication détaillée nécessaire"
                },
                "ar": {
                    "analyze": "تحليل سؤال إدارة المشروع حول",
                    "review": "مراجعة وثائق RAG للمعلومات ذات الصلة بـ",
                    "concepts": "المفاهيم الرئيسية لإدارة المشاريع التي يجب تناولها:",
                    "approach": "منهج الاستجابة: الحاجة إلى تفسير مفصل"
                }
            }
        }

    def _load_vector_store(self):
        """Load FAISS vector store or create simple fallback"""
        try:
            if not FAISS_AVAILABLE:
                logging.warning("⚠️ FAISS not available, using simple keyword search")
                self._create_simple_knowledge_base()
                return

            # Try to load existing vector store
            if os.path.exists(self.vector_store_path):
                logging.info(f"📂 Loading FAISS vector store from: {self.vector_store_path}")

                try:
                    with gzip.open(self.vector_store_path, 'rb') as f:
                        vector_data = pickle.load(f)

                    # Extract components from vector store
                    if isinstance(vector_data, dict):
                        self.document_chunks = vector_data.get('chunks', [])
                        self.chunk_metadata = vector_data.get('metadata', [])
                        embeddings = vector_data.get('embeddings', [])

                        if embeddings and len(embeddings) > 0:
                            # Create FAISS index
                            embeddings_array = np.array(embeddings).astype('float32')
                            dimension = embeddings_array.shape[1]
                            self.faiss_index = faiss.IndexFlatIP(dimension)

                            # Normalize embeddings for cosine similarity
                            faiss.normalize_L2(embeddings_array)
                            self.faiss_index.add(embeddings_array)

                            logging.info(f"✅ FAISS vector store loaded: {len(self.document_chunks)} chunks, dimension: {dimension}")

                            # Load embedding model for query encoding
                            self._load_embedding_model()
                            return

                except Exception as e:
                    logging.warning(f"⚠️ Failed to load vector store: {str(e)}")

            # Fallback to simple knowledge base
            logging.info("🔄 Creating simple knowledge base as fallback")
            self._create_simple_knowledge_base()

        except Exception as e:
            logging.error(f"❌ Error in vector store loading: {str(e)}")
            self._create_simple_knowledge_base()

    def _create_simple_knowledge_base(self):
        """Create multilingual fallback knowledge base when FAISS is not available"""
        self.simple_knowledge = {
            'agile': {
                'en': "Agile methodology focuses on iterative development, collaboration, and adaptability to change",
                'fr': "La méthodologie Agile se concentre sur le développement itératif, la collaboration et l'adaptabilité au changement",
                'ar': "منهجية الأجايل تركز على التطوير التكراري والتعاون والقدرة على التكيف مع التغيير"
            },
            'risk_management': {
                'en': "Risk management includes identification, analysis, response planning, and monitoring of project risks",
                'fr': "La gestion des risques comprend l'identification, l'analyse, la planification des réponses et le suivi des risques du projet",
                'ar': "إدارة المخاطر تشمل تحديد وتحليل ووضع خطط الاستجابة ومراقبة مخاطر المشروع"
            },
            'stakeholder': {
                'en': "Stakeholders are individuals or groups who can affect or are affected by the project outcomes",
                'fr': "Les parties prenantes sont des individus ou groupes qui peuvent affecter ou être affectés par les résultats du projet",
                'ar': "أصحاب المصلحة هم الأفراد أو المجموعات الذين يمكن أن يؤثروا على نتائج المشروع أو يتأثروا بها"
            }
        }

    def _load_embedding_model(self):
        """Load embedding model for query encoding"""
        try:
            if not FAISS_AVAILABLE:
                return

            logging.info("🔄 Loading embedding model for queries...")
            # Set cache to writable location
            os.environ['TRANSFORMERS_CACHE'] = '/tmp/transformers_cache'
            os.environ['HF_HOME'] = '/tmp/huggingface'
            os.makedirs('/tmp/transformers_cache', exist_ok=True)
            os.makedirs('/tmp/huggingface', exist_ok=True)

            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2', cache_folder='/tmp/transformers_cache')
            logging.info("✅ Embedding model loaded successfully")

        except Exception as e:
            logging.error(f"❌ Error loading embedding model: {str(e)}")
            self.embedding_model = None

    def _detect_language(self, text: str) -> str:
        """Detect language of input text with improved accuracy"""
        try:
            # Clean text for better detection
            clean_text = re.sub(r'[^\w\s]', '', text).strip()
            
            if len(clean_text) < 3:
                return 'en'  # Default for very short text
                
            detected = detect(clean_text)
            
            # Map similar languages to supported ones
            language_mapping = {
                'fr': 'fr',
                'ar': 'ar', 
                'en': 'en',
                'es': 'en',  # Spanish -> English fallback
                'de': 'en',  # German -> English fallback
                'it': 'en'   # Italian -> English fallback
            }
            
            return language_mapping.get(detected, 'en')
            
        except LangDetectException:
            # Fallback language detection based on keywords
            text_lower = text.lower()
            
            # Arabic keywords
            arabic_keywords = ['مشروع', 'إدارة', 'كيف', 'ماذا', 'متى', 'أين', 'لماذا']
            if any(keyword in text for keyword in arabic_keywords):
                return 'ar'
                
            # French keywords  
            french_keywords = ['projet', 'gestion', 'comment', 'pourquoi', 'quand', 'où']
            if any(keyword in text_lower for keyword in french_keywords):
                return 'fr'
                
            return 'en'  # Default to English

    def _is_greeting(self, text: str) -> bool:
        """Check if text is a greeting in multiple languages"""
        greetings = [
            # English
            "hello", "hi", "hey", "good morning", "good afternoon", "good evening", "greetings",
            # French
            "bonjour", "salut", "bonsoir", "bonne journée", "bonne soirée", "coucou",
            # Arabic
            "مرحبا", "أهلا", "السلام عليكم", "صباح الخير", "مساء الخير", "أهلاً وسهلاً"
        ]
        text_lower = text.lower().strip()
        return any(greeting in text_lower for greeting in greetings) and len(text.split()) <= 3

    def _is_clarification_request(self, text: str, language: str) -> bool:
        """Check if text is asking for clarification"""
        clarification_phrases = {
            'en': ['i don\'t understand', 'didn\'t understand', 'explain more', 'clarify', 'what do you mean', 'can you explain', 'more detail', 'elaborate', 'unclear'],
            'fr': ['je ne comprends pas', 'je n\'ai pas compris', 'expliquez plus', 'clarifiez', 'que voulez-vous dire', 'plus de détails', 'précisez', 'expliquez mieux'],
            'ar': ['لم أفهم', 'لم أفهم', 'اشرح أكثر', 'وضح', 'ماذا تقصد', 'تفاصيل أكثر', 'أوضح أكثر', 'اشرح بوضوح']
        }

        phrases = clarification_phrases.get(language, clarification_phrases['en'])
        text_lower = text.lower()
        return any(phrase in text_lower for phrase in phrases)

    def _get_rag_context(self, query: str, language: str = "en", top_k: int = 2) -> Dict[str, Any]:
        """Get relevant context from FAISS vector store using semantic search"""
        try:
            if not self.faiss_index or not self.embedding_model or not self.document_chunks:
                return self._get_fallback_context(query, language)

            logging.info(f"🔍 Searching vector store for: {query[:50]}... (lang: {language})")

            # Encode query to embedding
            query_embedding = self.embedding_model.encode([query])
            query_embedding = np.array(query_embedding).astype('float32')

            # Normalize for cosine similarity
            faiss.normalize_L2(query_embedding)

            # Search FAISS index
            scores, indices = self.faiss_index.search(query_embedding, top_k)

            if len(indices[0]) == 0:
                return self._get_fallback_context(query, language)

            # Get relevant chunks
            relevant_chunks = []
            for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
                if idx < len(self.document_chunks) and score > 0.3:  # Similarity threshold
                    chunk = self.document_chunks[idx]
                    metadata = self.chunk_metadata[idx] if idx < len(self.chunk_metadata) else {}

                    relevant_chunks.append({
                        'content': chunk[:300],  # Keep concise for speed
                        'score': float(score),
                        'source': metadata.get('source', 'Guide PMBOK'),
                        'pages': metadata.get('pages', 'N/A'),
                        'section': metadata.get('section', 'General')
                    })

            if not relevant_chunks:
                return self._get_fallback_context(query, language)

            # Format context for different languages
            context_text = self._format_rag_context(relevant_chunks, language)
            references = [f"{chunk['source']}, Pages: {chunk['pages']}" for chunk in relevant_chunks[:2]]

            return {
                'context': context_text,
                'references': references,
                'chunks_found': len(relevant_chunks)
            }

        except Exception as e:
            logging.error(f"❌ Error in RAG context retrieval: {str(e)}")
            return self._get_fallback_context(query, language)

    def _get_fallback_context(self, query: str, language: str) -> Dict[str, Any]:
        """Get fallback context when RAG is not available"""
        # Simple keyword matching for fallback
        query_lower = query.lower()
        
        fallback_refs = {
            'en': ["PMBOK Guide - 7th Edition", "PM Best Practices"],
            'fr': ["Guide PMBOK - 7e édition", "Meilleures pratiques GP"], 
            'ar': ["دليل PMBOK - الإصدار السابع", "أفضل ممارسات إدارة المشاريع"]
        }
        
        fallback_context = {
            'en': "General project management knowledge base covering standard PM practices and methodologies.",
            'fr': "Base de connaissances générale en gestion de projet couvrant les pratiques et méthodologies GP standard.",
            'ar': "قاعدة المعرفة العامة لإدارة المشاريع تغطي الممارسات والمنهجيات المعيارية."
        }
        
        return {
            'context': fallback_context.get(language, fallback_context['en']),
            'references': fallback_refs.get(language, fallback_refs['en']),
            'chunks_found': 0
        }

    def _format_rag_context(self, chunks: List[Dict], language: str) -> str:
        """Format RAG context based on language"""
        if not chunks:
            return ""
            
        context_parts = []
        for chunk in chunks[:2]:  # Only top 2 for speed
            source_info = f"Source: {chunk['source']}"
            if chunk['pages'] != 'N/A':
                source_info += f", Pages: {chunk['pages']}"
            if chunk['section'] != 'General':
                source_info += f", Section: {chunk['section']}"
                
            context_parts.append(f"{source_info} | Content: {chunk['content'][:200]}...")
        
        return " || ".join(context_parts)

    def _determine_response_format(self, query: str, language: str) -> str:
        """Determine if response should use paragraphs or bullet points"""
        
        # Keywords that suggest list/step format needed
        list_indicators = {
            'en': ['steps', 'how to', 'process', 'methods', 'ways', 'list', 'phases', 'stages'],
            'fr': ['étapes', 'comment', 'processus', 'méthodes', 'façons', 'liste', 'phases'],
            'ar': ['خطوات', 'كيف', 'عملية', 'طرق', 'أساليب', 'قائمة', 'مراحل']
        }
        
        # Keywords that suggest paragraph format needed
        paragraph_indicators = {
            'en': ['explain', 'describe', 'what is', 'definition', 'concept', 'theory', 'why'],
            'fr': ['expliquer', 'décrire', 'qu\'est-ce que', 'définition', 'concept', 'théorie', 'pourquoi'],
            'ar': ['اشرح', 'وضح', 'ما هو', 'تعريف', 'مفهوم', 'نظرية', 'لماذا']
        }
        
        query_lower = query.lower()
        
        # Check for list indicators
        list_words = list_indicators.get(language, list_indicators['en'])
        if any(word in query_lower for word in list_words):
            return 'list'
        
        # Check for paragraph indicators
        paragraph_words = paragraph_indicators.get(language, paragraph_indicators['en'])
        if any(word in query_lower for word in paragraph_words):
            return 'paragraph'
        
        # Default to paragraph for complex explanations
        return 'paragraph' if len(query.split()) > 8 else 'list'

    def _clean_response(self, response: str) -> str:
        """Clean response to prevent repetition and ensure proper structure"""
        
        # Remove content after Source section completion
        lines = response.split('\n')
        cleaned_lines = []
        source_section_found = False
        source_count = 0
        
        for line in lines:
            line_stripped = line.strip()
            
            # Track when we enter Source section
            if line_stripped == "**Source**":
                source_section_found = True
                cleaned_lines.append(line)
                continue
            
            # If in source section, count references
            if source_section_found:
                if line_stripped.startswith('[') and line_stripped.endswith(']'):
                    source_count += 1
                    cleaned_lines.append(line)
                    
                    # Stop after 2 references
                    if source_count >= 2:
                        break
                elif line_stripped == '':
                    cleaned_lines.append(line)
                    continue
                else:
                    # Non-reference line in source section, might be repetition
                    if not line_stripped.startswith('**'):
                        break
            
            cleaned_lines.append(line)
            
            # Prevent duplicate sections
            if (line_stripped.startswith('**') and line_stripped.endswith('**') and 
                line_stripped in [l.strip() for l in cleaned_lines[:-1]]):
                cleaned_lines.pop()  # Remove duplicate section header
                break
        
        return '\n'.join(cleaned_lines).strip()

    def _format_final_response(self, response: str, rag_data: Dict[str, Any], language: str) -> str:
        """Format final response ensuring clean structure with proper RAG integration"""
        
        # Clean the response first
        response = self._clean_response(response)
        
        # Check if response already has proper structure
        if "**Thinking**" in response and "**Answer**" in response and "**Source**" in response:
            return response.strip()
        
        # Create structured response if missing
        templates = self.responses["thinking_templates"][language]
        
        structured_response = f"""**Thinking**

1. {templates['analyze']} {response[:50]}...
2. {templates['review']} {rag_data.get('context', 'documents généraux')[:100]}...
3. {templates['concepts']} concepts clés identifiés
4. {templates['approach']}

**Answer**

{response}

**Source**

{chr(10).join([f'[{ref}]' for ref in rag_data.get('references', ['Guide PMBOK', 'PM Best Practices'])[:2]])}"""

        return structured_response.strip()

    def _load_model(self):
        """Load the specialized PM model with optimized settings for instant responses"""
        try:
            import os
            logging.info(f"🚀 Initializing FAST PM Assistant AI Model...")
            logging.info(f"📁 Checking model file at: {self.model_path}")

            # Check if model file exists
            if not os.path.exists(self.model_path):
                logging.warning(f"❌ Model file not found at {self.model_path}")
                logging.info("🔄 Running in fallback mode without AI model")
                self.model = None
                return

            # Get file size for logging
            file_size = os.path.getsize(self.model_path) / (1024**3)  # GB
            logging.info(f"📊 Model file size: {file_size:.2f} GB")

            # HIGH PERFORMANCE optimization for 8 vCPU, 15GB RAM VPS
            cpu_cores = os.cpu_count() or 8
            n_threads = min(cpu_cores - 1, 7)  # Use 7 of 8 cores for maximum performance

            # Get environment variables for VPS optimization
            n_ctx = int(os.getenv('LLM_N_CTX', 1024))      # Optimized context
            n_batch = int(os.getenv('LLM_N_BATCH', 512))   # Large batch for throughput
            use_mmap = os.getenv('LLM_USE_MMAP', 'true').lower() == 'true'
            use_mlock = os.getenv('LLM_USE_MLOCK', 'true').lower() == 'true'
            f16_kv = os.getenv('LLM_F16_KV', 'true').lower() == 'true'

            logging.info(f"🚀 HIGH PERFORMANCE Mode: {cpu_cores} cores, using {n_threads} threads")
            logging.info(f"⚡ Context: {n_ctx}, Batch: {n_batch}, MMAP: {use_mmap}, MLOCK: {use_mlock}")
            logging.info("� Loading AI model for MAXIMUM SPEED and QUALITY...")

            self.model = Llama(
                model_path=self.model_path,
                n_ctx=n_ctx,
                n_threads=n_threads,
                n_gpu_layers=0,  # CPU only for VPS
                verbose=False,
                temperature=0.7,  # Higher for better generation
                top_p=0.9,       # High for variety
                repeat_penalty=1.1,  # Low to allow normal text flow
                n_batch=n_batch,
                use_mmap=use_mmap,    # Use environment variable
                use_mlock=use_mlock,  # Use environment variable
                low_vram=False,       # Disable for high-RAM VPS
                f16_kv=f16_kv,        # Use environment variable
                # High performance parameters for 15GB RAM VPS
                seed=42,              # Fixed seed for consistent responses
                top_k=20,             # Limit token choices for faster inference
                numa=True             # Enable NUMA optimization for multi-core
            )
            logging.info("✅ FAST PM AI Model loaded successfully! Ready for instant intelligent responses.")
        except Exception as e:
            logging.error(f"❌ Error loading model: {str(e)}")
            logging.info("🔄 Switching to fallback mode")
            self.model = None

    async def process_message(
        self,
        message: str,
        user_id: str,
        session_id: Optional[str] = None,
        user_role: str = "user"
    ) -> Dict[str, Any]:
        """Process user message and generate response with multi-language support"""
        logging.info(f"🧠 Processing message from user {user_id}: {message[:50]}...")

        if not session_id:
            session_id = str(uuid.uuid4())

        # Detect language
        language = self._detect_language(message)
        self.current_language = language
        logging.info(f"🌍 Detected language: {language}")

        # Check for greetings
        if self._is_greeting(message):
            response = self.responses["greetings"][language]
            logging.info(f"👋 Greeting detected, responding in {language}")
            return {
                "response": response,
                "session_id": session_id,
                "timestamp": datetime.now(timezone.utc),
                "message_id": str(uuid.uuid4()),
                "language": language,
                "type": "greeting"
            }

        # Check for clarification requests
        is_clarification = self._is_clarification_request(message, language)
        if is_clarification:
            self.clarification_count[session_id] = self.clarification_count.get(session_id, 0) + 1

        # Add user message to session
        await self.session_service.add_message_to_session(
            session_id,
            user_id,
            message,
            is_user=True
        )

        # Generate PM response using AI model
        response = await self._generate_ai_response(message, user_id, user_role, session_id, is_clarification, language)

        # Add assistant response to session
        await self.session_service.add_message_to_session(
            session_id,
            user_id,
            response,
            is_user=False
        )

        return {
            "response": response,
            "session_id": session_id,
            "timestamp": datetime.now(timezone.utc),
            "message_id": str(uuid.uuid4()),
            "language": language,
            "type": "pm_response"
        }

    async def _generate_ai_response(self, message: str, user_id: str, role: str, session_id: str, is_clarification: bool = False, language: str = "en") -> str:
        """Generate AI response with improved stopping and formatting"""
        try:
            if self.model is None:
                return await self._generate_structured_fallback_response(message, is_clarification, session_id, language)

            logging.info(f"🧠 Generating AI response for: {message[:50]}... (lang: {language})")

            # Get RAG context
            rag_data = self._get_rag_context(message, language)
            
            # Determine response format
            response_format = self._determine_response_format(message, language)
            
            # Create enhanced prompt with RAG context and language instruction
            if is_clarification:
                clarification_level = self.clarification_count.get(session_id, 1)
                complexity = 'simple' if clarification_level >= 2 else 'intermediate'
                user_message = f"""RAG Context: {rag_data['context']}

Please clarify this PM topic in {complexity} terms ({language} language): {message}

Response format needed: {response_format}"""
            else:
                user_message = f"""RAG Context: {rag_data['context']}

Project Management Question ({language} language): {message}

Response format needed: {response_format}
Available references: {', '.join(rag_data['references'])}"""

            # Enhanced prompt structure
            full_prompt = f"<|im_start|>system\n{self.system_prompt}<|im_end|>\n<|im_start|>user\n{user_message}<|im_end|>\n<|im_start|>assistant\n"

            # Generate with optimized parameters for speed and accuracy
            output = self.model(
                full_prompt,
                max_tokens=350,  # Increased slightly for detailed responses
                temperature=0.1,  # Very low for consistency
                top_p=0.7,
                top_k=20,  # Limited choices for speed
                repeat_penalty=2.0,  # High to prevent repetition
                stop=[
                    "<|im_end|>", 
                    "**Source**\n\n[",
                    "**Thinking**\n\n1.",
                    "**Answer**\n\n1.",
                    "\n\n**",
                    "\n\n\n",
                    "[Reference 3]",
                    "[Source 3]"
                ],
                echo=False,
                stream=False
            )

            generated_text = output['choices'][0]['text'].strip()
            
            # Clean and format response
            cleaned_response = self._clean_response(generated_text)
            formatted_response = self._format_final_response(cleaned_response, rag_data, language)
            
            logging.info(f"✅ Generated response ({len(formatted_response)} chars) in {language}")
            return formatted_response

        except Exception as e:
            logging.error(f"❌ Error generating AI response: {str(e)}")
            return await self._generate_structured_fallback_response(message, is_clarification, session_id, language)

    async def _generate_structured_fallback_response(self, message: str, is_clarification: bool = False, session_id: str = "", language: str = "en") -> str:
        """Generate structured fallback response in the requested language"""
        
        # Get fallback context
        rag_data = self._get_fallback_context(message, language)
        
        # Language-specific templates
        templates = {
            "en": {
                "thinking_analyze": "Analyzing the project management question about",
                "thinking_review": "Reviewing available PM knowledge for",
                "thinking_concepts": "Key PM concepts: stakeholder management, risk assessment, project planning",
                "thinking_approach": "Response approach: providing structured PM guidance",
                "fallback_explanation": "The AI model is currently not available for detailed analysis, but I can provide structured PM guidance for your question about",
                "comprehensive_guide": "For comprehensive project management analysis, please ensure the full AI model is enabled.",
                "sources": ["PM Assistant Support System", "Project Management Best Practices"]
            },
            "fr": {
                "thinking_analyze": "Analyse de la question de gestion de projet concernant",
                "thinking_review": "Examen des connaissances GP disponibles pour",
                "thinking_concepts": "Concepts GP clés : gestion des parties prenantes, évaluation des risques, planification de projet",
                "thinking_approach": "Approche de réponse : fournir des conseils GP structurés",
                "fallback_explanation": "Le modèle IA n'est actuellement pas disponible pour une analyse détaillée, mais je peux fournir des conseils GP structurés pour votre question concernant",
                "comprehensive_guide": "Pour une analyse complète de gestion de projet, veuillez vous assurer que le modèle IA complet est activé.",
                "sources": ["Système de Support Assistant GP", "Meilleures Pratiques de Gestion de Projet"]
            },
            "ar": {
                "thinking_analyze": "تحليل سؤال إدارة المشروع حول",
                "thinking_review": "مراجعة المعرفة المتاحة لإدارة المشاريع لـ",
                "thinking_concepts": "المفاهيم الرئيسية لإدارة المشاريع: إدارة أصحاب المصلحة، تقييم المخاطر، تخطيط المشروع",
                "thinking_approach": "منهج الاستجابة: توفير التوجيه المنظم لإدارة المشاريع",
                "fallback_explanation": "نموذج الذكاء الاصطناعي غير متاح حالياً للتحليل المفصل، لكن يمكنني تقديم التوجيه المنظم لإدارة المشاريع لسؤالك حول",
                "comprehensive_guide": "للحصول على تحليل شامل لإدارة المشاريع، يرجى التأكد من تفعيل نموذج الذكاء الاصطناعي الكامل.",
                "sources": ["نظام دعم مساعد إدارة المشاريع", "أفضل ممارسات إدارة المشاريع"]
            }
        }
        
        template = templates.get(language, templates["en"])
        
        if is_clarification:
            return f"""**Thinking**

1. {template['thinking_analyze']} "{message[:50]}..."
2. {template['thinking_review']} clarification détaillée
3. {template['thinking_concepts']}  
4. {template['thinking_approach']} avec explications simplifiées

**Answer**

Je comprends que vous avez besoin de plus de clarifications. {template['fallback_explanation']} "{message[:80]}...". {template['comprehensive_guide']}

**Source**

[{template['sources'][0]}]
[{template['sources'][1]}]"""

        return f"""**Thinking**

1. {template['thinking_analyze']} "{message[:50]}..."
2. {template['thinking_review']} cette question
3. {template['thinking_concepts']}
4. {template['thinking_approach']}

**Answer**

{template['fallback_explanation']} "{message[:80]}...". {template['comprehensive_guide']}

**Source**

[{template['sources'][0]}]
[{template['sources'][1]}]"""

    async def stream_ai_response(self, message: str, user_id: str, role: str, session_id: Optional[str] = None):
        """Stream AI response using the working generate_ai_response method"""
        try:
            if not session_id:
                session_id = str(uuid.uuid4())

            logging.info(f"🚀 Starting streaming for: {message[:50]}...")

            # Use the working generate_ai_response method and stream it word by word
            full_response = await self.generate_ai_response(
                message=message,
                user_id=user_id,
                role=role,
                session_id=session_id
            )

            logging.info(f"✅ Generated response, length: {len(full_response)} chars")

            # Stream the response word by word for real-time effect
            words = full_response.split()
            for i, word in enumerate(words):
                chunk = word + " "
                yield {
                    "chunk": chunk,
                    "completed": False,
                    "type": "token",
                    "token_count": i + 1,
                    "language": "en"
                }

                # Small delay for streaming effect (optional)
                import asyncio
                await asyncio.sleep(0.01)  # 10ms delay between words

            # Send completion signal
            yield {
                "chunk": "",
                "completed": True,
                "full_response": full_response,
                "type": "complete",
                "total_tokens": len(words),
                "language": "en"
            }

            logging.info(f"🏁 Streaming completed, sent {len(words)} words")

        except Exception as e:
            logging.error(f"❌ Error streaming: {str(e)}")
            # Fallback response
            fallback_response = "I apologize, but I'm having trouble generating a response right now. Please try again."
            words = fallback_response.split()
            for i, word in enumerate(words):
                yield {
                    "chunk": word + " ",
                    "completed": False,
                    "type": "token",
                    "token_count": i + 1,
                    "language": "en"
                }

            yield {
                "chunk": "",
                "completed": True,
                "full_response": fallback_response,
                "type": "error",
                "language": "en"
            }

    async def get_session_history(self, session_id: str, user_id: str) -> List[Dict[str, Any]]:
        """Get session history for context"""
        try:
            return await self.session_service.get_session_messages(session_id, user_id)
        except Exception as e:
            logging.error(f"❌ Error getting session history: {str(e)}")
            return []

    def get_model_status(self) -> Dict[str, Any]:
        """Get current model status and configuration"""
        return {
            "model_loaded": self.model is not None,
            "model_path": self.model_path,
            "faiss_available": FAISS_AVAILABLE,
            "vector_store_loaded": self.faiss_index is not None,
            "document_chunks_count": len(self.document_chunks),
            "embedding_model_loaded": self.embedding_model is not None,
            "current_language": self.current_language,
            "system_prompt_configured": bool(self.system_prompt),
            "fast_mode": self.fast_mode,
            "supported_languages": ["en", "fr", "ar"],
            "multilingual_templates_loaded": bool(self.responses)
        }

    async def analyze_query_complexity(self, message: str, language: str) -> Dict[str, Any]:
        """Analyze query complexity to optimize response generation"""
        
        complexity_indicators = {
            'simple': {
                'en': ['what is', 'define', 'meaning of'],
                'fr': ['qu\'est-ce que', 'définir', 'signification de'],
                'ar': ['ما هو', 'تعريف', 'معنى']
            },
            'moderate': {
                'en': ['how to', 'steps', 'process', 'methods'],
                'fr': ['comment', 'étapes', 'processus', 'méthodes'],
                'ar': ['كيف', 'خطوات', 'عملية', 'طرق']
            },
            'complex': {
                'en': ['analyze', 'compare', 'evaluate', 'relationship between'],
                'fr': ['analyser', 'comparer', 'évaluer', 'relation entre'],
                'ar': ['تحليل', 'مقارنة', 'تقييم', 'العلاقة بين']
            }
        }
        
        message_lower = message.lower()
        complexity_score = 0
        detected_complexity = 'simple'
        
        # Check complexity indicators
        for complexity, lang_indicators in complexity_indicators.items():
            indicators = lang_indicators.get(language, lang_indicators['en'])
            for indicator in indicators:
                if indicator in message_lower:
                    if complexity == 'simple':
                        complexity_score = max(complexity_score, 1)
                    elif complexity == 'moderate':
                        complexity_score = max(complexity_score, 2)
                    elif complexity == 'complex':
                        complexity_score = max(complexity_score, 3)
        
        # Determine final complexity
        if complexity_score >= 3:
            detected_complexity = 'complex'
        elif complexity_score >= 2:
            detected_complexity = 'moderate'
        
        # Adjust based on message length
        word_count = len(message.split())
        if word_count > 15:
            detected_complexity = 'complex'
        elif word_count > 8:
            detected_complexity = 'moderate'
        
        return {
            'complexity': detected_complexity,
            'score': complexity_score,
            'word_count': word_count,
            'estimated_response_tokens': 150 if detected_complexity == 'simple' else 250 if detected_complexity == 'moderate' else 350,
            'suggested_format': 'list' if detected_complexity == 'moderate' else 'paragraph'
        }

    def validate_response_structure(self, response: str, language: str) -> Dict[str, Any]:
        """Validate response structure and suggest improvements"""
        
        required_sections = ["**Thinking**", "**Answer**", "**Source**"]
        found_sections = []
        
        for section in required_sections:
            if section in response:
                found_sections.append(section)
        
        # Count references
        reference_pattern = r'\[.*?\]'
        references = re.findall(reference_pattern, response)
        
        # Check for repetition
        lines = response.split('\n')
        duplicate_lines = len(lines) - len(set(lines))
        
        # Language consistency check
        is_language_consistent = self._check_language_consistency(response, language)
        
        validation_result = {
            'is_valid': len(found_sections) == 3,
            'found_sections': found_sections,
            'missing_sections': [s for s in required_sections if s not in found_sections],
            'reference_count': len(references),
            'has_duplicates': duplicate_lines > 0,
            'language_consistent': is_language_consistent,
            'response_length': len(response),
            'structure_score': len(found_sections) * 33.33  # Percentage score
        }
        
        return validation_result

    def _check_language_consistency(self, text: str, expected_language: str) -> bool:
        """Check if response maintains language consistency"""
        try:
            # Sample check on key phrases
            sample_text = text[:200]  # Check first 200 chars
            detected = detect(sample_text)
            return detected == expected_language or expected_language == 'en'  # English as fallback
        except:
            return True  # Assume consistent if detection fails

    async def optimize_response_for_language(self, response: str, language: str) -> str:
        """Optimize response formatting based on language requirements"""
        
        if language == 'ar':
            # Arabic text improvements
            response = response.replace('**Thinking**', '**التفكير**')
            response = response.replace('**Answer**', '**الإجابة**')
            response = response.replace('**Source**', '**المصدر**')
            
        elif language == 'fr':
            # French text improvements
            response = response.replace('**Thinking**', '**Réflexion**')
            response = response.replace('**Answer**', '**Réponse**')
            response = response.replace('**Source**', '**Source**')
        
        return response

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for monitoring"""
        return {
            "total_sessions": len(self.clarification_count),
            "average_clarifications_per_session": sum(self.clarification_count.values()) / max(len(self.clarification_count), 1),
            "current_language_distribution": {
                "current": self.current_language,
                "supported": ["en", "fr", "ar"]
            },
            "model_performance": {
                "model_loaded": self.model is not None,
                "fast_mode_enabled": self.fast_mode,
                "rag_chunks_available": len(self.document_chunks)
            },
            "response_optimization": {
                "max_tokens_per_response": 500,
                "average_response_time_target": "< 2 seconds",
                "stop_conditions_active": True
            }
        }