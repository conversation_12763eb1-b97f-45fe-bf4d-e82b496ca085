import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'core/theme/app_theme.dart';
import 'core/constants/app_constants.dart';
import 'core/api_gateway/api_gateway.dart';
import 'presentation/viewmodels/auth_viewmodel.dart';
import 'presentation/viewmodels/chat_viewmodel.dart';
import 'presentation/views/login_screen.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Initialize API Gateway - single point of communication with backend
    final apiGateway = ApiGateway();
    
    return MultiProvider(
      providers: [
        // ViewModels with API Gateway injection
        ChangeNotifierProvider<AuthViewModel>(
          create: (_) => AuthViewModel(apiGateway)..initialize(),
        ),
        ChangeNotifierProxyProvider<AuthViewModel, ChatViewModel>(
          create: (_) => ChatViewModel(apiGateway),
          update: (context, authViewModel, chatViewModel) {
            // Update API Gateway with auth token when user logs in
            if (authViewModel.isLoggedIn && authViewModel.user != null) {
              // Set auth token in API Gateway
              apiGateway.setAuthToken(authViewModel.authToken);
              
              // Update chat viewmodel with auth info
              chatViewModel?.updateAuth(
                authViewModel.user!.id, 
                authViewModel.authToken ?? '',
              );
            } else if (!authViewModel.isLoggedIn) {
              // Clear auth when user logs out
              apiGateway.setAuthToken(null);
              chatViewModel?.clearAuth();
            }
            return chatViewModel ?? ChatViewModel(apiGateway);
          },
        ),
      ],
      child: MaterialApp(
        title: AppConstants.appName,
        theme: AppTheme.lightTheme,
        home: const LoginScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}