import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../viewmodels/auth_viewmodel.dart';
import '../widgets/common/yonnovia_button.dart';
import '../widgets/common/yonnovia_card.dart';
import '../widgets/auth/register_form.dart';
import '../widgets/auth/feature_icon.dart';
import '../../core/constants/app_colors.dart';
import 'login_screen.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  _RegisterScreenState createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Container(
                constraints: const BoxConstraints(maxWidth: 450),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Welcome Section with Illustration
                    YonnoviaGradientCard(
                      padding: const EdgeInsets.all(40),
                      gradient: const LinearGradient(
                        colors: [Colors.white, AppColors.lightGray],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      child: Column(
                        children: [
                          // Team Illustration
                          Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: AppColors.primaryTeal.withOpacity(0.15),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: AppColors.primaryTeal.withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: Image.asset(
                              'assets/images/team.png',
                              width: 80,
                              height: 80,
                              errorBuilder: (context, error, stackTrace) {
                                return Icon(
                                  Icons.group_add,
                                  size: 50,
                                  color: AppColors.primaryTeal,
                                );
                              },
                            ),
                          ),
                          const SizedBox(height: 24),
                          // App Logo
                          Image.asset(
                            'assets/images/logo_light_mode.png',
                            width: 280,
                            height: 75,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                                decoration: BoxDecoration(
                                  color: AppColors.primaryTeal.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: AppColors.primaryTeal.withOpacity(0.3),
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.business_center,
                                      size: 30,
                                      color: AppColors.primaryTeal,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'YonnovIA',
                                      style: TextStyle(
                                        fontSize: 24,
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.primaryTeal,
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                          const SizedBox(height: 20),
                          const Text(
                            'Join PM Assistant',
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textPrimary,
                              letterSpacing: 0.5,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [AppColors.primaryTeal, AppColors.secondaryGreen],
                              ),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Text(
                              'Create Your Account',
                              style: TextStyle(
                                fontSize: 14,
                                color: AppColors.darkGray,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ),
                          const SizedBox(height: 12),
                          const Text(
                            'Start your intelligent project management journey',
                            style: TextStyle(
                              fontSize: 16,
                              color: AppColors.textSecondary,
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          // Feature Icons
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              FeatureIcon(assetPath: 'assets/images/teamwork.png', label: 'Collaborate'),
                              const SizedBox(width: 20),
                              FeatureIcon(assetPath: 'assets/images/efficient.png', label: 'Efficient'),
                              const SizedBox(width: 20),
                              FeatureIcon(assetPath: 'assets/images/goal.png', label: 'Goal-Oriented'),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 48),
                    // Registration Form
                    YonnoviaCard(
                      padding: const EdgeInsets.all(36),
                      elevated: true,
                      child: Consumer<AuthViewModel>(
                        builder: (context, authViewModel, child) {
                          return RegisterForm(
                            isLoading: authViewModel.isLoading,
                            errorMessage: authViewModel.errorMessage,
                            onRegister: (name, email, password, confirmPassword, role) async {
                              final success = await authViewModel.register(
                                name: name,
                                email: email,
                                password: password,
                                confirmPassword: confirmPassword,
                                role: role,
                              );

                              if (success) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Account created successfully! Please login.'),
                                    backgroundColor: AppColors.success,
                                  ),
                                );

                                // Navigate back to login
                                Navigator.pushReplacement(
                                  context,
                                  MaterialPageRoute(builder: (_) => const LoginScreen()),
                                );
                              }
                            },
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 24),
                    // Back to Login Button
                    YonnoviaButton(
                      text: 'Already have an account? Sign In',
                      icon: Icons.login,
                      isOutlined: true,
                      onPressed: () {
                        Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(builder: (_) => const LoginScreen()),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
