#!/usr/bin/env python3
"""
Project Management Assistant - Clean Server
==========================================
Refactored main server using proper service architecture and router modules.
Follows microservices pattern with clean separation of concerns.
"""

import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Import routers
from routers.auth_router import router as auth_router
from routers.chat_router import router as chat_router
from routers.admin_router import router as admin_router
# from routers.admin_documents_router import router as admin_documents_router  # Disabled to fix CPU overload

# Import services for initialization
from services.auth_service import AuthService
from services.session_service import SessionService
from services.message_service import MessageService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(
    title="PM Assistant Business Logic API",
    description="Clean microservices architecture for Project Management Assistant",
    version="2.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth_router)
app.include_router(chat_router)
app.include_router(admin_router)
# app.include_router(admin_documents_router)  # Disabled to fix CPU overload


@app.on_event("startup")
async def startup_event():
    """Initialize services and test data on startup"""
    logger.info("Starting PM Assistant Business Logic API v2.0.0")
    
    # Initialize services
    auth_service = AuthService()
    session_service = SessionService()
    message_service = MessageService()
    
    # Initialize test data
    await auth_service.init_test_data()
    await session_service.init_test_data()
    
    logger.info("Services initialized successfully")


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "PM Assistant Business Logic API",
        "version": "2.0.0"
    }


@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "PM Assistant Business Logic API",
        "version": "2.0.0",
        "architecture": "Clean microservices with router modules",
        "endpoints": {
            "authentication": "/auth/*",
            "chat": "/chat/*", 
            "admin": "/admin/*",
            "health": "/health"
        }
    }


if __name__ == "__main__":
    uvicorn.run(
        "server_clean:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
