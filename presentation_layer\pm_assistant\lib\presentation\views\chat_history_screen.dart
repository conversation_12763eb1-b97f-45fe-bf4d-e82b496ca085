import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_colors.dart';
import '../../models/conversation.dart';
import '../viewmodels/chat_viewmodel.dart';
import '../widgets/common/yonnovia_header.dart';
import '../widgets/common/yonnovia_card.dart';
import '../widgets/common/yonnovia_text_field.dart';
import '../widgets/common/yonnovia_button.dart';
import '../widgets/history/history_stat_item.dart';
import '../widgets/history/conversation_card.dart';
import 'chat_screen.dart';

class ChatHistoryScreen extends StatefulWidget {
  const ChatHistoryScreen({super.key});

  @override
  _ChatHistoryScreenState createState() => _ChatHistoryScreenState();
}

class _ChatHistoryScreenState extends State<ChatHistoryScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    // Load conversations when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final chatViewModel = Provider.of<ChatViewModel>(context, listen: false);
      if (chatViewModel.isAuthenticated) {
        chatViewModel.loadConversations();
      }
    });
    
    // Listen to search changes
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            YonnoviaHeader(
              title: 'Conversation History',
              onBackPressed: () => Navigator.pop(context),
              actions: [
                IconButton(
                  icon: Icon(Icons.refresh, color: Colors.white),
                  onPressed: () => _refreshConversations(),
                ),
                IconButton(
                  icon: Icon(Icons.analytics, color: Colors.white),
                  onPressed: () => _showStatsDialog(),
                ),
              ],
            ),
            
            // Content
            Expanded(
              child: Consumer<ChatViewModel>(
                builder: (context, chatViewModel, child) {
                  if (!chatViewModel.isAuthenticated) {
                    return _buildNotAuthenticatedView();
                  }

                  if (chatViewModel.isLoading) {
                    return _buildLoadingView();
                  }

                  if (chatViewModel.errorMessage != null) {
                    return _buildErrorView(chatViewModel.errorMessage!, chatViewModel);
                  }

                  if (chatViewModel.isNewUser) {
                    return _buildNewUserView();
                  }

                  return _buildConversationsList(chatViewModel);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotAuthenticatedView() {
    return Center(
      child: YonnoviaCard(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.login,
              size: 80,
              color: AppColors.primaryTeal,
            ),
            SizedBox(height: 24),
            Text(
              'Please Log In',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            SizedBox(height: 16),
            Text(
              'You need to be logged in to view conversation history.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: 24),
            YonnoviaButton(
              text: 'Go to Login',
              onPressed: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: AppColors.primaryTeal),
          SizedBox(height: 16),
          Text(
            'Loading conversations...',
            style: TextStyle(color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(String errorMessage, ChatViewModel chatViewModel) {
    return Center(
      child: YonnoviaCard(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: AppColors.error,
            ),
            SizedBox(height: 16),
            Text(
              'Error Loading Conversations',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            SizedBox(height: 8),
            Text(
              errorMessage,
              textAlign: TextAlign.center,
              style: TextStyle(color: AppColors.textSecondary),
            ),
            SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                YonnoviaButton(
                  text: 'Retry',
                  onPressed: () => chatViewModel.loadConversations(),
                ),
                YonnoviaButton(
                  text: 'Clear Error',
                  isSecondary: true,
                  onPressed: () => chatViewModel.clearError(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNewUserView() {
    return Center(
      child: YonnoviaCard(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 80,
              color: AppColors.primaryTeal,
            ),
            SizedBox(height: 24),
            Text(
              'No Conversations Yet',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            SizedBox(height: 16),
            Text(
              'Start a conversation to see your chat history here.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: 24),
            // Note: Start conversation via main chat interface
          ],
        ),
      ),
    );
  }

  Widget _buildConversationsList(ChatViewModel chatViewModel) {
    final conversations = _getFilteredConversations(chatViewModel);
    
    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          // Search bar
          YonnoviaTextField(
            controller: _searchController,
            hintText: 'Search conversations...',
            prefixIcon: Icon(Icons.search),
            suffixIcon: _searchQuery.isNotEmpty 
              ? IconButton(
                  icon: Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                )
              : null,
          ),
          
          SizedBox(height: 16),
          
          // Action buttons
          Row(
            children: [
              Expanded(
                child: YonnoviaButton(
                  text: 'Stats',
                  onPressed: () => _showStatsDialog(),
                  isSecondary: true,
                ),
              ),
            ],
          ),
          
          SizedBox(height: 16),
          
          // Conversations list
          Expanded(
            child: conversations.isEmpty
              ? _buildEmptySearchResults()
              : ListView.builder(
                  itemCount: conversations.length,
                  itemBuilder: (context, index) {
                    final conversation = conversations[index];
                    return ConversationCard(
                      conversation: conversation,
                      onTap: () => _openConversation(conversation, chatViewModel),
                      onDelete: () => _deleteConversation(conversation.id, chatViewModel),
                      onArchive: () => _archiveConversation(conversation.id, chatViewModel),
                      onExport: () => _exportConversation(conversation, chatViewModel),
                    );
                  },
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptySearchResults() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: AppColors.textSecondary,
          ),
          SizedBox(height: 16),
          Text(
            'No conversations found',
            style: TextStyle(
              fontSize: 18,
              color: AppColors.textSecondary,
            ),
          ),
          if (_searchQuery.isNotEmpty) ...[
            SizedBox(height: 8),
            Text(
              'Try a different search term',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ],
      ),
    );
  }

  List<Conversation> _getFilteredConversations(ChatViewModel chatViewModel) {
    if (_searchQuery.isEmpty) {
      return chatViewModel.conversations;
    }
    return chatViewModel.searchConversations(_searchQuery);
  }

  void _openConversation(Conversation conversation, ChatViewModel chatViewModel) async {
    await chatViewModel.selectConversation(conversation);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatScreen(),
      ),
    );
  }

  void _deleteConversation(String conversationId, ChatViewModel chatViewModel) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Conversation'),
        content: Text('Are you sure you want to delete this conversation? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await chatViewModel.deleteConversation(conversationId);
              if (chatViewModel.errorMessage == null) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Conversation deleted'),
                    backgroundColor: AppColors.error,
                  ),
                );
              }
            },
            child: Text('Delete', style: TextStyle(color: AppColors.error)),
          ),
        ],
      ),
    );
  }

  void _archiveConversation(String conversationId, ChatViewModel chatViewModel) async {
    await chatViewModel.archiveConversation(conversationId);
    if (chatViewModel.errorMessage == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Conversation archived'),
          backgroundColor: AppColors.primaryTeal,
        ),
      );
    }
  }

  void _exportConversation(Conversation conversation, ChatViewModel chatViewModel) {
    final exportText = chatViewModel.exportConversation(conversation);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Export Conversation'),
        content: SingleChildScrollView(
          child: SelectableText(exportText),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showStatsDialog() async {
    final chatViewModel = Provider.of<ChatViewModel>(context, listen: false);
    
    // Ensure conversations are loaded before calculating stats
    await chatViewModel.refreshConversations();
    final stats = await chatViewModel.getConversationStats();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Conversation Statistics'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            HistoryStatItem(
              icon: Icons.chat,
              label: 'Total Conversations',
              value: stats['totalConversations'].toString(),
            ),
            HistoryStatItem(
              icon: Icons.message,
              label: 'Total Messages',
              value: stats['totalMessages'].toString(),
            ),
            HistoryStatItem(
              icon: Icons.person,
              label: 'User Messages',
              value: stats['userMessages'].toString(),
            ),
            HistoryStatItem(
              icon: Icons.assistant,
              label: 'Assistant Messages',
              value: stats['assistantMessages'].toString(),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }

  void _refreshConversations() {
    final chatViewModel = Provider.of<ChatViewModel>(context, listen: false);
    chatViewModel.refreshConversations();
  }
}
