# Python
__pycache__
*.pyc
*.pyo
*.pyd
*.pdb
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.pytest_cache/

# Environment files
.env
.env.local
.env.*.local
.env.dev
.env.prod

# IDE and editors
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Documentation and demos
business_logic_layer/Docs/*.pdf
demos/
documentation/

# Models (too large for Docker context)
business_logic_layer/models/*.gguf
*.gguf

# Version control
.git/
.github/
.gitignore
.gitattributes

# Docker
dockerfile*
docker-compose*.yml
.dockerignore

# Build artifacts
build/
dist/
*.egg-info/

# Temporary files
tmp/
temp/
*.tmp

# Backup files
*.bak
*.backup
backups/

# Scripts (not needed in container)
scripts/
Makefile

# Flutter/Dart (presentation layer)
presentation_layer/
*.dart
pubspec.*

# AI layer
AI\ layer/

# Data layer
data\ layer/

# Diagrams
diagrams/