import 'package:flutter/material.dart';
import '../../models/message.dart';
import '../../models/conversation.dart';
import '../../core/api_gateway/api_gateway.dart';

/// ChatViewModel following MVVM pattern and SOLID principles
/// Follows Single Responsibility Principle - handles only chat state
/// Uses API Gateway for communication with business logic layer
class ChatViewModel extends ChangeNotifier {
  final ApiGateway _apiGateway;
  
  List<Message> _messages = [];
  List<Conversation> _conversations = [];
  bool _isLoading = false;
  bool _isSendingMessage = false;
  String? _errorMessage;
  Conversation? _currentConversation;
  String? _currentUserId;
  String? _authToken;

  ChatViewModel(this._apiGateway);

  // Getters
  List<Message> get messages => List.unmodifiable(_messages);
  List<Conversation> get conversations => List.unmodifiable(_conversations);
  bool get isLoading => _isLoading;
  bool get isSendingMessage => _isSendingMessage;
  String? get errorMessage => _errorMessage;
  Conversation? get currentConversation => _currentConversation;
  bool get hasMessages => _messages.isNotEmpty;
  bool get hasConversations => _conversations.isNotEmpty;
  bool get isNewUser => isAuthenticated && _conversations.isEmpty && !_isLoading;
  bool get isAuthenticated => _authToken != null;

  /// Update authentication token and user info
  void updateAuth(String userId, String token) {
    _currentUserId = userId;
    _authToken = token;
    
    // Update the API Gateway with the new auth token
    _apiGateway.setAuthToken(token);
    
    // Load conversations automatically when authenticated
    loadConversations();
    
    notifyListeners();
  }

  /// Clear authentication
  void clearAuth() {
    _currentUserId = null;
    _authToken = null;
    _messages.clear();
    _conversations.clear();
    _currentConversation = null;
    notifyListeners();
  }

  /// Initialize chat with demo messages
  void initializeChat() {
    _messages = [
      Message.assistant(
        text: 'Hello! I\'m your PM Assistant. How can I help you with your project management tasks today?',
      ),
    ];
    notifyListeners();
  }

  /// Send a user message and get assistant response with real-time streaming
  Future<void> sendMessageStream(String content) async {
    if (content.trim().isEmpty) return;

    // Check if user is authenticated
    if (!isAuthenticated) {
      _setError('Please log in to send messages');
      return;
    }

    _isSendingMessage = true;
    _clearError();
    notifyListeners();

    try {
      // Add user message immediately for better UX
      final userMessage = Message.user(text: content.trim());
      _messages.add(userMessage);
      notifyListeners();

      // Create new conversation if none exists
      String sessionId;
      if (_currentConversation == null) {
        sessionId = DateTime.now().millisecondsSinceEpoch.toString();
        final conversation = Conversation.create(
          id: sessionId,
          title: 'New Conversation',
        );
        _currentConversation = conversation;
      } else {
        sessionId = _currentConversation!.id;
      }

      // Create assistant message placeholder for streaming
      final assistantMessage = Message.assistant(text: '');
      _messages.add(assistantMessage);
      notifyListeners();

      print('🚀 Starting streaming for: ${content.trim()}');
      print('🔑 Session ID: $sessionId');

      // Use working send endpoint and simulate streaming
      try {
        final response = await _apiGateway.sendMessage(
          sessionId: sessionId,
          message: content.trim(),
        );

        if (response['success'] == true && response['data'] != null) {
          final fullResponse = response['data']['response'] ?? '';

          // Simulate streaming by showing words progressively
          final words = fullResponse.split(' ');
          String accumulatedResponse = '';

          for (int i = 0; i < words.length; i++) {
            accumulatedResponse += words[i] + ' ';

            // Update the assistant message with accumulated text
            if (_messages.isNotEmpty && !_messages.last.isUser) {
              _messages.last = _messages.last.copyWith(text: accumulatedResponse.trim());
              notifyListeners();
            }

            // Small delay for streaming effect
            await Future.delayed(const Duration(milliseconds: 50));
          }

          // Final update with complete response
          if (_messages.isNotEmpty && !_messages.last.isUser) {
            _messages.last = _messages.last.copyWith(text: fullResponse);
            notifyListeners();
          }
        } else {
          throw Exception(response['message'] ?? 'Failed to get response');
        }
      } catch (e) {
        // Fallback to old streaming method
        String accumulatedResponse = '';
        await for (final chunk in _apiGateway.streamMessage(
          sessionId: sessionId,
          message: content.trim(),
        )) {
        if (chunk.containsKey('chunk') && chunk['chunk'] != null) {
          accumulatedResponse += chunk['chunk'];

          // Update the last message (assistant) with accumulated text
          if (_messages.isNotEmpty && !_messages.last.isUser) {
            _messages[_messages.length - 1] = Message.assistant(
              text: accumulatedResponse,
            );
            notifyListeners(); // Update UI immediately with each token
          }
        } else if (chunk.containsKey('completed') && chunk['completed'] == true) {
          print('✅ Streaming completed');
          break;
        } else if (chunk.containsKey('error')) {
          throw Exception(chunk['error']);
        }
      }

      // Update conversation with final messages
      if (_currentConversation != null) {
        _currentConversation = _currentConversation!.addMessage(userMessage);
        final finalResponse = _messages.isNotEmpty && !_messages.last.isUser
            ? _messages.last.text
            : 'No response received';
        _currentConversation = _currentConversation!.addMessage(
          Message.assistant(text: finalResponse)
        );
      }

    } catch (e) {
      print('❌ Streaming error: ${e.toString()}');

      // Remove the placeholder assistant message on error
      if (_messages.isNotEmpty && !_messages.last.isUser && _messages.last.text.isEmpty) {
        _messages.removeLast();
      }

      String errorMsg = 'Failed to send message: ${e.toString()}';
      if (e.toString().contains('401') || e.toString().contains('Unauthorized')) {
        errorMsg = 'Your session has expired. Please log in again.';
      }

      _setError(errorMsg);
    } finally {
      _isSendingMessage = false;
      notifyListeners();
    }
  }

  /// Send a user message and get assistant response (non-streaming fallback)
  Future<void> sendMessage(String content) async {
    if (content.trim().isEmpty) return;

    // Check if user is authenticated
    if (!isAuthenticated) {
      _setError('Please log in to send messages');
      return;
    }

    _isSendingMessage = true;
    _clearError();
    notifyListeners();

    try {
      // Add user message immediately for better UX
      final userMessage = Message.user(text: content.trim());
      _messages.add(userMessage);
      notifyListeners();

      // Create new conversation if none exists
      String sessionId;
      if (_currentConversation == null) {
        // Create a new conversation for this message
        sessionId = DateTime.now().millisecondsSinceEpoch.toString();
        final conversation = Conversation.create(
          id: sessionId,
          title: 'New Conversation',
        );
        _currentConversation = conversation;
      } else {
        sessionId = _currentConversation!.id;
      }

      // Send message to backend via API Gateway
      print('💬 Sending message: ${content.trim()}');
      print('🔑 Session ID: $sessionId');
      print('🔐 Auth token available: ${_authToken != null}');

      final result = await _apiGateway.sendMessage(
        sessionId: sessionId,
        message: content.trim(),
      );

      print('📨 API Response: $result');
      
      if (result.containsKey('response')) {
        final assistantMessage = Message.assistant(
          text: result['response'] ?? 'Sorry, I couldn\'t process your request.',
        );
        
        // Add assistant message
        _messages.add(assistantMessage);
        
        // Update current conversation if exists
        if (_currentConversation != null) {
          _currentConversation = _currentConversation!.addMessage(userMessage);
          _currentConversation = _currentConversation!.addMessage(assistantMessage);
        }
      } else {
        // Remove user message if failed to get response
        _messages.removeLast();
        
        // Check for specific authentication errors
        String errorMsg = result['error'] ?? 'Failed to get response';
        if (errorMsg.contains('401') || errorMsg.contains('Unauthorized')) {
          errorMsg = 'Authentication failed. Please log in again.';
        }
        
        throw Exception(errorMsg);
      }
      
    } catch (e) {
      print('❌ Chat error: ${e.toString()}');
      String errorMsg = 'Failed to send message: ${e.toString()}';

      // Handle authentication errors specifically
      if (e.toString().contains('401') || e.toString().contains('Unauthorized')) {
        errorMsg = 'Your session has expired. Please log in again.';
      }

      print('❌ Setting error: $errorMsg');
      _setError(errorMsg);
      
      // Remove user message if it was added
      if (_messages.isNotEmpty && _messages.last.isUser) {
        _messages.removeLast();
      }
    } finally {
      _isSendingMessage = false;
      notifyListeners();
    }
  }

  /// Clear all messages
  void clearMessages() {
    _messages.clear();
    _currentConversation?.clearMessages();
    notifyListeners();
  }

  /// Load conversation history
  Future<void> loadConversations() async {
    _setLoading(true);
    _clearError();

    try {
      if (!isAuthenticated || _currentUserId == null) {
        _conversations = [];
        _setLoading(false);
        return;
      }

      final result = await _apiGateway.getConversations();
      
      if (result['success'] == true) {
        final List<dynamic> conversationsData = result['conversations'] ?? [];
        
        _conversations = conversationsData.map((data) {
          // Handle both backend JSON structure and ensure proper message count
          if (data is Map<String, dynamic>) {
            return Conversation.fromJson(data);
          }
          // Fallback for unexpected data structure
          return Conversation.create(title: 'Unknown Conversation');
        }).toList();
      } else {
        throw Exception(result['error'] ?? 'Failed to load conversations');
      }
      
      _setLoading(false);
    } catch (e) {
      _setError('Failed to load conversations: ${e.toString()}');
      _setLoading(false);
    }
  }

  /// Load a specific conversation with all its messages
  Future<void> loadConversationDetail(String conversationId) async {
    _setLoading(true);
    _clearError();

    try {
      if (!isAuthenticated || _currentUserId == null) {
        _setError('Please log in to load conversation');
        _setLoading(false);
        return;
      }

      final result = await _apiGateway.getConversationHistory(conversationId);
      
      if (result['success'] == true && result['conversation'] != null) {
        final conversation = Conversation.fromJson(result['conversation']);
        _currentConversation = conversation;
        _messages = List.from(conversation.messages);
        
        // Update the conversation in the list if it exists
        final index = _conversations.indexWhere((c) => c.id == conversationId);
        if (index != -1) {
          _conversations[index] = conversation;
        }
      } else {
        _setError(result['error'] ?? 'Conversation not found');
      }
      
      _setLoading(false);
    } catch (e) {
      _setError('Failed to load conversation: ${e.toString()}');
      _setLoading(false);
    }
  }

  /// Refresh conversations from backend
  Future<void> refreshConversations() async {
    await loadConversations();
  }

  /// Start a new conversation (clears current chat)
  void startNewConversation() {
    _currentConversation = null;
    _messages.clear();
    initializeChat();
    notifyListeners();
  }

  /// Select a conversation and load its messages
  Future<void> selectConversation(Conversation conversation) async {
    // If conversation has messages, use them directly
    if (conversation.messages.isNotEmpty) {
      _currentConversation = conversation;
      _messages = List.from(conversation.messages);
      notifyListeners();
    } else {
      // Load full conversation from backend
      await loadConversationDetail(conversation.id);
    }
  }

  /// Create a new conversation
  Future<void> createNewConversation({String? title}) async {
    try {
      // Create a simple local conversation for now
      final conversation = Conversation.create(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: title ?? 'New Conversation',
      );
      
      _currentConversation = conversation;
      _messages.clear();
      initializeChat();
      
      if (!_conversations.any((c) => c.id == conversation.id)) {
        _conversations.add(conversation);
      }
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to create conversation: ${e.toString()}');
    }
  }

  /// Update current conversation
  Future<void> updateConversation() async {
    if (_currentConversation == null) return;

    try {
      // TODO: Implement updateConversation in service interface
      // For now, just update locally
      final updatedConversation = _currentConversation!.copyWith(
        messages: List.from(_messages),
        updatedAt: DateTime.now(),
      );
      
      // Update the conversation in the list
      final index = _conversations.indexWhere((c) => c.id == updatedConversation.id);
      if (index != -1) {
        _conversations[index] = updatedConversation;
      }
      
      _currentConversation = updatedConversation;
      notifyListeners();
    } catch (e) {
      _setError('Failed to update conversation: ${e.toString()}');
    }
  }

  /// Archive a conversation
  Future<void> archiveConversation(String conversationId) async {
    try {
      if (!isAuthenticated || _currentUserId == null) {
        _setError('Please log in to archive conversations');
        return;
      }

      // Archive functionality not implemented in API Gateway yet
      // Using delete as temporary solution
      final result = await _apiGateway.deleteConversation(conversationId);
      
      if (result['success'] == true) {
        // Update the conversation status locally
        final index = _conversations.indexWhere((c) => c.id == conversationId);
        if (index != -1) {
          _conversations[index] = _conversations[index].copyWith(
            updatedAt: DateTime.now(),
          );
        }
        
        // If current conversation was archived, clear it but don't auto-create new one
        if (_currentConversation?.id == conversationId) {
          _currentConversation = null;
          _messages.clear();
        }
        
        notifyListeners();
      } else {
        _setError(result['error'] ?? 'Failed to archive conversation');
      }
    } catch (e) {
      _setError('Failed to archive conversation: ${e.toString()}');
    }
  }

  /// Delete a conversation
  Future<void> deleteConversation(String conversationId) async {
    try {
      if (!isAuthenticated || _currentUserId == null) {
        _setError('Please log in to delete conversations');
        return;
      }

      final result = await _apiGateway.deleteConversation(conversationId);
      
      if (result['success'] == true) {
        // Remove from local list
        _conversations.removeWhere((c) => c.id == conversationId);
        
        // If current conversation was deleted, clear it but don't auto-create new one
        if (_currentConversation?.id == conversationId) {
          _currentConversation = null;
          _messages.clear();
        }
        
        notifyListeners();
      } else {
        _setError(result['error'] ?? 'Failed to delete conversation');
      }
    } catch (e) {
      _setError('Failed to delete conversation: ${e.toString()}');
    }
  }

  /// Clear conversation messages
  Future<void> clearConversation(String conversationId) async {
    try {
      // TODO: Implement clearConversation in service interface
      // For now, just clear locally
      final index = _conversations.indexWhere((c) => c.id == conversationId);
      if (index != -1) {
        _conversations[index] = _conversations[index].copyWith(messages: []);
      }
      
      // If this is the current conversation, clear local messages
      if (_currentConversation?.id == conversationId) {
        _currentConversation = _currentConversation!.copyWith(messages: []);
        _messages.clear();
        initializeChat();
      }
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to clear conversation: ${e.toString()}');
    }
  }

  /// Search conversations by title or content
  List<Conversation> searchConversations(String query) {
    if (query.trim().isEmpty) return _conversations;

    final lowercaseQuery = query.trim().toLowerCase();
    return _conversations.where((conversation) {
      return conversation.title.toLowerCase().contains(lowercaseQuery) ||
             conversation.messages.any((message) => 
               message.text.toLowerCase().contains(lowercaseQuery));
    }).toList();
  }

  /// Get conversation statistics
  Future<Map<String, int>> getConversationStats() async {
    try {
      if (!isAuthenticated || _currentUserId == null) {
        _setError('Please log in to get conversation statistics');
        return {
          'totalConversations': 0,
          'totalMessages': 0,
          'userMessages': 0,
          'assistantMessages': 0,
        };
      }

      // Debug: Log conversation count
      print('DEBUG: Calculating stats for ${_conversations.length} conversations');
      
      // Calculate stats locally since API Gateway doesn't have this endpoint yet
      // Skip API call and go directly to local calculation
      
      // Fall back to local calculation
      int totalMessages = 0;
      int userMessages = 0;
      int assistantMessages = 0;
      
      for (final conversation in _conversations) {
        // Use messageCount from backend if available, otherwise count messages array
        final conversationMessageCount = conversation.messageCount ?? conversation.messages.length;
        print('DEBUG: Conversation ${conversation.id} has $conversationMessageCount messages (messageCount: ${conversation.messageCount}, array length: ${conversation.messages.length})');
        
        totalMessages += conversationMessageCount;
        
        // If we have the actual messages, count user vs assistant messages
        if (conversation.messages.isNotEmpty) {
          for (final message in conversation.messages) {
            if (message.isUser) {
              userMessages++;
            } else {
              assistantMessages++;
            }
          }
        } else if (conversationMessageCount > 0) {
          // If we don't have the actual messages but know the count,
          // we can't distinguish between user and assistant messages
          // For now, assume roughly half are user messages (this is an approximation)
          final estimatedUserMessages = (conversationMessageCount / 2).round();
          userMessages += estimatedUserMessages;
          assistantMessages += (conversationMessageCount - estimatedUserMessages);
        }
      }
      
      final stats = {
        'totalConversations': _conversations.length,
        'totalMessages': totalMessages,
        'userMessages': userMessages,
        'assistantMessages': assistantMessages,
      };
      
      print('DEBUG: Final stats: $stats');
      return stats;
    } catch (e) {
      _setError('Failed to get conversation stats: ${e.toString()}');
      return {
        'totalConversations': 0,
        'totalMessages': 0,
        'userMessages': 0,
        'assistantMessages': 0,
      };
    }
  }

  /// Export conversation as text
  String exportConversation(Conversation conversation) {
    final buffer = StringBuffer();
    buffer.writeln('Conversation: ${conversation.title}');
    buffer.writeln('Created: ${conversation.createdAt}');
    buffer.writeln('Updated: ${conversation.updatedAt}');
    buffer.writeln('Messages: ${conversation.messages.length}');
    buffer.writeln('=' * 50);
    
    for (final message in conversation.messages) {
      buffer.writeln();
      buffer.writeln('${message.isUser ? 'User' : 'Assistant'}: ${message.timestamp}');
      buffer.writeln(message.text);
      buffer.writeln('-' * 30);
    }
    
    return buffer.toString();
  }

  /// Clear current error
  void clearError() {
    _clearError();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

}
