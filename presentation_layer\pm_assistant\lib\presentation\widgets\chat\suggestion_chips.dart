import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';

/// Suggestion chips widget following Single Responsibility Principle
/// Handles only suggestion chips display UI logic
class SuggestionChips extends StatelessWidget {
  final Function(String) onSuggestionTap;

  const SuggestionChips({
    super.key,
    required this.onSuggestionTap,
  });

  @override
  Widget build(BuildContext context) {
    final suggestions = [
      'How to plan a sprint?',
      'Risk management tips',
      'Team productivity',
      'Budget tracking',
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: suggestions.map((suggestion) {
        return GestureDetector(
          onTap: () => onSuggestionTap(suggestion),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.surface,
              border: Border.all(color: AppColors.borderLight),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              suggestion,
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 14,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}
