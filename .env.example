# PM Assistant Environment Configuration
# Copy this file to .env and update the values

# Application Settings
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=false
LOG_LEVEL=INFO
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1

# Model Configuration
MODEL_PATH=/app/business_logic_layer/models/qwen25-7b-project-mgmt-q5_k_m.gguf
DOCS_FOLDER=/app/business_logic_layer/Docs

# RAG System Settings
VECTOR_STORE_PATH=pm_rag_store.pkl.gz
EMBEDDING_DIM=384
MAX_DOC_CHUNK_SIZE=800
CHUNK_OVERLAP=150
RELEVANCE_THRESHOLD=0.5
MIN_PM_KEYWORDS=2

# LLM Settings
LLM_MAX_TOKENS=800
LLM_TEMPERATURE=0.7
LLM_N_CTX=2048
LLM_N_THREADS=8
LLM_N_GPU_LAYERS=0

# Conversation Cache Settings
MAX_SESSIONS=1000
MAX_MESSAGES_PER_SESSION=50
SESSION_TIMEOUT_HOURS=12

# CORS Settings
CORS_ORIGINS=*
CORS_METHODS=*
CORS_HEADERS=*

# Database Settings (if using PostgreSQL)
POSTGRES_DB=pm_assistant
POSTGRES_USER=pm_user
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# Redis Settings (if using Redis)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Security Settings
SECRET_KEY=your_secret_key_here
JWT_SECRET_KEY=your_jwt_secret_here
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# File Upload Settings
MAX_FILE_SIZE=50MB
ALLOWED_FILE_TYPES=pdf,docx,txt,md

# Monitoring and Logging
ENABLE_METRICS=true
METRICS_PORT=9090
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
