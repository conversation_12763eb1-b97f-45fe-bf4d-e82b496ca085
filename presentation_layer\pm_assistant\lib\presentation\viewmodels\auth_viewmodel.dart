import 'package:flutter/material.dart';
import '../../models/user.dart';
import '../../core/api_gateway/api_gateway.dart';
import '../../core/utils/validation_utils.dart';

/// AuthViewModel following MVVM pattern and SOLID principles
/// Follows Single Responsibility Principle - handles only authentication state
/// Uses API Gateway to communicate with business logic layer
class AuthViewModel extends ChangeNotifier {
  final ApiGateway _apiGateway;
  
  User? _user;
  bool _isLoading = false;
  String? _errorMessage;
  String? _authToken;

  AuthViewModel(this._apiGateway);

  // Getters
  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String? get authToken => _authToken;
  bool get isLoggedIn => _user != null;
  bool get isAdmin => _user?.isAdmin ?? false;
  bool get isRegular => _user?.isRegular ?? false;
  ApiGateway get apiGateway => _apiGateway;

  /// Initialize ViewModel - check for existing session
  Future<void> initialize() async {
    _setLoading(true);
    try {
      final result = await _apiGateway.getCurrentUser();
      if (result['success'] == true && result['user'] != null) {
        _user = User.fromJson(result['user']);
        _clearError();
      }
    } catch (e) {
      _setError('Failed to load user session: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Login user with email and password
  Future<bool> login(String email, String password) async {
    _setLoading(true);
    _clearError();

    try {
      // Client-side validation
      if (email.trim().isEmpty) {
        throw Exception('Email cannot be empty');
      }

      if (password.trim().isEmpty) {
        throw Exception('Password cannot be empty');
      }

      if (!ValidationUtils.isValidEmail(email.trim())) {
        throw Exception('Please enter a valid email address');
      }

      final result = await _apiGateway.login(
        email: email.trim(),
        password: password.trim(),
      );
      
      // Handle successful response (success field check)
      if (result['success'] == true && result['user'] != null) {
        _user = User.fromJson(result['user']);
        _authToken = result['access_token'];
        _apiGateway.setAuthToken(_authToken);  // Set token in API Gateway
        _setLoading(false);
        return true;
      } else {
        // Handle error response
        throw Exception(result['error'] ?? 'Login failed');
      }
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  /// Register new user
  Future<bool> register({
    required String name,
    required String email,
    required String password,
    required String confirmPassword,
    required String role,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      // Client-side validation
      if (name.trim().isEmpty) {
        throw Exception('Name cannot be empty');
      }

      if (email.trim().isEmpty) {
        throw Exception('Email cannot be empty');
      }

      if (password.trim().isEmpty) {
        throw Exception('Password cannot be empty');
      }

      if (confirmPassword.trim().isEmpty) {
        throw Exception('Confirm password cannot be empty');
      }

      if (password != confirmPassword) {
        throw Exception('Passwords do not match');
      }

      if (!ValidationUtils.isValidEmail(email.trim())) {
        throw Exception('Please enter a valid email address');
      }

      if (!ValidationUtils.isValidPassword(password)) {
        throw Exception('Password must be at least 8 characters long');
      }

      final result = await _apiGateway.register(
        fullName: name.trim(),  // Updated to use fullName parameter
        email: email.trim(),
        password: password.trim(),
        role: role.trim(), // Pass the selected role from the registration form
      );
      
      if (result['success'] == true && result['user'] != null) {
        _user = User.fromJson(result['user']);
        if (result['access_token'] != null) {  // Updated to match backend response
          _authToken = result['access_token'];
          _apiGateway.setAuthToken(_authToken);  // Set token in API Gateway
        }
        _setLoading(false);
        return true;
      } else {
        throw Exception(result['error'] ?? 'Registration failed');
      }
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  /// Logout current user
  Future<void> logout() async {
    try {
      await _apiGateway.logout();
    } catch (e) {
      debugPrint('Logout error: $e');
    }
    _user = null;
    _authToken = null;
    _clearError();
    notifyListeners();
  }

  /// Clear current error
  void clearError() {
    _clearError();
  }

  /// Update user profile
  Future<bool> updateProfile({
    required String name,
    required String email,
  }) async {
    if (_user == null) return false;

    _setLoading(true);
    _clearError();

    try {
      // Client-side validation
      if (name.trim().isEmpty) {
        throw Exception('Name cannot be empty');
      }

      if (email.trim().isEmpty) {
        throw Exception('Email cannot be empty');
      }

      if (!ValidationUtils.isValidEmail(email.trim())) {
        throw Exception('Please enter a valid email address');
      }

      // TODO: Implement updateProfile in service interface
      // For now, just update local user data
      if (_user != null) {
        _user = _user!.copyWith(
          name: name.trim(),
          email: email.trim(),
        );
      }
      
      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  /// Validate session
  Future<bool> validateSession() async {
    try {
      final result = await _apiGateway.getCurrentUser();
      if (result['success'] == true && result['user'] != null) {
        _user = User.fromJson(result['user']);
        return true;
      } else {
        _user = null;
        return false;
      }
    } catch (e) {
      _user = null;
      return false;
    }
  }

  /// Get all users (admin functionality)
  Future<List<User>> getAllUsers() async {
    if (!isAdmin) return [];

    try {
      // TODO: Implement getAllUsers in service interface
      // For now, return empty list
      return [];
    } catch (e) {
      _setError('Failed to get users: ${e.toString()}');
      return [];
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

}
