#!/bin/bash

# High Performance PM Assistant Deployment for 15GB RAM, 8 vCPU VPS
echo "🚀 High Performance PM Assistant Deployment"
echo "==========================================="
echo "VPS Specs: 15GB RAM, 8 vCPU cores"
echo "Optimizations: Maximum speed and quality"
echo ""

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "docker-compose.vps.yml" ]; then
    print_error "docker-compose.vps.yml not found. Please run from project root."
    exit 1
fi

print_status "Checking system resources..."
echo "Memory: $(free -h | grep Mem | awk '{print $2}') total, $(free -h | grep Mem | awk '{print $7}') available"
echo "CPU cores: $(nproc)"
echo ""

# Check model file
if [ ! -f "business_logic_layer/models"/*.gguf ]; then
    print_warning "No .gguf model file found in business_logic_layer/models/"
    print_status "Please add your model file before continuing."
    read -p "Press Enter when model file is added..."
fi

print_status "Stopping existing containers..."
docker-compose -f docker-compose.vps.yml down 2>/dev/null || true

print_status "Building optimized containers for high performance..."
docker-compose -f docker-compose.vps.yml build --no-cache

print_status "Starting high-performance PM Assistant..."
docker-compose -f docker-compose.vps.yml up -d

print_status "Waiting for services to initialize..."
sleep 30

# Check health with retries
print_status "Checking service health..."
for i in {1..15}; do
    if curl -f http://localhost:8000/health &>/dev/null; then
        print_success "✅ PM Assistant is healthy and ready!"
        break
    else
        print_status "Waiting for service... (attempt $i/15)"
        sleep 5
    fi
    
    if [ $i -eq 15 ]; then
        print_error "❌ Health check failed after 15 attempts"
        print_status "Checking logs..."
        docker-compose -f docker-compose.vps.yml logs --tail=20 pm-assistant
        exit 1
    fi
done

# Test performance
print_status "Testing high-performance response..."
start_time=$(date +%s%N)
response=$(curl -s -X POST http://localhost:8000/chat \
    -H "Content-Type: application/json" \
    -d '{"message": "What is project management?"}' | head -1)
end_time=$(date +%s%N)
duration=$(( (end_time - start_time) / 1000000 ))

if echo "$response" | grep -q "response"; then
    print_success "✅ High-performance chat working! Response time: ${duration}ms"
else
    print_warning "⚠️ Chat test inconclusive"
fi

# Test streaming
print_status "Testing streaming performance..."
stream_test=$(timeout 10s curl -s -X POST http://localhost:8000/chat/stream \
    -H "Content-Type: application/json" \
    -d '{"message": "test"}' | head -3)

if echo "$stream_test" | grep -q "data:"; then
    print_success "✅ High-performance streaming working!"
else
    print_warning "⚠️ Streaming test inconclusive"
fi

# Show resource usage
print_status "Current resource usage:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" | grep pm-

# Show container status
print_status "Container status:"
docker-compose -f docker-compose.vps.yml ps

echo ""
print_success "🎉 High Performance PM Assistant deployed successfully!"
echo ""
echo "📊 Performance Configuration:"
echo "   CPU Threads: 7 of 8 cores"
echo "   Memory Limit: 12GB of 15GB"
echo "   Context Size: 1024 tokens"
echo "   Batch Size: 512 (high throughput)"
echo "   Memory Mapping: Enabled"
echo "   Memory Locking: Enabled"
echo "   Half Precision: Enabled"
echo ""
echo "📋 Access Information:"
echo "   Backend API: http://$(hostname -I | awk '{print $1}'):8000"
echo "   Health Check: http://$(hostname -I | awk '{print $1}'):8000/health"
echo "   API Docs: http://$(hostname -I | awk '{print $1}'):8000/docs"
echo ""
echo "🧪 Test Commands:"
echo "   Health: curl http://$(hostname -I | awk '{print $1}'):8000/health"
echo "   Chat: curl -X POST http://$(hostname -I | awk '{print $1}'):8000/chat -H 'Content-Type: application/json' -d '{\"message\": \"What is agile methodology?\"}'"
echo "   Stream: curl -X POST http://$(hostname -I | awk '{print $1}'):8000/chat/stream -H 'Content-Type: application/json' -d '{\"message\": \"Explain scrum framework\"}'"
echo ""
echo "🔧 Management Commands:"
echo "   Status: docker-compose -f docker-compose.vps.yml ps"
echo "   Logs: docker-compose -f docker-compose.vps.yml logs -f pm-assistant"
echo "   Restart: docker-compose -f docker-compose.vps.yml restart pm-assistant"
echo "   Stop: docker-compose -f docker-compose.vps.yml down"
echo "   Resources: docker stats --no-stream"
echo ""
echo "⚡ Performance Features:"
echo "   ✅ 7-core CPU utilization"
echo "   ✅ 12GB memory allocation"
echo "   ✅ Memory mapping optimization"
echo "   ✅ Large batch processing"
echo "   ✅ Half-precision acceleration"
echo "   ✅ NUMA optimization"
echo "   ✅ Aggressive stopping conditions"
echo "   ✅ RAG document integration"
echo ""
print_success "Your PM Assistant is now running at maximum performance! 🚀"
echo ""
echo "Expected performance:"
echo "   First token: 30-50ms"
echo "   Complete response: 2-4 seconds"
echo "   Concurrent users: 10-20"
echo "   Memory usage: 8-10GB"
echo "   CPU usage: 60-80%"
