import '../../models/conversation.dart';

/// Abstract interface for chat services
/// This allows different implementations (Mock, API, Hybrid) to be used interchangeably
/// Follows Interface Segregation Principle - focused on chat operations only
abstract class ChatServiceInterface {
  /// Send a message and get response
  Future<Map<String, dynamic>> sendMessage({
    required String message,
    String? sessionId,
    String? userId,
  });

  /// Get user conversations
  Future<List<Conversation>> getConversations({String? userId});

  /// Get detailed conversation with messages
  Future<Conversation?> getConversationDetail(String conversationId, {String? userId});

  /// Delete conversation
  Future<bool> deleteConversation(String conversationId, {String? userId});

  /// Archive conversation
  Future<bool> archiveConversation(String conversationId, {String? userId});

  /// Get conversation statistics
  Future<Map<String, dynamic>?> getConversationStats({String? userId});

  /// Set user session for API service
  void setUserSession(String userId, String sessionId, {String? token});
}
