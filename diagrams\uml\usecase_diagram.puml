@startuml ProjectManagementChatbot_UseCaseDiagram

!theme plain
left to right direction

skinparam actor {
    BackgroundColor #E6F3FF
    BorderColor #0066CC
}
skinparam usecase {
    BackgroundColor #F0F8FF
    BorderColor #0066CC
}
skinparam rectangle {
    BackgroundColor #FFFFFF
    BorderColor #0066CC
}

' Actors
actor "User" as User
actor "Admin" as Admin

' System boundary
rectangle "Project Management Chatbot System" {
    
    ' User Actions
    usecase "Ask Questions to Assistant" as UC1
    usecase "Receive Responses" as UC2
    usecase "Send Voice Requests" as UC3
    usecase "Access Conversation History" as UC4
    usecase "Receive Performance Alerts" as UC5
    
    ' Admin Actions
    usecase "Configure System Prompts" as UC6
    usecase "Monitor Assistant Performance" as UC7
}

' Relationships
User --> UC1
User --> UC2
User --> UC3
User --> UC4
User --> UC5

Admin --> UC6
Admin --> UC7

' Dependencies
UC1 ..> UC2 : <<extends>>
UC7 ..> UC5 : <<includes>>

' Notes
note right of UC1 : "Main interaction point\nfor users"
note bottom of UC7 : "Admin monitoring\nfunctions"

@enduml
