"""
Session Service
==============
Handles chat session creation, management, and persistence.
"""

import uuid
from datetime import datetime
from typing import Optional, Dict, Any, List

from fastapi import HTTPException

# In-memory storage
sessions_db = {}

class SessionService:
    
    async def get_session(self, session_id: str) -> Dict[str, Any]:
        """Get session by ID"""
        if session_id not in sessions_db:
            raise HTTPException(status_code=404, detail="Session not found")
        
        return sessions_db[session_id]
    
    async def get_user_sessions(self, user_id: str) -> List[Dict[str, Any]]:
        """Get user's chat sessions"""
        user_sessions = []
        for session in sessions_db.values():
            if session["user_id"] == user_id:
                user_sessions.append({
                    "session_id": session["session_id"],
                    "language": session["language"],
                    "created_at": session["created_at"],
                    "message_count": len(session["messages"]),
                    "active": session.get("active", True)
                })
        
        return user_sessions
    
    async def get_all_sessions(self) -> List[Dict[str, Any]]:
        """Get all sessions (admin only)"""
        all_sessions = []
        for session in sessions_db.values():
            all_sessions.append({
                "session_id": session["session_id"],
                "user_id": session["user_id"],
                "language": session["language"],
                "created_at": session["created_at"],
                "message_count": len(session["messages"]),
                "active": session.get("active", True)
            })
        
        return all_sessions
    
    async def add_message_to_session(self, session_id: str, user_id: str, message: str, is_user: bool = True) -> None:
        """Add a message to a session"""
        # Create session if it doesn't exist
        if session_id not in sessions_db:
            session = {
                "id": session_id,  # Changed from "session_id" to "id"
                "session_id": session_id,  # Keep both for compatibility
                "user_id": user_id,
                "title": f"Chat {datetime.utcnow().strftime('%Y-%m-%d %H:%M')}",
                "language": "en",
                "created_at": datetime.utcnow(),
                "last_activity": datetime.utcnow(),
                "messages": [],
                "active": True
            }
            sessions_db[session_id] = session
        
        # Update last activity
        sessions_db[session_id]["last_activity"] = datetime.utcnow()
        
        # Add message
        message_entry = {
            "content": message,
            "timestamp": datetime.utcnow(),
            "user_id": user_id,
            "is_user": is_user
        }
        
        sessions_db[session_id]["messages"].append(message_entry)
    
    async def close_session(self, session_id: str) -> None:
        """Mark a session as inactive/closed"""
        if session_id in sessions_db:
            sessions_db[session_id]["active"] = False
    
    async def create_session(self, user_id: str, title: Optional[str] = None) -> Dict[str, Any]:
        """Create a new conversation/session (updated signature)"""
        session_id = str(uuid.uuid4())
        
        session = {
            "id": session_id,
            "title": title or f"Conversation {datetime.utcnow().strftime('%Y-%m-%d %H:%M')}",
            "created_at": datetime.utcnow(),
            "last_activity": datetime.utcnow(),
            "user_id": user_id,
            "messages": [],
            "active": True
        }
        
        sessions_db[session_id] = session
        
        return {
            "id": session_id,
            "title": session["title"],
            "created_at": session["created_at"],
            "last_activity": session["last_activity"],
            "user_id": user_id
        }
    
    async def get_user_conversations(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all conversations for a user (updated return format)"""
        user_conversations = []
        for session_id, session in sessions_db.items():
            if session["user_id"] == user_id:
                last_message = ""
                if session["messages"]:
                    last_message = session["messages"][-1]["content"][:100]
                
                # Convert messages to frontend format
                formatted_messages = []
                for msg in session["messages"]:
                    formatted_messages.append({
                        "id": f"{session_id}_{len(formatted_messages)}",
                        "text": msg["content"],
                        "timestamp": msg["timestamp"].isoformat() if hasattr(msg["timestamp"], 'isoformat') else str(msg["timestamp"]),
                        "is_user": msg.get("is_user", True),
                        "user_id": msg.get("user_id", user_id)
                    })
                
                user_conversations.append({
                    "id": session.get("id", session_id),  # Handle both old and new format
                    "title": session.get("title", f"Chat {session.get('created_at', datetime.utcnow()).strftime('%Y-%m-%d %H:%M')}"),
                    "last_message": last_message,
                    "last_activity": session.get("last_activity", session.get("created_at", datetime.utcnow())),
                    "messages": formatted_messages,
                    "message_count": len(session["messages"]),
                    "user_id": user_id
                })
        
        return user_conversations
    
    async def get_conversation_detail(self, conversation_id: str, user_id: str) -> Dict[str, Any]:
        """Get conversation with all messages"""
        if conversation_id not in sessions_db:
            raise ValueError("Conversation not found")
        
        session = sessions_db[conversation_id]
        if session["user_id"] != user_id:
            raise ValueError("Access denied")
        
        # Convert messages to frontend format
        formatted_messages = []
        for msg in session["messages"]:
            formatted_messages.append({
                "id": f"{conversation_id}_{len(formatted_messages)}",
                "text": msg["content"],
                "timestamp": msg["timestamp"].isoformat() if hasattr(msg["timestamp"], 'isoformat') else str(msg["timestamp"]),
                "is_user": msg.get("is_user", True),
                "user_id": msg.get("user_id", user_id)
            })
        
        return {
            "success": True,
            "conversation": {
                "id": session.get("id", conversation_id),  # Handle both old and new format
                "title": session.get("title", f"Chat {session.get('created_at', datetime.utcnow()).strftime('%Y-%m-%d %H:%M')}"),
                "created_at": session["created_at"].isoformat() if hasattr(session["created_at"], 'isoformat') else str(session["created_at"]),
                "updated_at": session.get("last_activity", session["created_at"]).isoformat() if hasattr(session.get("last_activity", session["created_at"]), 'isoformat') else str(session.get("last_activity", session["created_at"])),
                "messages": formatted_messages,
                "user_id": user_id
            }
        }
    
    async def delete_conversation(self, conversation_id: str, user_id: str) -> bool:
        """Delete a conversation"""
        if conversation_id not in sessions_db:
            raise ValueError("Conversation not found")
        
        session = sessions_db[conversation_id]
        if session["user_id"] != user_id:
            raise ValueError("Access denied")
        
        del sessions_db[conversation_id]
        return True
    
    async def update_conversation_title(self, conversation_id: str, user_id: str, title: str) -> bool:
        """Update conversation title"""
        if conversation_id not in sessions_db:
            raise ValueError("Conversation not found")
        
        session = sessions_db[conversation_id]
        if session["user_id"] != user_id:
            raise ValueError("Access denied")
        
        sessions_db[conversation_id]["title"] = title
        sessions_db[conversation_id]["last_activity"] = datetime.utcnow()
        return True
    
    async def get_analytics_data(self) -> Dict[str, Any]:
        """Get analytics data for admin"""
        total_sessions = len(sessions_db)
        total_messages = sum(len(session["messages"]) for session in sessions_db.values())
        active_sessions = sum(1 for session in sessions_db.values() if session.get("active", True))
        
        return {
            "total_users": 0,  # Will be filled by auth service
            "total_sessions": total_sessions,
            "total_messages": total_messages,
            "active_users_today": 0,  # Placeholder
            "avg_response_time": 1.9,  # Average response time in seconds
            "average_session_length": total_messages / max(total_sessions, 1),
            "most_active_hours": [9, 10, 11, 14, 15, 16]  # Placeholder
        }
    
    async def get_active_session_count(self) -> int:
        """Get count of active sessions"""
        return sum(1 for session in sessions_db.values() if session.get("active", True))
    
    async def init_test_data(self):
        """Initialize test data"""
        # Test data will be created when users start chatting
        pass
