"""
Authentication Service
=====================
Handles user authentication, registration, token generation/validation.
"""

import os
import uuid
import hashlib
import base64
import json
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List

from fastapi import H<PERSON>PEx<PERSON>, Depends, Header, status

# Import models from the centralized models module
from models.user_models import UserCreate, UserLogin, User, TokenResponse

# In-memory storage (for testing purposes)
users_db = {}
active_tokens = {}

# Utility functions
def hash_password(password: str) -> str:
    """Hash password with SHA-256"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password: str, hashed: str) -> bool:
    """Verify password against hash"""
    return hash_password(password) == hashed

def create_access_token(user_id: str, role: str) -> str:
    """Create a simple token for testing"""
    token_data = {
        "user_id": user_id,
        "role": role,
        "expires": (datetime.utcnow() + timedelta(hours=24)).isoformat()
    }
    token_json = json.dumps(token_data)
    token_bytes = base64.b64encode(token_json.encode()).decode()
    
    # Store in active tokens
    active_tokens[token_bytes] = token_data
    
    return token_bytes

def verify_token(token: str) -> Dict[str, Any]:
    """Verify token and return payload"""
    if token not in active_tokens:
        raise HTTPException(status_code=401, detail="Invalid token")
    
    token_data = active_tokens[token]
    expires = datetime.fromisoformat(token_data["expires"])
    
    if datetime.utcnow() > expires:
        del active_tokens[token]
        raise HTTPException(status_code=401, detail="Token expired")
    
    return token_data

async def get_current_user(authorization: Optional[str] = Header(None)) -> Dict[str, Any]:
    """Get current user from authorization header"""
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header required")
    
    if not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Invalid authorization format")
    
    token = authorization.replace("Bearer ", "")
    token_data = verify_token(token)
    
    user_id = token_data["user_id"]
    if user_id not in users_db:
        raise HTTPException(status_code=404, detail="User not found")
    
    return {"user_id": user_id, "role": token_data["role"]}

class AuthService:
    def init_test_data(self):
        """Initialize test users"""
        # Create admin user
        admin_id = str(uuid.uuid4())
        users_db[admin_id] = {
            "id": admin_id,
            "email": "<EMAIL>",
            "password": hash_password("admin123"),
            "full_name": "System Administrator",
            "role": "admin",
            "created_at": datetime.utcnow()
        }
        
        # Create regular user
        user_id = str(uuid.uuid4())
        users_db[user_id] = {
            "id": user_id,
            "email": "<EMAIL>",
            "password": hash_password("user123"),
            "full_name": "Test User",
            "role": "user",
            "created_at": datetime.utcnow()
        }
        
        print("Test data initialized")
        print("Admin: <EMAIL> / admin123")
        print("User: <EMAIL> / user123")
    
    async def register_user(self, user_data: UserCreate) -> TokenResponse:
        """Register a new user"""
        # Check if user exists
        for user in users_db.values():
            if user["email"] == user_data.email:
                raise ValueError("Email already registered")
        
        # Auto-assign admin role for specific emails
        role = "admin" if user_data.email in ["<EMAIL>", "<EMAIL>"] else user_data.role
        
        # Create user
        user_id = str(uuid.uuid4())
        user_record = {
            "id": user_id,
            "email": user_data.email,
            "password": hash_password(user_data.password),
            "full_name": user_data.full_name,
            "role": role,
            "created_at": datetime.utcnow()
        }
        users_db[user_id] = user_record
        
        # Create access token
        access_token = create_access_token(user_id, role)
        
        # Create User object for response
        user_obj = User(
            id=user_record["id"],
            email=user_record["email"],
            full_name=user_record["full_name"],
            role=user_record["role"],
            created_at=user_record["created_at"]
        )
        
        return TokenResponse(
            access_token=access_token,
            token_type="bearer",
            user=user_obj
        )
    
    async def login_user(self, login_data: UserLogin) -> TokenResponse:
        """Login user and generate token"""
        # Find user
        user = None
        for u in users_db.values():
            if u["email"] == login_data.email:
                user = u
                break
        
        if not user or not verify_password(login_data.password, user["password"]):
            raise ValueError("Invalid email or password")
        
        # Create access token
        access_token = create_access_token(user["id"], user["role"])
        
        # Create User object for response
        user_obj = User(
            id=user["id"],
            email=user["email"],
            full_name=user["full_name"],
            role=user["role"],
            created_at=user["created_at"]
        )
        
        return TokenResponse(
            access_token=access_token,
            token_type="bearer",
            user=user_obj
        )
    
    async def get_user_info(self, user_id: str) -> User:
        """Get user information"""
        if user_id not in users_db:
            raise HTTPException(status_code=404, detail="User not found")
        
        user_data = users_db[user_id]
        return User(
            id=user_data["id"],
            email=user_data["email"],
            full_name=user_data["full_name"],
            role=user_data["role"],
            created_at=user_data["created_at"]
        )
    
    async def get_all_users(self) -> List[Dict[str, Any]]:
        """Get all users (admin only)"""
        users = []
        for user in users_db.values():
            users.append({
                "id": user["id"],
                "email": user["email"],
                "full_name": user["full_name"],
                "role": user["role"],
                "created_at": user["created_at"]
            })
        
        return users
    
    async def get_user_by_id(self, user_id: str) -> User:
        """Get user by ID"""
        if user_id not in users_db:
            raise ValueError("User not found")
        
        user = users_db[user_id]
        return User(
            id=user["id"],
            email=user["email"],
            full_name=user["full_name"],
            role=user["role"],
            created_at=user["created_at"],
            last_login_at=user.get("last_login_at"),
            is_active=user.get("is_active", True)
        )
    
    async def logout_user(self, user_id: str) -> bool:
        """Logout user by removing active tokens"""
        # Remove all tokens for this user
        tokens_to_remove = []
        for token, data in active_tokens.items():
            if data.get("user_id") == user_id:
                tokens_to_remove.append(token)
        
        for token in tokens_to_remove:
            del active_tokens[token]
        
        return True
    
    async def get_user_count(self) -> int:
        """Get total number of users"""
        return len(users_db)
    
    async def init_test_data(self):
        """Initialize test data for development"""
        # Add admin user if not exists
        admin_email = "<EMAIL>"
        if not any(user["email"] == admin_email for user in users_db.values()):
            admin_user = UserCreate(
                email=admin_email,
                password="admin123",
                full_name="Admin User",
                role="admin"
            )
            await self.register_user(admin_user)
        
        # Add test user if not exists
        test_email = "<EMAIL>"
        if not any(user["email"] == test_email for user in users_db.values()):
            test_user = UserCreate(
                email=test_email,
                password="test123",
                full_name="Test User",
                role="user"
            )
            await self.register_user(test_user)
    
    async def get_current_user(self, authorization: Optional[str] = Header(None)) -> Dict[str, Any]:
        """Get current user from authorization header"""
        if not authorization:
            raise HTTPException(status_code=401, detail="Authorization header missing")
        
        if not authorization.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Invalid authorization format")
        
        token = authorization.split(" ")[1]
        
        try:
            # Verify token
            if token not in active_tokens:
                raise HTTPException(status_code=401, detail="Invalid or expired token")
            
            token_data = active_tokens[token]
            user_id = token_data["user_id"]
            
            if user_id not in users_db:
                raise HTTPException(status_code=401, detail="User not found")
            
            user = users_db[user_id]
            return {
                "id": user["id"],
                "email": user["email"],
                "full_name": user["full_name"],
                "role": user["role"]
            }
            
        except Exception as e:
            raise HTTPException(status_code=401, detail="Invalid token")

    async def get_current_admin_user(self, authorization: Optional[str] = Header(None)) -> Dict[str, Any]:
        """Get current user and verify admin role"""
        user = await self.get_current_user(authorization)

        if user.get("role") != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required"
            )

        return user
