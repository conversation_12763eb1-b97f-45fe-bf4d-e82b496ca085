# config.py - Configuration file
import os
from pathlib import Path

class Config:
    """Configuration settings for the PM Chatbot API"""
    
    # API Settings
    API_HOST = os.getenv("API_HOST", "0.0.0.0")
    API_PORT = int(os.getenv("API_PORT", 8000))
    API_RELOAD = os.getenv("API_RELOAD", "true").lower() == "true"
    
    # Model Settings
    MODEL_PATH = os.getenv(
        "MODEL_PATH", 
        r"C:\Users\<USER>\Documents\GitHub\Yonn-GPT-2025-01-Yonn-Team14\fastapi\model\qwen25-7b-project-mgmt-q5_k_m.gguf"
    )
    
    # Documents Settings
    DOCS_FOLDER = os.getenv(
        "DOCS_FOLDER", 
        r"C:\Users\<USER>\Documents\GitHub\Yonn-GPT-2025-01-Yonn-Team14\fastapi\Docs"
    )
    
    # RAG Settings
    VECTOR_STORE_PATH = os.getenv("VECTOR_STORE_PATH", "pm_rag_store.pkl.gz")
    EMBEDDING_DIM = int(os.getenv("EMBEDDING_DIM", 384))
    MAX_DOC_CHUNK_SIZE = int(os.getenv("MAX_DOC_CHUNK_SIZE", 800))
    CHUNK_OVERLAP = int(os.getenv("CHUNK_OVERLAP", 150))
    RELEVANCE_THRESHOLD = float(os.getenv("RELEVANCE_THRESHOLD", 0.5))  # Lowered
    MIN_PM_KEYWORDS = int(os.getenv("MIN_PM_KEYWORDS", 2))
    
    # LLM Settings
    LLM_MAX_TOKENS = int(os.getenv("LLM_MAX_TOKENS", 800))  # Reduced
    LLM_TEMPERATURE = float(os.getenv("LLM_TEMPERATURE", 0.7))
    LLM_N_CTX = int(os.getenv("LLM_N_CTX", 2048))  # Reduced
    LLM_N_THREADS = int(os.getenv("LLM_N_THREADS", 8))
    LLM_N_GPU_LAYERS = int(os.getenv("LLM_N_GPU_LAYERS", 0))
    
    # Conversation Cache Settings
    MAX_SESSIONS = int(os.getenv("MAX_SESSIONS", 1000))
    MAX_MESSAGES_PER_SESSION = int(os.getenv("MAX_MESSAGES_PER_SESSION", 50))
    SESSION_TIMEOUT_HOURS = int(os.getenv("SESSION_TIMEOUT_HOURS", 12))
    
    # CORS Settings
    CORS_ORIGINS = os.getenv("CORS_ORIGINS", "*").split(",")
    CORS_METHODS = os.getenv("CORS_METHODS", "*").split(",")
    CORS_HEADERS = os.getenv("CORS_HEADERS", "*").split(",")
    
    # Logging Settings
    LOG_LEVEL = os.getenv("LOG_LEVEL", "DEBUG")
    LOG_FORMAT = os.getenv(
        "LOG_FORMAT", 
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    @classmethod
    def validate_paths(cls):
        """Validate that required paths exist"""
        if not os.path.exists(cls.MODEL_PATH):
            raise FileNotFoundError(f"Model file not found: {cls.MODEL_PATH}")
        
        if not os.path.exists(cls.DOCS_FOLDER):
            os.makedirs(cls.DOCS_FOLDER, exist_ok=True)
            print(f"Created docs folder: {cls.DOCS_FOLDER}")
    
    @classmethod
    def print_config(cls):
        """Print current configuration"""
        print("🔧 Current Configuration:")
        print(f"   API Host: {cls.API_HOST}:{cls.API_PORT}")
        print(f"   Model Path: {cls.MODEL_PATH}")
        print(f"   Docs Folder: {cls.DOCS_FOLDER}")
        print(f"   Vector Store: {cls.VECTOR_STORE_PATH}")
        print(f"   Max Sessions: {cls.MAX_SESSIONS}")
        print(f"   Session Timeout: {cls.SESSION_TIMEOUT_HOURS}h")
        print(f"   Log Level: {cls.LOG_LEVEL}")

# startup.py - Application startup script
import sys
import asyncio
import signal
import uvicorn
from pathlib import Path

# Add current directory to Python path
sys.path.append(str(Path(__file__).parent))

from config import Config

def setup_logging():
    """Setup logging configuration"""
    import logging
    
    logging.basicConfig(
        level=getattr(logging, Config.LOG_LEVEL.upper()),
        format=Config.LOG_FORMAT,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler("pm_chatbot.log")
        ]
    )

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    print(f"\n🛑 Received signal {signum}. Shutting down gracefully...")
    sys.exit(0)

def main():
    """Main startup function"""
    print("🚀 Starting Project Management Chatbot API")
    print("=" * 50)
    
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Setup logging
    setup_logging()
    
    # Validate configuration
    try:
        Config.validate_paths()
        Config.print_config()
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        sys.exit(1)
    
    # Start the server
    print(f"\n🌐 Starting server on {Config.API_HOST}:{Config.API_PORT}")
    print(f"📖 API Documentation: http://{Config.API_HOST}:{Config.API_PORT}/docs")
    print("💡 Use Ctrl+C to stop the server")
    
    try:
        uvicorn.run(
            "main:app",
            host=Config.API_HOST,
            port=Config.API_PORT,
            reload=Config.API_RELOAD,
            log_level=Config.LOG_LEVEL.lower(),
            access_log=True
        )
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()