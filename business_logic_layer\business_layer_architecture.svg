<svg width="1400" height="450" viewBox="0 0 1400 450" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1400" height="450" fill="#f8fafc"/>
  
  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#1e293b">
     Business Logic Layer - Domain-Driven Design
  </text>
  
  <!-- API Gateway -->
  <g id="api-gateway">
    <rect x="400" y="80" width="600" height="80" rx="10" fill="#059669" stroke="#047857" stroke-width="2"/>
    <text x="700" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">
       FastAPI Application Layer
    </text>
    <text x="700" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" fill="#d1fae5">
      Domain Routers • DDD Architecture • Dependency Injection • CORS • JWT Auth
    </text>
  </g>
  
  <!-- Core Services Layer -->
  <rect x="50" y="200" width="1300" height="220" rx="15" fill="#f1f5f9" stroke="#cbd5e1" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="70" y="220" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#374151">
     Domain Services Layer</text>
  
  <!-- Authentication Service -->
  <g id="auth-service">
    <rect x="80" y="240" width="220" height="140" rx="8" fill="#f59e0b" stroke="#d97706" stroke-width="2"/>
    <text x="190" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">
      Auth Service 
    </text>
    <text x="190" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#fef3c7">
      • JWT Token Management
    </text>
    <text x="190" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#fef3c7">
      • User Registration/Login
    </text>
    <text x="190" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#fef3c7">
      • Role-Based Access Control
    </text>
    <text x="190" y="330" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#fef3c7">
      • Password Security
    </text>
    <text x="190" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#fef3c7">
      • Input Validation
    </text>
    <text x="190" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#fef3c7">
      • Security Middleware
    </text>
  </g>
  
  <!-- AI Chat Service -->
  <g id="ai-service">
    <rect x="340" y="240" width="220" height="140" rx="8" fill="#dc2626" stroke="#991b1b" stroke-width="2"/>
    <text x="450" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">
      Message Service
    </text>
    <text x="450" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#fecaca">
      • AI Integration
    </text>
    <text x="450" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#fecaca">
      • Response Generation
    </text>
    <text x="450" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#fecaca">
      • Context Processing
    </text>
    <text x="450" y="330" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#fecaca">
      • Validation &amp; Safety
    </text>
    <text x="450" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#fecaca">
      • Business Logic Rules
    </text>
    <text x="450" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#fecaca">
      • Domain Models
    </text>
  </g>
  
  <!-- RAG Service -->
  <g id="rag-service">
    <rect x="600" y="240" width="220" height="140" rx="8" fill="#0891b2" stroke="#0e7490" stroke-width="2"/>
    <text x="710" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">
      RAG System
    </text>
    <text x="710" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#cffafe">
      • Document Processing
    </text>
    <text x="710" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#cffafe">
      • Vector Embeddings
    </text>
    <text x="710" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#cffafe">
      • Similarity Search
    </text>
    <text x="710" y="330" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#cffafe">
      • Context Retrieval
    </text>
    <text x="710" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#cffafe">
      • Knowledge Base Query
    </text>
    <text x="710" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#cffafe">
      • FAISS Integration
    </text>
  </g>
  
  <!-- Session Service -->
  <g id="session-service">
    <rect x="860" y="240" width="220" height="140" rx="8" fill="#7c2d12" stroke="#92400e" stroke-width="2"/>
    <text x="970" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">
      Session Service 
    </text>
    <text x="970" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#fed7aa">
      • Session Management
    </text>
    <text x="970" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#fed7aa">
      • Conversation State
    </text>
    <text x="970" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#fed7aa">
      • Domain Models
    </text>
    <text x="970" y="330" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#fed7aa">
      • Business Rules
    </text>
    <text x="970" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#fed7aa">
      • Cache Management
    </text>
    <text x="970" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#fed7aa">
      • Memory Optimization
    </text>
  </g>
  
  <!-- Document Service -->
  <g id="document-service">
    <rect x="1120" y="240" width="220" height="140" rx="8" fill="#059669" stroke="#047857" stroke-width="2"/>
    <text x="1230" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">
      Document Service
    </text>
    <text x="1230" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#d1fae5">
      • File Upload Processing
    </text>
    <text x="1230" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#d1fae5">
      • PDF Text Extraction
    </text>
    <text x="1230" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#d1fae5">
      • Format Validation
    </text>
    <text x="1230" y="330" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#d1fae5">
      • Content Indexing
    </text>
    <text x="1230" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#d1fae5">
      • Metadata Extraction
    </text>
    <text x="1230" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#d1fae5">
      • Processing Pipeline
    </text>
  </g>
  

  
  <!-- Data Flow Arrows -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4b5563"/>
    </marker>
  </defs>
  
  <!-- Vertical arrow from gateway to services -->
  <line x1="700" y1="160" x2="700" y2="200" stroke="#4b5563" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- Cross-service communication arrows -->
  <line x1="300" y1="310" x2="340" y2="310" stroke="#6b7280" stroke-width="1" stroke-dasharray="3,3" marker-end="url(#arrowhead)"/>
  <line x1="560" y1="310" x2="600" y2="310" stroke="#6b7280" stroke-width="1" stroke-dasharray="3,3" marker-end="url(#arrowhead)"/>
  <line x1="820" y1="310" x2="860" y2="310" stroke="#6b7280" stroke-width="1" stroke-dasharray="3,3" marker-end="url(#arrowhead)"/>
  <line x1="1080" y1="310" x2="1120" y2="310" stroke="#6b7280" stroke-width="1" stroke-dasharray="3,3" marker-end="url(#arrowhead)"/>
  
</svg>
