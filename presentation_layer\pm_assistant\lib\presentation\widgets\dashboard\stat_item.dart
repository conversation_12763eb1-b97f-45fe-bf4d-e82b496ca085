import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';

/// Stat item widget following Single Responsibility Principle
/// Handles only statistical display UI logic for regular dashboard
class StatItem extends StatelessWidget {
  final String emoji;
  final String value;
  final String label;

  const StatItem({
    super.key,
    required this.emoji,
    required this.value,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          emoji,
          style: const TextStyle(fontSize: 20),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.white,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: AppColors.white.withOpacity(0.8),
          ),
        ),
      ],
    );
  }
}
