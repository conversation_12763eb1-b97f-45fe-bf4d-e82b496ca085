from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from collections import deque
import threading
import uuid
import logging
import os
import json
import pickle
from dataclasses import dataclass, asdict

@dataclass
class ConversationMessage:
    """Structured message with enhanced metadata"""
    role: str
    content: str
    timestamp: str
    metadata: Dict[str, Any]
    message_id: str
    session_id: str

class EnhancedConversationCache:
    """Enhanced thread-safe conversation cache with advanced clarification tracking"""

    def __init__(self, max_sessions: int = 1000, max_messages_per_session: int = 100, session_timeout_hours: int = 24):
        self.max_sessions = max_sessions
        self.max_messages_per_session = max_messages_per_session
        self.session_timeout = session_timeout_hours * 3600  # Convert to seconds

        # Core storage
        self.conversations: Dict[str, deque] = {}
        self.clarification_counts: Dict[str, int] = {}
        self.session_metadata: Dict[str, Dict] = {}
        self.access_times: Dict[str, float] = {}
        self.message_index: Dict[str, List[str]] = {}  # For quick message lookup

        # Thread safety
        self._lock = threading.RLock()

        # Setup
        self._setup_logging()
        self._setup_persistence()
        self._load_conversations()

    def _setup_logging(self):
        """Setup comprehensive logging"""
        log_dir = os.path.join("fastapi", "Docs")
        os.makedirs(log_dir, exist_ok=True)

        log_file = os.path.join(log_dir, 'conversation_cache.log')
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info("Enhanced Conversation Cache initialized")

    def _setup_persistence(self):
        """Setup persistence for conversation data"""
        self.cache_dir = os.path.join("fastapi", "Docs", "cache")
        os.makedirs(self.cache_dir, exist_ok=True)
        self.persistence_file = os.path.join(self.cache_dir, "conversation_cache.pkl")

    def _load_conversations(self):
        """Load conversations from persistent storage"""
        with self._lock:
            if os.path.exists(self.persistence_file):
                try:
                    with open(self.persistence_file, 'rb') as f:
                        data = pickle.load(f)
                        self.conversations = {
                            k: deque(v, maxlen=self.max_messages_per_session)
                            for k, v in data.get('conversations', {}).items()
                        }
                        self.clarification_counts = data.get('clarification_counts', {})
                        self.session_metadata = data.get('session_metadata', {})
                        self.access_times = data.get('access_times', {})
                        self.message_index = data.get('message_index', {})
                    self.logger.info("Loaded conversations from persistent storage")
                except Exception as e:
                    self.logger.error(f"Error loading conversations: {str(e)}")
                    # Initialize empty storage on load failure
                    self.conversations = {}
                    self.clarification_counts = {}
                    self.session_metadata = {}
                    self.access_times = {}
                    self.message_index = {}
            else:
                self.logger.info("No existing conversation cache found, starting fresh")

    def _save_conversations(self):
        """Save conversations to persistent storage"""
        with self._lock:
            try:
                data = {
                    'conversations': {k: list(v) for k, v in self.conversations.items()},
                    'clarification_counts': self.clarification_counts,
                    'session_metadata': self.session_metadata,
                    'access_times': self.access_times,
                    'message_index': self.message_index
                }
                with open(self.persistence_file, 'wb') as f:
                    pickle.dump(data, f, protocol=pickle.HIGHEST_PROTOCOL)
                self.logger.info("Conversations saved to persistent storage")
            except Exception as e:
                self.logger.error(f"Error saving conversations: {str(e)}")

    def create_session(self, user_id: Optional[str] = None, preferred_language: str = "en") -> str:
        """Create a new session with comprehensive metadata"""
        with self._lock:
            # Cleanup if needed
            if len(self.conversations) >= self.max_sessions:
                self._cleanup_expired_sessions()
                if len(self.conversations) >= self.max_sessions:
                    self._force_cleanup_oldest_sessions()

            session_id = str(uuid.uuid4())
            current_time = datetime.now()

            # Initialize session storage
            self.conversations[session_id] = deque(maxlen=self.max_messages_per_session)
            self.clarification_counts[session_id] = 0
            self.access_times[session_id] = current_time.timestamp()
            self.message_index[session_id] = []

            # Enhanced session metadata
            self.session_metadata[session_id] = {
                "user_id": user_id or "anonymous",
                "preferred_language": preferred_language,
                "created_at": current_time.isoformat(),
                "last_activity": current_time.timestamp(),
                "message_count": 0,
                "clarification_requests": 0,
                "topics_discussed": [],
                "session_active": True
            }

            self.logger.info(f"Created session {session_id} for user {user_id or 'anonymous'}")
            self._save_conversations()
            return session_id

    def add_message(self, session_id: str, role: str, content: str, metadata: Optional[Dict] = None) -> bool:
        """Add message with enhanced tracking"""
        with self._lock:
            if session_id not in self.conversations:
                self.logger.warning(f"Session {session_id} does not exist")
                return False

            # Prepare metadata with defaults
            metadata = metadata or {}
            current_time = datetime.now()

            # Enhanced metadata
            enhanced_metadata = {
                "language": metadata.get("language", self.session_metadata[session_id]["preferred_language"]),
                "topic": metadata.get("topic", "unknown"),
                "sources_count": metadata.get("sources_count", 0),
                "clarification_count": metadata.get("clarification_count", 0),
                "is_clarification": metadata.get("is_clarification", False),
                "references": metadata.get("references", []),
                "message_length": len(content),
                "processing_time": metadata.get("processing_time", 0),
                **metadata  # Include any additional metadata
            }

            # Create message object
            message_id = str(uuid.uuid4())
            message = ConversationMessage(
                role=role,
                content=content,
                timestamp=current_time.isoformat(),
                metadata=enhanced_metadata,
                message_id=message_id,
                session_id=session_id
            )

            # Add to conversation
            self.conversations[session_id].append(asdict(message))
            self.message_index[session_id].append(message_id)

            # Update session metadata
            self.access_times[session_id] = current_time.timestamp()
            session_meta = self.session_metadata[session_id]
            session_meta["last_activity"] = current_time.timestamp()
            session_meta["message_count"] += 1

            # Track clarifications
            if enhanced_metadata.get("is_clarification", False):
                session_meta["clarification_requests"] += 1
                self.clarification_counts[session_id] = enhanced_metadata["clarification_count"]

            # Track topics
            topic = enhanced_metadata.get("topic", "unknown")
            if topic not in session_meta["topics_discussed"]:
                session_meta["topics_discussed"].append(topic)

            self.logger.debug(f"Added {role} message to session {session_id}: {content[:100]}...")
            self._save_conversations()
            return True

    def get_context(self, session_id: str, max_messages: int = 6) -> str:
        """Get formatted conversation context with enhanced formatting"""
        with self._lock:
            if session_id not in self.conversations:
                self.logger.warning(f"Session {session_id} not found")
                return ""

            self.access_times[session_id] = datetime.now().timestamp()

            # Get recent messages
            messages = list(self.conversations[session_id])[-max_messages:]

            # Format for better context
            context_lines = []
            for msg in messages:
                role = msg['role'].capitalize()
                content = msg['content']
                timestamp = msg['timestamp']

                # Add metadata context for assistant messages
                if role == "Assistant":
                    topic = msg['metadata'].get('topic', '')
                    sources_count = msg['metadata'].get('sources_count', 0)
                    if topic and sources_count > 0:
                        context_lines.append(f"{role} ({topic}, {sources_count} sources): {content}")
                    else:
                        context_lines.append(f"{role}: {content}")
                else:
                    context_lines.append(f"{role}: {content}")

            self._save_conversations()
            return "\n".join(context_lines)

    def get_conversation(self, session_id: str) -> List[Dict]:
        """Get full conversation with enhanced filtering options"""
        with self._lock:
            if session_id not in self.conversations:
                self.logger.warning(f"Session {session_id} not found")
                return []

            self.access_times[session_id] = datetime.now().timestamp()
            self._save_conversations()
            return list(self.conversations[session_id])

    def get_last_assistant_message(self, session_id: str) -> Optional[str]:
        """Get the most recent assistant message"""
        with self._lock:
            if session_id not in self.conversations:
                return None

            for msg in reversed(self.conversations[session_id]):
                if msg["role"] == "assistant":
                    return msg["content"]
            return None

    def get_last_user_query(self, session_id: str, exclude_clarifications: bool = False) -> Optional[str]:
        """Get the last user query with option to exclude clarification requests"""
        with self._lock:
            if session_id not in self.conversations:
                return None

            if exclude_clarifications:
                clarification_phrases = [
                    # English
                    "explain more", "clarify", "elaborate", "tell me more", "more details",
                    "i didn't understand", "i dont understand", "i don't understand",
                    "simplify", "break it down", "make it simpler", "not clear", "unclear",
                    "can you explain", "what do you mean", "i'm confused", "confusing",
                    "more information", "expand on", "go deeper", "in detail",
                    # French
                    "expliquer plus", "expliquez davantage", "clarifier", "élaborer",
                    "dites-moi en plus", "plus de détails", "je n'ai pas compris",
                    "je ne comprends pas", "simplifier", "détailler", "rendre plus simple",
                    "pas clair", "peu clair", "pouvez-vous expliquer", "que voulez-vous dire",
                    "je suis confus", "déroutant", "plus d'informations", "développer",
                    # Arabic
                    "اشرح أكثر", "وضح", "فصل", "أخبرني المزيد", "تفاصيل أكثر",
                    "لم أفهم", "لا أفهم", "بسط", "افصل ذلك", "اجعله أبسط", "غير واضح",
                    "يمكنك أن تشرح", "ماذا تقصد", "أنا مشوش", "محير", "المزيد من المعلومات",
                    "توسع في", "بالتفصيل"
                ]

                for msg in reversed(self.conversations[session_id]):
                    if msg["role"] == "user":
                        content_lower = msg["content"].lower()
                        is_clarification = any(phrase in content_lower for phrase in clarification_phrases)
                        if not is_clarification:
                            return msg["content"]
                return None
            else:
                for msg in reversed(self.conversations[session_id]):
                    if msg["role"] == "user":
                        return msg["content"]
                return None
        
    def get_clarification_count(self, session_id: str) -> int:
        """Get current clarification count for session"""
        with self._lock:
            return self.clarification_counts.get(session_id, 0)

    def get_session_stats(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get comprehensive session statistics"""
        with self._lock:
            if session_id not in self.session_metadata:
                return None

            session_meta = self.session_metadata[session_id].copy()
            conversation = self.conversations.get(session_id, deque())

            # Calculate additional stats
            total_messages = len(conversation)
            user_messages = sum(1 for msg in conversation if msg["role"] == "user")
            assistant_messages = sum(1 for msg in conversation if msg["role"] == "assistant")

            # Language distribution
            languages = {}
            for msg in conversation:
                lang = msg.get("metadata", {}).get("language", "unknown")
                languages[lang] = languages.get(lang, 0) + 1

            # Session duration
            if conversation:
                first_msg_time = datetime.fromisoformat(conversation[0]["timestamp"])
                last_msg_time = datetime.fromisoformat(conversation[-1]["timestamp"])
                duration = (last_msg_time - first_msg_time).total_seconds()
            else:
                duration = 0

            session_meta.update({
                "total_messages": total_messages,
                "user_messages": user_messages,
                "assistant_messages": assistant_messages,
                "languages_used": languages,
                "session_duration_seconds": duration,
                "clarification_count": self.clarification_counts.get(session_id, 0),
                "last_access": datetime.fromtimestamp(self.access_times.get(session_id, 0)).isoformat()
            })

            return session_meta

    def _cleanup_expired_sessions(self):
        """Clean up expired sessions based on timeout"""
        with self._lock:
            current_time = datetime.now().timestamp()
            expired_sessions = []

            for session_id, last_access in self.access_times.items():
                if current_time - last_access > self.session_timeout:
                    expired_sessions.append(session_id)

            for session_id in expired_sessions:
                self._remove_session(session_id)
                self.logger.info(f"Cleaned up expired session: {session_id}")

            if expired_sessions:
                self.logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
                self._save_conversations()

    def _force_cleanup_oldest_sessions(self):
        """Force cleanup of oldest sessions when limit reached"""
        with self._lock:
            if len(self.conversations) < self.max_sessions:
                return

            # Sort sessions by last access time
            sessions_by_access = sorted(
                self.access_times.items(),
                key=lambda x: x[1]
            )

            # Remove oldest 20% of sessions
            cleanup_count = max(1, len(sessions_by_access) // 5)
            for session_id, _ in sessions_by_access[:cleanup_count]:
                self._remove_session(session_id)

            self.logger.info(f"Force cleaned up {cleanup_count} oldest sessions")
            self._save_conversations()

    def _remove_session(self, session_id: str):
        """Remove all data for a session"""
        with self._lock:
            self.conversations.pop(session_id, None)
            self.clarification_counts.pop(session_id, None)
            self.session_metadata.pop(session_id, None)
            self.access_times.pop(session_id, None)
            self.message_index.pop(session_id, None)

    def clear_conversation(self, session_id: str) -> bool:
        """Clear conversation while preserving session metadata"""
        with self._lock:
            if session_id not in self.conversations:
                self.logger.warning(f"Session {session_id} not found")
                return False

            # Clear conversation but keep session alive
            self.conversations[session_id].clear()
            self.clarification_counts[session_id] = 0
            self.message_index[session_id] = []

            # Update metadata
            current_time = datetime.now().timestamp()
            self.access_times[session_id] = current_time
            self.session_metadata[session_id].update({
                "last_activity": current_time,
                "message_count": 0,
                "clarification_requests": 0,
                "topics_discussed": [],
                "cleared_at": datetime.now().isoformat()
            })

            self.logger.info(f"Cleared conversation for session {session_id}")
            self._save_conversations()
            return True

    def get_active_sessions(self) -> List[Dict[str, Any]]:
        """Get list of active sessions with summary info"""
        with self._lock:
            active_sessions = []
            current_time = datetime.now().timestamp()

            for session_id, meta in self.session_metadata.items():
                if session_id in self.conversations:
                    last_activity = self.access_times.get(session_id, 0)
                    is_active = (current_time - last_activity) < self.session_timeout

                    if is_active:
                        active_sessions.append({
                            "session_id": session_id,
                            "user_id": meta.get("user_id", "anonymous"),
                            "preferred_language": meta.get("preferred_language", "en"),
                            "created_at": meta.get("created_at"),
                            "message_count": len(self.conversations[session_id]),
                            "clarification_count": self.clarification_counts.get(session_id, 0),
                            "last_activity": datetime.fromtimestamp(last_activity).isoformat(),
                            "topics_discussed": meta.get("topics_discussed", [])
                        })

            return sorted(active_sessions, key=lambda x: x["last_activity"], reverse=True)

    def get_conversation_summary(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get conversation summary with key insights"""
        with self._lock:
            if session_id not in self.conversations:
                return None

            conversation = list(self.conversations[session_id])
            if not conversation:
                return {"session_id": session_id, "empty": True}

            # Basic stats
            user_messages = [msg for msg in conversation if msg["role"] == "user"]
            assistant_messages = [msg for msg in conversation if msg["role"] == "assistant"]

            # Topics analysis
            topics = []
            for msg in assistant_messages:
                topic = msg.get("metadata", {}).get("topic", "unknown")
                if topic not in topics and topic != "unknown":
                    topics.append(topic)

            # Language analysis
            languages = {}
            for msg in conversation:
                lang = msg.get("metadata", {}).get("language", "unknown")
                languages[lang] = languages.get(lang, 0) + 1

            # Clarification analysis
            clarification_requests = sum(
                1 for msg in assistant_messages
                if msg.get("metadata", {}).get("is_clarification", False)
            )

            # Time analysis
            if conversation:
                start_time = datetime.fromisoformat(conversation[0]["timestamp"])
                end_time = datetime.fromisoformat(conversation[-1]["timestamp"])
                duration = (end_time - start_time).total_seconds()
            else:
                duration = 0

            return {
                "session_id": session_id,
                "total_messages": len(conversation),
                "user_messages": len(user_messages),
                "assistant_messages": len(assistant_messages),
                "topics_discussed": topics,
                "languages_used": languages,
                "clarification_requests": clarification_requests,
                "session_duration_seconds": duration,
                "last_message_time": end_time.isoformat() if conversation else None
            }

    def prune_old_messages(self, session_id: str, max_age_hours: int) -> bool:
        """Remove messages older than specified hours for a session"""
        with self._lock:
            if session_id not in self.conversations:
                self.logger.warning(f"Session {session_id} not found")
                return False

            max_age = timedelta(hours=max_age_hours)
            current_time = datetime.now()
            new_conversation = deque(maxlen=self.max_messages_per_session)
            new_message_index = []

            for msg in self.conversations[session_id]:
                msg_time = datetime.fromisoformat(msg["timestamp"])
                if current_time - msg_time <= max_age:
                    new_conversation.append(msg)
                    new_message_index.append(msg["message_id"])

            removed_count = len(self.conversations[session_id]) - len(new_conversation)
            self.conversations[session_id] = new_conversation
            self.message_index[session_id] = new_message_index

            # Update metadata
            self.session_metadata[session_id]["message_count"] = len(new_conversation)
            if removed_count > 0:
                self.logger.info(f"Pruned {removed_count} old messages from session {session_id}")
            self._save_conversations()
            return True

    def get_message_by_id(self, session_id: str, message_id: str) -> Optional[Dict]:
        """Retrieve a specific message by its ID"""
        with self._lock:
            if session_id not in self.conversations:
                self.logger.warning(f"Session {session_id} not found")
                return None

            if message_id not in self.message_index[session_id]:
                self.logger.warning(f"Message ID {message_id} not found in session {session_id}")
                return None

            for msg in self.conversations[session_id]:
                if msg["message_id"] == message_id:
                    return msg
            return None

    def update_session_metadata(self, session_id: str, metadata_updates: Dict[str, Any]) -> bool:
        """Update session metadata with new values"""
        with self._lock:
            if session_id not in self.session_metadata:
                self.logger.warning(f"Session {session_id} not found")
                return False

            self.session_metadata[session_id].update(metadata_updates)
            self.access_times[session_id] = datetime.now().timestamp()
            self.logger.info(f"Updated metadata for session {session_id}")
            self._save_conversations()
            return True

    def export_conversation(self, session_id: str, format: str = "json") -> Optional[str]:
        """Export conversation in specified format (json or text)"""
        with self._lock:
            if session_id not in self.conversations:
                self.logger.warning(f"Session {session_id} not found")
                return None

            conversation = list(self.conversations[session_id])
            if not conversation:
                return None

            if format.lower() == "json":
                export_data = {
                    "session_id": session_id,
                    "metadata": self.session_metadata[session_id],
                    "messages": conversation
                }
                return json.dumps(export_data, indent=2, ensure_ascii=False)

            elif format.lower() == "text":
                lines = [f"Session ID: {session_id}"]
                lines.append(f"Metadata: {json.dumps(self.session_metadata[session_id], indent=2)}")
                lines.append("\nMessages:")
                for msg in conversation:
                    lines.append(f"[{msg['timestamp']} {msg['role'].capitalize()}]: {msg['content']}")
                    lines.append(f"Metadata: {json.dumps(msg['metadata'], indent=2)}")
                    lines.append("")
                return "\n".join(lines)

            else:
                self.logger.warning(f"Unsupported export format: {format}")
                return None

    def import_conversation(self, data: str, format: str = "json") -> Optional[str]:
        """Import conversation from specified format"""
        with self._lock:
            try:
                if format.lower() == "json":
                    import_data = json.loads(data)
                    session_id = import_data.get("session_id")
                    if not session_id or session_id in self.conversations:
                        session_id = str(uuid.uuid4())

                    self.conversations[session_id] = deque(
                        import_data.get("messages", []),
                        maxlen=self.max_messages_per_session
                    )
                    self.session_metadata[session_id] = import_data.get("metadata", {})
                    self.access_times[session_id] = datetime.now().timestamp()
                    self.clarification_counts[session_id] = sum(
                        1 for msg in self.conversations[session_id]
                        if msg.get("metadata", {}).get("is_clarification", False)
                    )
                    self.message_index[session_id] = [
                        msg["message_id"] for msg in self.conversations[session_id]
                    ]

                    self.logger.info(f"Imported conversation to session {session_id}")
                    self._save_conversations()
                    return session_id

                else:
                    self.logger.warning(f"Unsupported import format: {format}")
                    return None

            except Exception as e:
                self.logger.error(f"Error importing conversation: {str(e)}")
                return None