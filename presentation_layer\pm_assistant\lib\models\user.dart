/// Simple User model for frontend
/// Follows Single Responsibility Principle - handles only user data
class User {
  final String id;
  final String email;
  final String name;
  final String role;
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  final bool isActive;

  const User({
    required this.id,
    required this.email,
    required this.name,
    required this.role,
    required this.createdAt,
    this.lastLoginAt,
    this.isActive = true,
  });

  /// Factory constructor for creating user with default values
  factory User.create({
    required String email,
    required String name,
    required String role,
    String? id,
  }) {
    return User(
      id: id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      email: email,
      name: name,
      role: role,
      createdAt: DateTime.now(),
    );
  }

  /// Factory constructor for creating User from JSON
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id']?.toString() ?? '',
      email: json['email']?.toString() ?? '',
      name: json['full_name']?.toString() ?? json['name']?.toString() ?? '',  // Handle both full_name and name
      role: json['role']?.toString() ?? 'user',
      createdAt: json['created_at'] != null 
        ? DateTime.tryParse(json['created_at'].toString()) ?? DateTime.now()
        : DateTime.now(),
      lastLoginAt: json['last_login_at'] != null 
        ? DateTime.tryParse(json['last_login_at'].toString())
        : null,
      isActive: json['is_active'] as bool? ?? true,
    );
  }

  /// Convert User to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'role': role,
      'created_at': createdAt.toIso8601String(),
      'last_login_at': lastLoginAt?.toIso8601String(),
      'is_active': isActive,
    };
  }

  /// Check if user is admin
  bool get isAdmin => role.toLowerCase() == 'admin';

  /// Check if user is regular user
  bool get isRegular => role.toLowerCase() == 'user';

  /// Create a copy with updated values
  User copyWith({
    String? id,
    String? email,
    String? name,
    String? role,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    bool? isActive,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      role: role ?? this.role,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'User(id: $id, email: $email, name: $name, role: $role)';
  }
}
