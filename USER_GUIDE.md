# 📖 PM Assistant - Complete User Guide

## 🚀 Quick Start

PM Assistant is an AI-powered project management consultant that provides instant, intelligent responses using your own documents. This guide will help you set up and use the system in minutes.

## 📋 What You Need

### For VPS/Production (Recommended)
- **VPS Server**: Ubuntu 20.04+ with 6GB RAM, 4+ CPU cores
- **Docker**: Installed and running
- **Model File**: `qwen2.5-7b-pm-assistant.q4_k_m.gguf` (or similar)
- **Documents**: Your PM documents in `business_logic_layer/Docs/`

### For Local Development
- **Computer**: 8GB RAM, Docker installed
- **Same files**: Model and documents

## 🎯 Response Format

PM Assistant provides structured responses:

```
**Thinking**
1. Analyze the query: [What you're asking]
2. Review context: [References from your documents]
3. Explain components: [Key parts from documents]
4. Describe benefits: [Benefits from sources]

**Answer**
1- [Point from documents]: [Explanation with references]
2- [Point from documents]: [Explanation with references]
3- [Point from documents]: [Explanation with references]
4- [Point from documents]: [Explanation with references]

**Reference**
[Source 1, Pages: X-Y, Section: Z]
[Source 2, Pages: X-Y, Section: Z]
[Source 3, Pages: X-Y, Section: Z]
[Source 4, Pages: X-Y, Section: Z]
```

## 🐳 Docker Configurations

We provide 4 Docker configurations for different use cases:

### 1. **VPS Production** (`docker-compose.vps.yml`)
- **Use for**: Production deployment on VPS
- **Includes**: PM Assistant + Redis + PostgreSQL
- **Features**: Auto-restart, health checks, resource limits
- **Memory**: 6GB limit, optimized for production

### 2. **VPS Simple** (`dockerfile.vps`)
- **Use for**: Simple VPS deployment without database
- **Includes**: Just PM Assistant
- **Features**: Lightweight, fast startup

### 3. **Local Development** (`docker-compose.dev.yml`)
- **Use for**: Development and testing
- **Includes**: PM Assistant + optional Redis/PostgreSQL
- **Features**: Hot reload, debugging, code mounting

### 4. **Development Simple** (`dockerfile.dev`)
- **Use for**: Simple local development
- **Includes**: Just PM Assistant with dev tools
- **Features**: Debugging, development packages

## 🚀 Setup Instructions

### Option 1: VPS Production (Full Stack)

```bash
# 1. Connect to your VPS
ssh ubuntu@YOUR_VPS_IP

# 2. Install Docker (if not installed)
sudo apt update && sudo apt upgrade -y
sudo apt install docker.io docker-compose -y
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker ubuntu
# Log out and back in

# 3. Upload project files
# Use WinSCP, git clone, or scp to upload to /opt/pm-assistant/

# 4. Add your model file
# Place your .gguf model in: business_logic_layer/models/

# 5. Add your documents
# Place your PM documents in: business_logic_layer/Docs/

# 6. Deploy
cd /opt/pm-assistant/Yonn-GPT-2025-01-Yonn-Team14
docker-compose -f docker-compose.vps.yml up --build -d

# 7. Check status
docker-compose -f docker-compose.vps.yml ps
curl http://localhost:8000/health
```

### Option 2: VPS Simple (PM Assistant Only)

```bash
# 1-5. Same as above

# 6. Build and run
docker build -f dockerfile.vps -t pm-assistant .
docker run -d \
  --name pm-assistant \
  -p 8000:8000 \
  -v $(pwd)/business_logic_layer/models:/app/business_logic_layer/models:ro \
  -v $(pwd)/business_logic_layer/Docs:/app/business_logic_layer/Docs:ro \
  pm-assistant

# 7. Check status
docker ps
curl http://localhost:8000/health
```

### Option 3: Local Development (Full Stack)

```bash
# 1. Clone or download project
git clone <repository-url>
cd Yonn-GPT-2025-01-Yonn-Team14

# 2. Add model and documents (same as VPS)

# 3. Start development environment
docker-compose -f docker-compose.dev.yml up --build

# 4. Access services
# PM Assistant: http://localhost:8000
# Redis: localhost:6379
# PostgreSQL: localhost:5432
```

### Option 4: Local Development (Simple)

```bash
# 1-2. Same as above

# 3. Build and run
docker build -f dockerfile.dev -t pm-assistant-dev .
docker run -it \
  --name pm-assistant-dev \
  -p 8000:8000 \
  -v $(pwd)/business_logic_layer:/app/business_logic_layer \
  pm-assistant-dev

# 4. Access: http://localhost:8000
```

## 📁 File Structure

```
Yonn-GPT-2025-01-Yonn-Team14/
├── business_logic_layer/
│   ├── models/                     # Place your .gguf model here
│   │   └── qwen2.5-7b-pm-assistant.q4_k_m.gguf
│   ├── Docs/                       # Place your PM documents here
│   │   ├── PMBOK_Guide.pdf
│   │   ├── Agile_Handbook.pdf
│   │   └── Project_Templates.docx
│   ├── services/
│   │   └── message_service.py      # Main AI service
│   ├── routers/
│   │   └── chat_router.py          # API endpoints
│   ├── requirements.txt            # Python dependencies
│   └── server_clean.py             # Main server
├── presentation_layer/
│   └── pm_assistant/               # Flutter frontend
├── docker-compose.vps.yml          # VPS production
├── docker-compose.dev.yml          # Local development
├── dockerfile.vps                  # VPS container
├── dockerfile.dev                  # Development container
└── USER_GUIDE.md                   # This file
```

## 🧪 Testing Your Setup

### 1. Health Check
```bash
curl http://YOUR_IP:8000/health
# Should return: {"status": "healthy", "model_loaded": true}
```

### 2. Simple Chat
```bash
curl -X POST http://YOUR_IP:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "What is project management?"}'
```

### 3. Streaming Chat
```bash
curl -X POST http://YOUR_IP:8000/chat/stream \
  -H "Content-Type: application/json" \
  -d '{"message": "Explain agile methodology"}'
```

### 4. Multi-language Test
```bash
# French
curl -X POST http://YOUR_IP:8000/chat/stream \
  -H "Content-Type: application/json" \
  -d '{"message": "Qu'\''est-ce que la gestion de projet?"}'

# Arabic
curl -X POST http://YOUR_IP:8000/chat/stream \
  -H "Content-Type: application/json" \
  -d '{"message": "ما هي إدارة المشاريع؟"}'
```

## 🔧 Management Commands

### Check Status
```bash
# For VPS production
docker-compose -f docker-compose.vps.yml ps
docker-compose -f docker-compose.vps.yml logs -f pm-assistant

# For development
docker-compose -f docker-compose.dev.yml ps
docker-compose -f docker-compose.dev.yml logs -f pm-assistant-dev
```

### Restart Services
```bash
# VPS production
docker-compose -f docker-compose.vps.yml restart pm-assistant

# Development
docker-compose -f docker-compose.dev.yml restart pm-assistant-dev
```

### Update Code
```bash
# After making changes
docker-compose -f docker-compose.vps.yml down
docker-compose -f docker-compose.vps.yml up --build -d
```

### Monitor Resources
```bash
docker stats --no-stream
htop
free -h
```

## 🌐 API Endpoints

### Base URL
- **Production**: `http://YOUR_VPS_IP:8000`
- **Development**: `http://localhost:8000`

### Available Endpoints
- **GET** `/health` - Health check
- **POST** `/chat` - Send message (complete response)
- **POST** `/chat/stream` - Send message (streaming response)
- **POST** `/sessions` - Create new session
- **GET** `/docs` - API documentation

## 🎯 Performance Features

### ⚡ Speed Optimizations
- **First token**: 50-100ms
- **Complete response**: 3-5 seconds
- **Context size**: 512 tokens (optimized)
- **Max tokens**: 200 (for speed)

### 📚 RAG Integration
- **Document processing**: Automatic chunking and indexing
- **Semantic search**: Finds relevant content
- **Context injection**: Uses documents in responses
- **Source citation**: References specific pages/sections

### 🛑 Smart Stopping
- **Structured format**: Always follows Thinking → Answer → Reference
- **Clean termination**: Stops after Reference section
- **No repetition**: Advanced repeat penalty

## 🔍 Troubleshooting

### Model Not Loading
```bash
# Check model file
ls -la business_logic_layer/models/
chmod 644 business_logic_layer/models/*.gguf

# Check logs
docker logs pm-assistant-vps
```

### Out of Memory
```bash
# Check memory usage
free -h
docker stats

# Reduce model context size in message_service.py
# n_ctx = 512  # Make smaller if needed
```

### Slow Responses
```bash
# Check CPU usage
htop

# Optimize in message_service.py:
# - Reduce max_tokens
# - Lower temperature
# - Increase n_threads
```

### Connection Issues
```bash
# Check if service is running
docker ps | grep pm-assistant

# Check port
netstat -tlnp | grep 8000

# Check firewall
sudo ufw status
```

## 🎉 Success Indicators

You'll know it's working when:

1. ✅ **Health check passes**: Returns `{"status": "healthy"}`
2. ✅ **Model loads**: `"model_loaded": true` in health response
3. ✅ **Instant streaming**: First tokens appear within 100ms
4. ✅ **RAG integration**: Thinking section references your documents
5. ✅ **Structured format**: Responses follow Thinking → Answer → Reference
6. ✅ **Clean stopping**: No content after Reference section
7. ✅ **Multi-language**: Works in English, French, Arabic

## 📞 Support

If you encounter issues:

1. **Check logs**: `docker logs <container-name>`
2. **Verify files**: Model and documents in correct locations
3. **Test endpoints**: Use curl commands above
4. **Monitor resources**: Check RAM/CPU usage
5. **Review configuration**: Ensure Docker files are correct

Your PM Assistant is now ready to provide intelligent, instant project management advice! 🚀
