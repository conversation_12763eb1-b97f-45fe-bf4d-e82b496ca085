# 🤖 PM Assistant - Intelligent Project Management Assistant

## 🚀 Overview

PM Assistant is an AI-powered project management consultant that provides instant, structured responses using RAG (Retrieval-Augmented Generation) technology. It combines local AI models with project management documents to deliver expert advice in real-time.

### ✨ Key Features

- **🔥 Instant Token Streaming**: Real-time response generation (50-100ms first token)
- **📚 RAG Integration**: Uses project management documents in responses
- **🎯 Structured Responses**: Thinking → Answer → Reference format
- **🌍 Multi-language Support**: English, French, Arabic
- **🐳 Docker Deployment**: Easy development and production deployment
- **📱 Flutter Frontend**: Modern, responsive UI with copy functionality
- **⚡ High Performance**: Optimized for speed and accuracy

## 🎯 Key Features

- **Layered Architecture** - Clear 5-layer separation with MVVM pattern
- **Admin Dashboard** - Comprehensive admin interface with real-time analytics and system monitoring
- **Role-Based Access Control** - Admin and user roles with secure authentication
- **Real-Time Analytics** - Performance metrics, user statistics, and system health monitoring
- **Service-Oriented Backend** - Well-structured API organization with domain separation
- **Production Ready** - Scalable, maintainable, testable architecture with JWT authentication
- **Local Operation** - No data leaves your machine, complete privacy
- **Multi-language Support** - Extensible internationalization framework
- **Real-time Chat** - Responsive conversational interface with session management

## 🏗️ Architecture Overview

![Layered Architecture Diagram](diagrams/archi/archi.svg)

### Five-Layer Architecture

#### 🎨 **Presentation Layer** (Flutter)
- **Web Interface** - Flutter web with voice input
- **Desktop App** - Native Flutter desktop application with Windows support
- **Mobile App** - Cross-platform mobile support  
- **Admin Dashboard** - Real-time performance monitoring, user management, system health, and analytics
- **Role-Based UI** - Dynamic interface based on user permissions

#### 🧠 **Business Logic Layer** (FastAPI)
- **Chat Controller** - Session and conversation management with analytics
- **Admin Services** - User management, system monitoring, and performance analytics
- **Authentication System** - JWT-based security with role-based access control
- **NLP Processor** - Intent classification and entity extraction
- **Knowledge Manager** - Search queries and context injection
- **Security Manager** - Input sanitization and access control

#### 🤖 **AI/ML Processing Layer**
- **Knowledge Base** - Q&A dataset processing and retrieval
- **NLP Engine** - Intent classification and response generation
- **Data Processing** - Text preprocessing and validation
- **ML Models** - Text classification and similarity matching

#### 🗄️ **Data Storage Layer**
- **SQLite Database** - User sessions and conversation history
- **Vector Database** - FAISS for semantic search
- **Knowledge Base** - FAQ documents and training data
- **File Storage** - Model files and configuration

#### 🏗️ **Infrastructure Layer**
- **Docker Containers** - Containerized deployment
- **Monitoring** - Prometheus and Grafana integration
- **Load Balancer** - nginx/HAProxy for scaling
- **CI/CD Pipeline** - Automated testing and deployment

## 🚀 Quick Start

### Prerequisites
- Python 3.9+ with FastAPI and dependencies
- Flutter SDK 3.0+ (for frontend)
- 4GB RAM minimum

### Running the Layered Architecture System

#### 1. Backend (Business Logic Layer)
```bash
# Navigate to business logic layer
cd business_logic_layer

# Install dependencies
pip install -r requirements.txt

# Run the main server
python server.py
# OR use uvicorn directly
uvicorn main:app --host 0.0.0.0 --port 8000 --reload

# Server will start at http://localhost:8000
# API docs available at http://localhost:8000/docs
```

#### 2. Frontend (Presentation Layer)
```bash
# Navigate to presentation layer
cd presentation_layer/pm_assistant

# Install Flutter dependencies
flutter pub get

# Run the app
flutter run

# For web development
flutter run -d chrome
```

### Architecture Validation
The system provides:
- ✅ Proper layer separation
- ✅ Service-oriented design
- ✅ Scalable architecture
- ✅ Cross-platform support

## 📁 Project Structure

```
├── presentation_layer/              # Flutter Frontend (MVVM)
│   └── pm_assistant/
│       ├── lib/
│       │   ├── core/
│       │   │   ├── api_gateway/     # ✅ Single backend communication
│       │   │   │   └── api_gateway.dart
│       │   │   ├── constants/       # App constants
│       │   │   ├── theme/          # UI theming
│       │   │   └── utils/          # Utilities
│       │   ├── models/             # ✅ Data models with JSON serialization
│       │   │   ├── user.dart
│       │   │   ├── conversation.dart
│       │   │   └── message.dart
│       │   ├── presentation/       # ✅ MVVM implementation
│       │   │   ├── viewmodels/     # State management
│       │   │   │   ├── auth_viewmodel.dart
│       │   │   │   ├── chat_viewmodel.dart
│       │   │   │   └── admin_dashboard_viewmodel.dart
│       │   │   ├── views/          # UI components
│       │   │   └── widgets/        # Reusable UI components
│       │   │       ├── admin/      # Admin-specific widgets
│       │   │       ├── auth/       # Authentication widgets
│       │   │       ├── chat/       # Chat-related widgets
│       │   │       └── common/     # Shared components
│       │   └── main.dart           # ✅ Clean dependency injection
│       └── pubspec.yaml
│
├── business_logic_layer/            # FastAPI Backend (Microservices)
│   ├── server.py                   # ✅ Main production server
│   ├── models/                     # ✅ Data Transfer Objects
│   │   ├── user_models.py          # User-related DTOs
│   │   ├── chat_models.py          # Chat-related DTOs
│   │   └── analytics_models.py     # Analytics and metrics DTOs
│   ├── routers/                    # ✅ Domain-organized endpoints
│   │   ├── auth_router.py          # /auth/* endpoints
│   │   ├── chat_router.py          # /chat/* endpoints
│   │   └── admin_router.py         # /admin/* endpoints
│   ├── services/                   # ✅ Business logic services
│   │   ├── auth_service.py         # Authentication & user management
│   │   ├── session_service.py      # Conversation & analytics management
│   │   ├── message_service.py      # Message processing
│   │   └── document_service.py     # Document handling
│   ├── requirements.txt
│   └── main.py                     # 📦 Alternative entry point
│
├── data_layer/                     # Data & Training
│   └── training_data/              # AI training datasets
│
├── diagrams/                       # Architecture Documentation
│   ├── archi/                      # ✅ Updated architecture diagrams
│   ├── ui_ux/                      # UI/UX flow diagrams
│   └── uml/                        # UML class/sequence diagrams
│
└── demos/                          # Demo videos and examples
```

## 🔧 Core Components & Architecture

### 🎨 Presentation Layer (Flutter - MVVM)

#### API Gateway Pattern
- **Single Communication Point** with business logic layer
- **Token Management** for authentication
- **Error Handling** and response parsing
- **HTTP Client** abstraction

#### ViewModels (State Management)
- **AuthViewModel** - User authentication, profile management, and role-based access
- **ChatViewModel** - Chat functionality, conversation management, and message handling
- **AdminDashboardViewModel** - Admin dashboard, analytics, user management, and system health
- **Provider Pattern** - Clean state management with dependency injection

#### Models with JSON Serialization
- **User Model** - User data with fromJson/toJson
- **Conversation Model** - Chat conversations with message tracking
- **Message Model** - Individual messages with metadata

### 🧠 Business Logic Layer (FastAPI - Microservices)

#### Domain-Organized Routers
```python
# Authentication Router (/auth/*)
- POST /auth/register      # User registration
- POST /auth/login         # User authentication  
- GET  /auth/me           # Current user info
- POST /auth/logout       # User logout

# Chat Router (/chat/*)  
- POST /chat/send         # Send message
- POST /chat/conversation # Create conversation
- GET  /chat/conversations # List conversations
- GET  /chat/conversation/{id} # Get conversation detail
- DELETE /chat/conversation/{id} # Delete conversation

# Admin Router (/admin/*)
- GET  /admin/users       # List all users (admin only)
- GET  /admin/analytics   # System analytics with performance metrics
- GET  /admin/stats       # Admin dashboard statistics
- GET  /admin/system-health # System health monitoring
```

#### Service Layer Architecture
- **AuthService** - User management, JWT token validation, role-based access control
- **SessionService** - Conversation lifecycle, message storage, analytics data (avg response time: 1.9s)
- **MessageService** - Message processing, response generation, and conversation tracking
- **DocumentService** - File upload, processing, and knowledge base management

#### Data Transfer Objects (DTOs)
- **Type-safe Models** with Pydantic validation
- **Request/Response** structures for API consistency
- **Error Handling** with structured responses

### 🗄️ Data Layer
- **In-Memory Storage** (development) - Ready for database integration
- **Training Data** - AI model enhancement datasets
- **File Storage** - Document and media handling

## 🌐 API Endpoints

### Health & Documentation
- `GET /` - Interactive API documentation (Swagger UI)
- `GET /health` - System health check

### Authentication Domain (`/auth`)
- `POST /auth/register` - Register new user with role assignment
- `POST /auth/login` - Authenticate and receive JWT token
- `GET /auth/me` - Get current user profile and permissions

### Session Management (`/session`)  
- `POST /session/create` - Create new chat session
- `GET /session/list` - Get user's chat sessions
- `GET /session/{session_id}` - Get specific session details
- `DELETE /session/{session_id}` - Delete session and history

### Message Processing (`/message`)
- `POST /message/send` - Send message and get AI response
- `GET /message/history/{session_id}` - Get conversation history
- `POST /message/feedback` - Provide feedback on AI responses

### Document Management (`/documents`)
- `POST /documents/upload` - Upload and process documents
- `GET /documents/list` - Get user's document library
- `GET /documents/{doc_id}` - Retrieve specific document
- `DELETE /documents/{doc_id}` - Remove document from system

### Administration (`/admin`) 
- `GET /admin/users` - User management and user listing (admin only)
- `GET /admin/analytics` - System analytics with performance metrics (admin only)
- `GET /admin/stats` - Admin dashboard statistics (admin only)
- `GET /admin/system-health` - System health monitoring with component status (admin only)

## 🛠️ Technology Stack

### Frontend (Presentation Layer)
- **Framework**: Flutter 3.0+ with Windows desktop support
- **Architecture**: MVVM with Provider pattern and dependency injection
- **State Management**: Provider pattern with role-based UI rendering
- **HTTP Client**: Native http package with JWT authentication
- **Models**: JSON serialization with factory constructors (User, Message, Conversation)
- **Admin Features**: Real-time dashboard with analytics and system monitoring

### Backend (Business Logic Layer)  
- **Framework**: FastAPI (async/await) with production-ready architecture
- **Architecture**: Clean microservices with domain separation and admin features
- **Authentication**: JWT tokens with role-based access control (admin/user roles)
- **Performance**: 1.9s average response time with real-time analytics
- **API Documentation**: Auto-generated OpenAPI/Swagger with interactive interface
- **Validation**: Pydantic models with type safety and error handling
- **Admin Features**: Comprehensive dashboard with user management and system health monitoring

### Development & Deployment
- **Version Control**: Git with clean commit history
- **Documentation**: Markdown with architecture diagrams
- **Testing**: Pytest (backend), Flutter Test (frontend)
- **API Documentation**: Interactive Swagger UI

## 📊 Architecture Benefits

### Development Benefits
- ✅ **Maintainable** - Clear separation of concerns with MVVM and service layers
- ✅ **Scalable** - Microservices can scale independently with admin monitoring
- ✅ **Testable** - Services isolated for unit testing with comprehensive error handling
- ✅ **Extensible** - Easy to add new features/domains with role-based access

### Production Benefits
- ✅ **Performance** - Async/await throughout with 1.9s avg response time
- ✅ **Security** - JWT authentication, role-based access, input validation
- ✅ **Monitoring** - Real-time health checks, admin dashboard, and error tracking
- ✅ **Documentation** - Self-documenting APIs with comprehensive admin features

### Team Benefits
- ✅ **Onboarding** - Clear structure for new developers with comprehensive documentation
- ✅ **Code Review** - Smaller, focused files with SOLID principles
- ✅ **Feature Development** - Domain isolation with admin/user role separation
- ✅ **Debugging** - Issues isolated to specific layers with admin monitoring tools  

## 🤝 Team Members

- Yonn Team 14

- Open-source GPT model communities
- Hugging Face Transformers library
- FastAPI framework contributors
- Docker and containerization tools

---

**Team 14** | **Local AI Assistant** | **Privacy-First**
