/// Application constants
/// Follows Single Responsibility Principle - handles only app-wide constants
class AppConstants {
  static const String appName = 'PM Assistant';
  static const String appVersion = '1.0.0';
  static const String companyName = 'Yonnovia';
  
  // API Configuration - Updated for VPS deployment with AI model
  static const String apiBaseUrl = 'http://************:8000';
  static const int apiTimeout = 60000; // milliseconds - Increased for AI model responses
  static const int maxRetries = 2; // Reduced retries for speed
  static const int streamTimeout = 120000; // milliseconds - For streaming responses
  static const int chatTimeout = 60000; // milliseconds - Specific for chat requests
  
  // Local Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String conversationsKey = 'conversations';
  static const String settingsKey = 'app_settings';
  
  // Validation Rules
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 50;
  static const int maxMessageLength = 1000;
  static const int maxConversationTitle = 100;
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 8.0;
  static const double cardElevation = 2.0;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // Asset Paths
  static const String logoPath = 'assets/images/logo_light_mode.png';
  static const String efficientIconPath = 'assets/images/efficient.png';
  static const String saluteIconPath = 'assets/images/salute.png';
  static const String teamIconPath = 'assets/images/team.png';
  
  // User Roles
  static const String adminRole = 'admin';
  static const String regularRole = 'regular';
  
  // Error Messages
  static const String networkError = 'Network connection failed. Please check your internet connection.';
  static const String unknownError = 'An unexpected error occurred. Please try again.';
  static const String authError = 'Authentication failed. Please login again.';
  static const String validationError = 'Please check your input and try again.';
}
