<svg width="1200" height="800" viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Arial', sans-serif; font-size: 24px; font-weight: bold; fill: #1f2937; }
      .subtitle { font-family: 'Arial', sans-serif; font-size: 16px; font-weight: 600; fill: #374151; }
      .label { font-family: 'Arial', sans-serif; font-size: 12px; fill: #6b7280; }
      .code { font-family: 'Consolas', monospace; font-size: 10px; fill: #1f2937; }
      .view-box { fill: #dbeafe; stroke: #3b82f6; stroke-width: 2; }
      .viewmodel-box { fill: #d1fae5; stroke: #10b981; stroke-width: 2; }
      .model-box { fill: #fef3c7; stroke: #f59e0b; stroke-width: 2; }
      .service-box { fill: #e0e7ff; stroke: #6366f1; stroke-width: 2; }
      .widget-box { fill: #f3e8ff; stroke: #8b5cf6; stroke-width: 2; }
      .arrow { stroke: #4b5563; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-flow { stroke: #ef4444; stroke-width: 2; fill: none; marker-end: url(#redArrow); stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4b5563" />
    </marker>
    <marker id="redArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#ef4444" />
    </marker>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="800" fill="#f9fafb"/>
  
  <!-- Title -->
  <text x="600" y="30" text-anchor="middle" class="title"> MVVM Architecture - PM Assistant Frontend</text>
  
  <!-- Main Architecture Layers -->
  
  <!-- View Layer -->
  <rect x="50" y="80" width="300" height="180" class="view-box" rx="8"/>
  <text x="200" y="100" text-anchor="middle" class="subtitle">VIEW LAYER</text>
  <text x="60" y="120" class="label">UI Components &amp; User Interaction</text>
  
  <!-- View Components -->
  <rect x="70" y="130" width="120" height="40" fill="white" stroke="#3b82f6" rx="4"/>
  <text x="130" y="145" text-anchor="middle" class="code">LoginScreen</text>
  <text x="130" y="155" text-anchor="middle" class="code">ChatScreen</text>
  
  <rect x="210" y="130" width="120" height="40" fill="white" stroke="#3b82f6" rx="4"/>
  <text x="270" y="145" text-anchor="middle" class="code">DashboardScreen</text>
  <text x="270" y="155" text-anchor="middle" class="code">ProfileScreen</text>
  
  <rect x="70" y="180" width="120" height="40" fill="white" stroke="#3b82f6" rx="4"/>
  <text x="130" y="195" text-anchor="middle" class="code">Consumer&amp;lt;VM&amp;gt;</text>
  <text x="130" y="205" text-anchor="middle" class="code">State Updates</text>
  
  <rect x="210" y="180" width="120" height="40" fill="white" stroke="#3b82f6" rx="4"/>
  <text x="270" y="195" text-anchor="middle" class="code">Navigation</text>
  <text x="270" y="205" text-anchor="middle" class="code">User Events</text>
  
  <!-- ViewModel Layer -->
  <rect x="450" y="80" width="300" height="180" class="viewmodel-box" rx="8"/>
  <text x="600" y="100" text-anchor="middle" class="subtitle">VIEWMODEL LAYER</text>
  <text x="460" y="120" class="label">Business Logic &amp; State Management</text>
  
  <rect x="470" y="130" width="120" height="40" fill="white" stroke="#10b981" rx="4"/>
  <text x="530" y="145" text-anchor="middle" class="code">AuthViewModel</text>
  <text x="530" y="155" text-anchor="middle" class="code">ChangeNotifier</text>
  
  <rect x="610" y="130" width="120" height="40" fill="white" stroke="#10b981" rx="4"/>
  <text x="670" y="145" text-anchor="middle" class="code">ChatViewModel</text>
  <text x="670" y="155" text-anchor="middle" class="code">State Logic</text>
  
  <rect x="470" y="180" width="120" height="40" fill="white" stroke="#10b981" rx="4"/>
  <text x="530" y="195" text-anchor="middle" class="code">notifyListeners()</text>
  <text x="530" y="205" text-anchor="middle" class="code">Error Handling</text>
  
  <rect x="610" y="180" width="120" height="40" fill="white" stroke="#10b981" rx="4"/>
  <text x="670" y="195" text-anchor="middle" class="code">Data Validation</text>
  <text x="670" y="205" text-anchor="middle" class="code">Service Calls</text>
  
  <!-- Model Layer -->
  <rect x="850" y="80" width="300" height="180" class="model-box" rx="8"/>
  <text x="1000" y="100" text-anchor="middle" class="subtitle">MODEL LAYER</text>
  <text x="860" y="120" class="label">Data Structures &amp; Entities</text>
  
  <rect x="870" y="130" width="120" height="40" fill="white" stroke="#f59e0b" rx="4"/>
  <text x="930" y="145" text-anchor="middle" class="code">User</text>
  <text x="930" y="155" text-anchor="middle" class="code">Immutable Data</text>
  
  <rect x="1010" y="130" width="120" height="40" fill="white" stroke="#f59e0b" rx="4"/>
  <text x="1070" y="145" text-anchor="middle" class="code">Message</text>
  <text x="1070" y="155" text-anchor="middle" class="code">Conversation</text>
  
  <rect x="870" y="180" width="120" height="40" fill="white" stroke="#f59e0b" rx="4"/>
  <text x="930" y="195" text-anchor="middle" class="code">fromJson()</text>
  <text x="930" y="205" text-anchor="middle" class="code">toJson()</text>
  
  <rect x="1010" y="180" width="120" height="40" fill="white" stroke="#f59e0b" rx="4"/>
  <text x="1070" y="195" text-anchor="middle" class="code">Validation</text>
  <text x="1070" y="205" text-anchor="middle" class="code">Business Rules</text>
  
  <!-- Service Layer -->
  <rect x="450" y="320" width="300" height="120" class="service-box" rx="8"/>
  <text x="600" y="340" text-anchor="middle" class="subtitle">SERVICE LAYER</text>
  <text x="460" y="360" class="label">Data Access &amp; External APIs</text>
  
  <rect x="470" y="370" width="120" height="40" fill="white" stroke="#6366f1" rx="4"/>
  <text x="530" y="385" text-anchor="middle" class="code">AuthService</text>
  <text x="530" y="395" text-anchor="middle" class="code">API Calls</text>
  
  <rect x="610" y="370" width="120" height="40" fill="white" stroke="#6366f1" rx="4"/>
  <text x="670" y="385" text-anchor="middle" class="code">ChatService</text>
  <text x="670" y="395" text-anchor="middle" class="code">Mock Data</text>
  
  <!-- Widget Components Section -->
  <rect x="50" y="320" width="350" height="280" class="widget-box" rx="8"/>
  <text x="225" y="340" text-anchor="middle" class="subtitle">WIDGET COMPONENTS (SOLID)</text>
  <text x="60" y="360" class="label">Extracted, Reusable UI Components</text>
  
  <!-- Widget Folders -->
  <rect x="70" y="380" width="80" height="50" fill="white" stroke="#8b5cf6" rx="4"/>
  <text x="110" y="395" text-anchor="middle" class="code">admin/</text>
  <text x="110" y="405" text-anchor="middle" class="label">StatCard</text>
  <text x="110" y="415" text-anchor="middle" class="label">HealthItem</text>
  
  <rect x="160" y="380" width="80" height="50" fill="white" stroke="#8b5cf6" rx="4"/>
  <text x="200" y="395" text-anchor="middle" class="code">auth/</text>
  <text x="200" y="405" text-anchor="middle" class="label">RoleCard</text>
  <text x="200" y="415" text-anchor="middle" class="label">LoginForm</text>
  
  <rect x="250" y="380" width="80" height="50" fill="white" stroke="#8b5cf6" rx="4"/>
  <text x="290" y="395" text-anchor="middle" class="code">chat/</text>
  <text x="290" y="405" text-anchor="middle" class="label">ChatMessage</text>
  <text x="290" y="415" text-anchor="middle" class="label">EmptyState</text>
  
  <rect x="70" y="440" width="80" height="50" fill="white" stroke="#8b5cf6" rx="4"/>
  <text x="110" y="455" text-anchor="middle" class="code">common/</text>
  <text x="110" y="465" text-anchor="middle" class="label">YonnoviaButton</text>
  <text x="110" y="475" text-anchor="middle" class="label">YonnoviaCard</text>
  
  <rect x="160" y="440" width="80" height="50" fill="white" stroke="#8b5cf6" rx="4"/>
  <text x="200" y="455" text-anchor="middle" class="code">dashboard/</text>
  <text x="200" y="465" text-anchor="middle" class="label">StatItem</text>
  <text x="200" y="475" text-anchor="middle" class="label">QuickAction</text>
  
  <rect x="250" y="440" width="80" height="50" fill="white" stroke="#8b5cf6" rx="4"/>
  <text x="290" y="455" text-anchor="middle" class="code">profile/</text>
  <text x="290" y="465" text-anchor="middle" class="label">ThemeSelector</text>
  <text x="290" y="475" text-anchor="middle" class="label">LanguageSelector</text>
  
  <!-- SOLID Principles Box -->
  <rect x="70" y="510" width="260" height="70" fill="#fef9e7" stroke="#f59e0b" rx="4"/>
  <text x="200" y="525" text-anchor="middle" class="subtitle">SOLID Principles Applied</text>
  <text x="80" y="540" class="label">✓ Single Responsibility: One widget per file</text>
  <text x="80" y="552" class="label">✓ Open/Closed: Extensible through parameters</text>
  <text x="80" y="564" class="label">✓ Dependency Inversion: Service abstractions</text>
  
  <!-- Provider State Management -->
  <rect x="850" y="320" width="300" height="120" class="view-box" rx="8"/>
  <text x="1000" y="340" text-anchor="middle" class="subtitle">STATE MANAGEMENT</text>
  <text x="860" y="360" class="label">Provider Pattern Implementation</text>
  
  <rect x="870" y="370" width="120" height="40" fill="white" stroke="#3b82f6" rx="4"/>
  <text x="930" y="385" text-anchor="middle" class="code">MultiProvider</text>
  <text x="930" y="395" text-anchor="middle" class="code">Dependency Injection</text>
  
  <rect x="1010" y="370" width="120" height="40" fill="white" stroke="#3b82f6" rx="4"/>
  <text x="1070" y="385" text-anchor="middle" class="code">Consumer&amp;lt;T&amp;gt;</text>
  <text x="1070" y="395" text-anchor="middle" class="code">State Listening</text>
  
  <!-- Data Flow Arrows -->
  
  <!-- View to ViewModel -->
  <line x1="350" y1="170" x2="450" y2="170" class="arrow"/>
  <text x="385" y="165" class="label">User Actions</text>
  
  <!-- ViewModel to View -->
  <line x1="450" y1="190" x2="350" y2="190" class="arrow"/>
  <text x="385" y="205" class="label">State Updates</text>
  
  <!-- ViewModel to Model -->
  <line x1="750" y1="150" x2="850" y2="150" class="arrow"/>
  <text x="785" y="145" class="label">Data Operations</text>
  
  <!-- ViewModel to Service -->
  <line x1="600" y1="260" x2="600" y2="320" class="arrow"/>
  <text x="610" y="290" class="label">Service Calls</text>
  
  <!-- Service to ViewModel -->
  <line x1="580" y1="320" x2="580" y2="260" class="arrow"/>
  <text x="520" y="290" class="label">Data Response</text>
  
  <!-- Widget to View Connection -->
  <line x1="225" y1="320" x2="200" y2="260" class="arrow"/>
  <text x="160" y="290" class="label">Composition</text>
  
  <!-- Data Flow Legend -->
  <rect x="50" y="650" width="1100" height="120" fill="#f8fafc" stroke="#e5e7eb" rx="8"/>
  <text x="600" y="675" text-anchor="middle" class="subtitle">Data Flow &amp; Architecture Benefits</text>
  
  <!-- Flow Diagram -->
  <text x="70" y="700" class="label">Data Flow:</text>
  <circle cx="150" cy="695" r="8" fill="#3b82f6"/>
  <text x="170" y="700" class="code">User Input</text>
  
  <line x1="250" y1="695" x2="290" y2="695" class="data-flow"/>
  
  <circle cx="320" cy="695" r="8" fill="#10b981"/>
  <text x="340" y="700" class="code">ViewModel</text>
  
  <line x1="420" y1="695" x2="460" y2="695" class="data-flow"/>
  
  <circle cx="490" cy="695" r="8" fill="#6366f1"/>
  <text x="510" y="700" class="code">Service</text>
  
  <line x1="580" y1="695" x2="620" y2="695" class="data-flow"/>
  
  <circle cx="650" cy="695" r="8" fill="#f59e0b"/>
  <text x="670" y="700" class="code">Model</text>
  
  <line x1="730" y1="695" x2="770" y2="695" class="data-flow"/>
  
  <circle cx="800" cy="695" r="8" fill="#3b82f6"/>
  <text x="820" y="700" class="code">UI Update</text>
  
  <!-- Benefits -->
  <text x="70" y="730" class="subtitle">Architecture Benefits:</text>
  <text x="70" y="745" class="label">✓ Separation of Concerns</text>
  <text x="250" y="745" class="label">✓ Testable Components</text>
  <text x="430" y="745" class="label">✓ Reusable Widgets</text>
  <text x="600" y="745" class="label">✓ Maintainable Code</text>
  <text x="780" y="745" class="label">✓ Scalable Architecture</text>
  <text x="970" y="745" class="label">✓ SOLID Principles</text>
</svg>
