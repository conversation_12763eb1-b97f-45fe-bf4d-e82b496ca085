/// Validation utilities
/// Follows Single Responsibility Principle - handles only validation logic
class ValidationUtils {
  /// Validates email format
  static bool isValidEmail(String email) {
    if (email.isEmpty) return false;
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Validates password strength
  static bool isValidPassword(String password) {
    if (password.isEmpty) return false;
    if (password.length < 6) return false;
    return true;
  }

  /// Validates password with specific requirements
  static PasswordValidationResult validatePasswordStrength(String password) {
    final result = PasswordValidationResult();
    
    if (password.isEmpty) {
      result.addError('Password cannot be empty');
      return result;
    }
    
    if (password.length < 6) {
      result.addError('Password must be at least 6 characters long');
    }
    
    if (password.length > 50) {
      result.addError('Password cannot exceed 50 characters');
    }
    
    if (!RegExp(r'^(?=.*[a-zA-Z])').hasMatch(password)) {
      result.addError('Password must contain at least one letter');
    }
    
    return result;
  }

  /// Validates name field
  static bool isValidName(String name) {
    if (name.isEmpty) return false;
    if (name.trim().length < 2) return false;
    return RegExp(r'^[a-zA-Z\s]+$').hasMatch(name.trim());
  }

  /// Validates message content
  static bool isValidMessage(String message) {
    if (message.trim().isEmpty) return false;
    if (message.length > 1000) return false;
    return true;
  }

  /// Validates user role
  static bool isValidRole(String role) {
    return ['admin', 'regular'].contains(role.toLowerCase());
  }

  /// Sanitizes user input
  static String sanitizeInput(String input) {
    return input.trim().replaceAll(RegExp(r'\s+'), ' ');
  }
}

/// Result of password validation
class PasswordValidationResult {
  final List<String> _errors = [];
  
  List<String> get errors => List.unmodifiable(_errors);
  bool get isValid => _errors.isEmpty;
  String get firstError => _errors.isNotEmpty ? _errors.first : '';
  
  void addError(String error) {
    _errors.add(error);
  }
}
