import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../viewmodels/auth_viewmodel.dart';
import '../viewmodels/chat_viewmodel.dart';
import '../widgets/common/yonnovia_card.dart';
import '../widgets/common/yonnovia_header.dart';
import '../widgets/common/logout_dialog.dart';
import '../widgets/dashboard/stat_item.dart';
import '../widgets/dashboard/quick_action_card.dart';
import '../widgets/dashboard/language_button.dart';
import '../../core/constants/app_colors.dart';
import 'chat_screen.dart';
import 'chat_history_screen.dart';
import 'profile_settings_screen.dart';

class RegularDashboardScreen extends StatefulWidget {
  const RegularDashboardScreen({super.key});

  @override
  RegularDashboardScreenState createState() => RegularDashboardScreenState();
}

class RegularDashboardScreenState extends State<RegularDashboardScreen> {
  String _selectedLanguage = 'EN';

  @override
  void initState() {
    super.initState();
    // Load conversations when dashboard initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final chatViewModel = Provider.of<ChatViewModel>(context, listen: false);
      chatViewModel.refreshConversations();
    });
  }

  @override
  Widget build(BuildContext context) {
    final authViewModel = context.watch<AuthViewModel>();
    final chatViewModel = context.watch<ChatViewModel>();
    final user = authViewModel.user;
    final userName = user?.name.split(' ').firstOrNull ?? 'User'; // Get first name only
    final conversationCount = chatViewModel.conversations.length;
    
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header with logo and user info
              YonnoviaHeader(
                title: 'Welcome, $userName',
                subtitle: 'Ready to boost your productivity?',
                showBackButton: false,
                actions: [
                  IconButton(
                    onPressed: () => LogoutDialog.show(context),
                    icon: const Icon(Icons.logout, color: AppColors.white, size: 24),
                    tooltip: 'Logout',
                  ),
                ],
              ),
              // Content
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    children: [
                      // Welcome Section
                      YonnoviaGradientCard(
                        padding: const EdgeInsets.all(32),
                        child: Column(
                          children: [
                            // Convo Illustration
                            Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: AppColors.white.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: AppColors.white.withOpacity(0.3),
                                  width: 1,
                                ),
                              ),
                              child: Image.asset(
                                'assets/images/convo.png',
                                width: 60,
                                height: 60,
                              ),
                            ),
                            const SizedBox(height: 20),
                            Text(
                              'Hello, $userName! 👋',
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: AppColors.white,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Your AI-powered project management assistant is ready to help',
                              style: TextStyle(
                                fontSize: 16,
                                color: AppColors.white.withOpacity(0.9),
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 20),
                            // Quick Stats
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                StatItem(
                                  emoji: '🎯',
                                  value: 'Soon',
                                  label: 'Active Tasks',
                                ),
                                StatItem(
                                  emoji: '📊',
                                  value: 'Soon',
                                  label: 'Projects',
                                ),
                                StatItem(
                                  emoji: '💬',
                                  value: conversationCount.toString(),
                                  label: 'Conversations',
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 32),
                      // Quick Actions Grid
                      YonnoviaCard(
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: AppColors.primaryTeal.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Image.asset(
                                    'assets/images/helper.png',
                                    width: 20,
                                    height: 20,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                const Text(
                                  'Quick Actions',
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.textPrimary,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 24),
                            GridView.count(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              crossAxisCount: 2,
                              crossAxisSpacing: 16,
                              mainAxisSpacing: 16,
                              childAspectRatio: 1.1,
                              children: [
                                QuickActionCard(
                                  title: 'Start Chat',
                                  description: 'Chat with your PM Assistant',
                                  assetPath: 'assets/images/convo.png',
                                  color: AppColors.primaryTeal,
                                  onTap: () {
                                    // Start a new conversation before navigating
                                    context.read<ChatViewModel>().startNewConversation();
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(builder: (_) => const ChatScreen()),
                                    );
                                  },
                                ),
                                QuickActionCard(
                                  title: 'View History',
                                  description: 'Browse past conversations',
                                  assetPath: 'assets/images/notif.png',
                                  color: AppColors.secondaryGreen,
                                  onTap: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(builder: (_) => const ChatHistoryScreen()),
                                    );
                                  },
                                ),
                                QuickActionCard(
                                  title: 'Profile',
                                  description: 'Manage your settings',
                                  assetPath: 'assets/images/settings.png',
                                  color: AppColors.info,
                                  onTap: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(builder: (_) => const ProfileSettingsScreen()),
                                    );
                                  },
                                ),
                                QuickActionCard(
                                  title: 'Project Map',
                                  description: 'View project overview',
                                  assetPath: 'assets/images/map.png',
                                  color: AppColors.warning,
                                  onTap: () {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text('Feature coming soon!'),
                                        backgroundColor: AppColors.info,
                                      ),
                                    );
                                  },
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),
                      // Language Switch
                      YonnoviaCard(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: AppColors.primaryTeal.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: const Icon(
                                    Icons.language,
                                    color: AppColors.primaryTeal,
                                    size: 20,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                const Text(
                                  'Language Settings',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.textPrimary,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: AppColors.gray100,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                children: [
                                  LanguageButton(
                                    code: 'EN',
                                    name: 'English',
                                    isSelected: _selectedLanguage == 'EN',
                                    onTap: () {
                                      setState(() {
                                        _selectedLanguage = 'EN';
                                      });
                                    },
                                  ),
                                  LanguageButton(
                                    code: 'FR',
                                    name: 'Français',
                                    isSelected: _selectedLanguage == 'FR',
                                    onTap: () {
                                      setState(() {
                                        _selectedLanguage = 'FR';
                                      });
                                    },
                                  ),
                                  LanguageButton(
                                    code: 'AR',
                                    name: 'العربية',
                                    isSelected: _selectedLanguage == 'AR',
                                    onTap: () {
                                      setState(() {
                                        _selectedLanguage = 'AR';
                                      });
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
