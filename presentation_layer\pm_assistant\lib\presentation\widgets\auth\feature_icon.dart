import 'package:flutter/material.dart';

/// Feature icon widget following Single Responsibility Principle
/// Handles only feature icon display UI logic
class FeatureIcon extends StatelessWidget {
  final String assetPath;
  final String label;

  const FeatureIcon({
    super.key,
    required this.assetPath,
    required this.label,
  });

  IconData _getIconForAsset(String assetPath) {
    if (assetPath.contains('light_bulb') || assetPath.contains('smart')) {
      return Icons.lightbulb;
    } else if (assetPath.contains('teamwork') || assetPath.contains('team')) {
      return Icons.groups;
    } else if (assetPath.contains('efficient') || assetPath.contains('speed')) {
      return Icons.speed;
    } else {
      return Icons.star;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Color(0xFF4FD1C7).withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Color(0xFF4FD1C7).withOpacity(0.4),
            ),
            boxShadow: [
              BoxShadow(
                color: Color(0xFF4FD1C7).withOpacity(0.2),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Image.asset(
            assetPath,
            width: 32,
            height: 32,
            fit: BoxFit.contain,
            errorBuilder: (context, error, stackTrace) {
              return Icon(
                _getIconForAsset(assetPath),
                size: 32,
                color: Color(0xFF4FD1C7),
              );
            },
          ),
        ),
        SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(
            fontSize: 13,
            color: Color(0xFF374151),
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}
