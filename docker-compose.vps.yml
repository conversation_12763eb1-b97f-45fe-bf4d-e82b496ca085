

services:
  pm-assistant:
    build:
      context: .
      dockerfile: dockerfile.vps
    ports:
      - "8000:8000"
    volumes:
      - ./business_logic_layer/Docs:/app/business_logic_layer/Docs:ro
      - ./business_logic_layer/models:/app/business_logic_layer/models:ro
      - pm_logs:/app/logs
    user: "1000:1000"  # Use ubuntu user ID to access files
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - LOG_LEVEL=INFO
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - API_RELOAD=false
      - PYTHONPATH=/app
      # HIGH PERFORMANCE VPS Optimizations (15GB RAM, 8 vCPU)
      - LLM_N_THREADS=7          # Use 7 of 8 cores (leave 1 for system)
      - LLM_N_CTX=1024           # Optimized context for speed
      - LLM_MAX_TOKENS=300       # Faster generation
      - LLM_N_BATCH=512          # Larger batch for better throughput
      - LLM_USE_MMAP=true        # Memory mapping for 15GB RAM
      - LLM_USE_MLOCK=true       # Lock memory for performance
      - LLM_F16_KV=true          # Half precision for speed
    container_name: pm-assistant-vps
    restart: always
    deploy:
      resources:
        limits:
          memory: 12G            # Use 12GB of 15GB available
          cpus: '7.5'            # Use 7.5 of 8 cores
        reservations:
          memory: 8G             # Reserve 8GB for consistent performance
          cpus: '6.0'            # Reserve 6 cores
    networks:
      - pm-network
    depends_on:
      - redis
      - postgres
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    container_name: pm-redis-vps
    restart: always
    command: redis-server --appendonly yes --maxmemory 1gb --maxmemory-policy allkeys-lru
    networks:
      - pm-network
    deploy:
      resources:
        limits:
          memory: 1.5G
          cpus: '1.0'
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=pm_assistant_prod
      - POSTGRES_USER=pm_user_prod
      - POSTGRES_PASSWORD=SecurePassword123!
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    container_name: pm-postgres-vps
    restart: always
    networks:
      - pm-network
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U pm_user_prod -d pm_assistant_prod"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  pm-network:
    driver: bridge

volumes:
  pm_logs:
    driver: local
  redis_data:
    driver: local
  postgres_data:
    driver: local
