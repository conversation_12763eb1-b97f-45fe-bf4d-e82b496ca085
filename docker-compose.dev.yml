version: '3.8'

services:
  pm-assistant-dev:
    build:
      context: .
      dockerfile: dockerfile.dev
    ports:
      - "8000:8000"
      - "5678:5678"  # Debug port
    volumes:
      # Mount source code for hot reload
      - ./business_logic_layer:/app/business_logic_layer
      - ./business_logic_layer/Docs:/app/business_logic_layer/Docs
      - ./business_logic_layer/models:/app/business_logic_layer/models:ro
      - pm_dev_logs:/app/logs
      # Cache pip packages
      - pip_cache:/home/<USER>/.cache/pip
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - DEVELOPMENT=1
      - LOG_LEVEL=DEBUG
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - API_RELOAD=true
    container_name: pm-assistant-dev
    restart: unless-stopped
    stdin_open: true
    tty: true
    networks:
      - pm-dev-network

  # Optional: Redis for caching in development
  redis-dev:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    container_name: pm-redis-dev
    networks:
      - pm-dev-network

  # Optional: PostgreSQL for development database
  postgres-dev:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=pm_assistant_dev
      - POSTGRES_USER=pm_user
      - POSTGRES_PASSWORD=pm_password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    container_name: pm-postgres-dev
    networks:
      - pm-dev-network

networks:
  pm-dev-network:
    driver: bridge

volumes:
  pm_dev_logs:
    driver: local
  pip_cache:
    driver: local
  redis_dev_data:
    driver: local
  postgres_dev_data:
    driver: local
