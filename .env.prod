# Production Environment Configuration
# IMPORTANT: Update all passwords and secrets before deploying!

# Application Settings
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=false
LOG_LEVEL=INFO
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1

# Model Configuration
MODEL_PATH=/app/business_logic_layer/models/qwen25-7b-project-mgmt-q5_k_m.gguf
DOCS_FOLDER=/app/business_logic_layer/Docs

# RAG System Settings
VECTOR_STORE_PATH=pm_rag_store.pkl.gz
EMBEDDING_DIM=384
MAX_DOC_CHUNK_SIZE=800
CHUNK_OVERLAP=150
RELEVANCE_THRESHOLD=0.5
MIN_PM_KEYWORDS=2

# LLM Settings (optimized for production)
LLM_MAX_TOKENS=800
LLM_TEMPERATURE=0.7
LLM_N_CTX=2048
LLM_N_THREADS=8
LLM_N_GPU_LAYERS=0

# Conversation Cache Settings
MAX_SESSIONS=1000
MAX_MESSAGES_PER_SESSION=50
SESSION_TIMEOUT_HOURS=12

# CORS Settings (restrict in production)
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
CORS_METHODS=GET,POST,PUT,DELETE
CORS_HEADERS=*

# Database Settings
POSTGRES_DB=pm_assistant
POSTGRES_USER=pm_user
POSTGRES_PASSWORD=CHANGE_THIS_SECURE_PASSWORD
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# Redis Settings
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=CHANGE_THIS_REDIS_PASSWORD
REDIS_DB=0

# Security Settings (CHANGE THESE!)
SECRET_KEY=CHANGE_THIS_TO_A_SECURE_RANDOM_STRING
JWT_SECRET_KEY=CHANGE_THIS_TO_A_SECURE_JWT_SECRET
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# File Upload Settings
MAX_FILE_SIZE=50MB
ALLOWED_FILE_TYPES=pdf,docx,txt,md

# Monitoring and Logging
ENABLE_METRICS=true
METRICS_PORT=9090
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# SSL/TLS Settings
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem
