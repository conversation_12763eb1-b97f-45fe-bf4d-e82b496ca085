"""
RAG Service
===========
Handles document embedding, chunking, and retrieval for enhanced AI responses.
"""

import os
import pickle
import gzip
import logging
import hashlib
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone

import numpy as np
import faiss
from sentence_transformers import SentenceTransformer

class DocumentChunk:
    """Represents a chunk of a document with metadata"""
    def __init__(self, content: str, source: str, chunk_id: str, metadata: Dict[str, Any] = None):
        self.content = content
        self.source = source
        self.chunk_id = chunk_id
        self.metadata = metadata or {}
        self.embedding = None
        self.created_at = datetime.now(timezone.utc)

class RAGService:
    """Service for Retrieval-Augmented Generation using vector store"""
    
    def __init__(self):
        # Use /tmp for writable storage in container
        self.vector_store_path = "/tmp/pm_rag_store.pkl.gz"
        self.docs_folder = "/tmp/docs"
        self.embedding_model = None
        self.vector_store = {
            'chunks': [],
            'embeddings': [],
            'metadata': {
                'created_at': datetime.now(timezone.utc),
                'total_documents': 0,
                'total_chunks': 0,
                'last_updated': datetime.now(timezone.utc)
            }
        }
        self._load_embedding_model()
        self._load_vector_store()
    
    def _load_embedding_model(self):
        """Load the sentence transformer model for embeddings"""
        try:
            logging.info("🔄 Loading embedding model...")
            # Set cache directory to writable location
            import os
            os.environ['TRANSFORMERS_CACHE'] = '/tmp/transformers_cache'
            os.environ['HF_HOME'] = '/tmp/huggingface'

            # Create cache directories
            os.makedirs('/tmp/transformers_cache', exist_ok=True)
            os.makedirs('/tmp/huggingface', exist_ok=True)

            # Use a lightweight model for VPS
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2', cache_folder='/tmp/transformers_cache')
            logging.info("✅ Embedding model loaded successfully")
        except Exception as e:
            logging.error(f"❌ Error loading embedding model: {str(e)}")
            logging.info("🔄 Running without embedding model - using fallback responses only")
            self.embedding_model = None
    
    def _load_vector_store(self):
        """Load existing vector store or create new one"""
        try:
            if os.path.exists(self.vector_store_path):
                logging.info(f"📂 Loading vector store from: {self.vector_store_path}")
                with gzip.open(self.vector_store_path, 'rb') as f:
                    self.vector_store = pickle.load(f)
                logging.info(f"✅ Vector store loaded: {len(self.vector_store['chunks'])} chunks")
            else:
                logging.info("📝 Creating new vector store")
                self._save_vector_store()
        except Exception as e:
            logging.error(f"❌ Error loading vector store: {str(e)}")
            # Create empty vector store
            self._save_vector_store()
    
    def _save_vector_store(self):
        """Save vector store to disk"""
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.vector_store_path), exist_ok=True)
            
            # Update metadata
            self.vector_store['metadata']['last_updated'] = datetime.now(timezone.utc)
            self.vector_store['metadata']['total_chunks'] = len(self.vector_store['chunks'])
            
            with gzip.open(self.vector_store_path, 'wb') as f:
                pickle.dump(self.vector_store, f)
            logging.info(f"💾 Vector store saved: {len(self.vector_store['chunks'])} chunks")
        except Exception as e:
            logging.error(f"❌ Error saving vector store: {str(e)}")
    
    def _chunk_text(self, text: str, chunk_size: int = 500, overlap: int = 50) -> List[str]:
        """Split text into overlapping chunks"""
        words = text.split()
        chunks = []
        
        for i in range(0, len(words), chunk_size - overlap):
            chunk = ' '.join(words[i:i + chunk_size])
            if chunk.strip():
                chunks.append(chunk.strip())
        
        return chunks
    
    def add_document(self, content: str, source: str, metadata: Dict[str, Any] = None) -> bool:
        """Add a document to the vector store"""
        try:
            if not self.embedding_model:
                logging.warning("⚠️ Embedding model not available - document not added")
                return False
            
            logging.info(f"📄 Processing document: {source}")
            
            # Generate document hash for deduplication
            doc_hash = hashlib.md5(content.encode()).hexdigest()
            
            # Check if document already exists
            for chunk in self.vector_store['chunks']:
                if chunk.metadata.get('doc_hash') == doc_hash:
                    logging.info(f"⚠️ Document already exists: {source}")
                    return True
            
            # Chunk the document
            chunks = self._chunk_text(content)
            logging.info(f"🔪 Created {len(chunks)} chunks from document")
            
            # Generate embeddings for chunks
            chunk_embeddings = self.embedding_model.encode(chunks)
            
            # Add chunks to vector store
            for i, (chunk_text, embedding) in enumerate(zip(chunks, chunk_embeddings)):
                chunk_id = f"{doc_hash}_{i}"
                chunk_metadata = {
                    'doc_hash': doc_hash,
                    'chunk_index': i,
                    'total_chunks': len(chunks),
                    **(metadata or {})
                }
                
                document_chunk = DocumentChunk(
                    content=chunk_text,
                    source=source,
                    chunk_id=chunk_id,
                    metadata=chunk_metadata
                )
                document_chunk.embedding = embedding
                
                self.vector_store['chunks'].append(document_chunk)
                self.vector_store['embeddings'].append(embedding)
            
            # Update metadata
            self.vector_store['metadata']['total_documents'] += 1
            
            # Save vector store
            self._save_vector_store()
            
            logging.info(f"✅ Document added successfully: {source}")
            return True
            
        except Exception as e:
            logging.error(f"❌ Error adding document: {str(e)}")
            return False
    
    def search_similar_chunks(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search for similar chunks using cosine similarity"""
        try:
            if not self.embedding_model or not self.vector_store['chunks']:
                return []
            
            # Generate query embedding
            query_embedding = self.embedding_model.encode([query])
            
            # Calculate similarities
            embeddings_matrix = np.array(self.vector_store['embeddings'])
            similarities = cosine_similarity(query_embedding, embeddings_matrix)[0]
            
            # Get top-k most similar chunks
            top_indices = np.argsort(similarities)[::-1][:top_k]
            
            results = []
            for idx in top_indices:
                chunk = self.vector_store['chunks'][idx]
                results.append({
                    'content': chunk.content,
                    'source': chunk.source,
                    'similarity': float(similarities[idx]),
                    'metadata': chunk.metadata
                })
            
            logging.info(f"🔍 Found {len(results)} similar chunks for query")
            return results
            
        except Exception as e:
            logging.error(f"❌ Error searching chunks: {str(e)}")
            return []
    
    def get_context_for_query(self, query: str, max_context_length: int = 2000) -> str:
        """Get relevant context for a query"""
        similar_chunks = self.search_similar_chunks(query, top_k=5)
        
        if not similar_chunks:
            return "No relevant context found in the knowledge base."
        
        context_parts = []
        current_length = 0
        
        for chunk in similar_chunks:
            chunk_text = f"Source: {chunk['source']}\nContent: {chunk['content']}\n"
            if current_length + len(chunk_text) <= max_context_length:
                context_parts.append(chunk_text)
                current_length += len(chunk_text)
            else:
                break
        
        return "\n---\n".join(context_parts)
    
    def get_vector_store_stats(self) -> Dict[str, Any]:
        """Get statistics about the vector store"""
        return {
            'total_chunks': len(self.vector_store['chunks']),
            'total_documents': self.vector_store['metadata']['total_documents'],
            'last_updated': self.vector_store['metadata']['last_updated'],
            'embedding_model_loaded': self.embedding_model is not None,
            'vector_store_size_mb': os.path.getsize(self.vector_store_path) / (1024*1024) if os.path.exists(self.vector_store_path) else 0
        }
    
    def delete_document(self, source: str) -> bool:
        """Delete all chunks from a specific document"""
        try:
            initial_count = len(self.vector_store['chunks'])
            
            # Remove chunks from the specified source
            self.vector_store['chunks'] = [
                chunk for chunk in self.vector_store['chunks'] 
                if chunk.source != source
            ]
            
            # Rebuild embeddings array
            self.vector_store['embeddings'] = [
                chunk.embedding for chunk in self.vector_store['chunks']
            ]
            
            removed_count = initial_count - len(self.vector_store['chunks'])
            
            if removed_count > 0:
                self.vector_store['metadata']['total_documents'] -= 1
                self._save_vector_store()
                logging.info(f"🗑️ Removed {removed_count} chunks from document: {source}")
                return True
            else:
                logging.info(f"⚠️ No chunks found for document: {source}")
                return False
                
        except Exception as e:
            logging.error(f"❌ Error deleting document: {str(e)}")
            return False
