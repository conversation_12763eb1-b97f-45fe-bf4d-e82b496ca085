"""
Document Service
===============
Handles document processing, storage, and retrieval.
"""

import os
import uuid
import shutil
from datetime import datetime
from typing import List, Optional, Dict, Any, BinaryIO
from pathlib import Path

import PyPDF2
from fastapi import HTTPException, UploadFile, BackgroundTasks
from pydantic import BaseModel

# Models
class Document(BaseModel):
    id: str
    filename: str
    content: str
    user_id: str
    upload_date: datetime
    file_type: str
    file_size: int

class DocumentInfo(BaseModel):
    id: str
    filename: str
    user_id: str
    upload_date: datetime
    file_type: str
    file_size: int

class DocumentResponse(BaseModel):
    success: bool
    document: DocumentInfo
    message: str

class DocumentService:
    def __init__(self, storage_path: str = "document_storage"):
        """Initialize document service with storage path"""
        self.storage_path = storage_path
        self.documents: Dict[str, Document] = {}
        
        # Create storage directory if it doesn't exist
        Path(storage_path).mkdir(parents=True, exist_ok=True)
    
    async def process_document(self, file: UploadFile, user_id: str, background_tasks: BackgroundTasks) -> DocumentResponse:
        """Process and store uploaded document"""
        # Validate file
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")
        
        # Generate unique ID for document
        doc_id = str(uuid.uuid4())
        file_extension = self._get_file_extension(file.filename)
        
        if not self._is_supported_file_type(file_extension):
            raise HTTPException(status_code=400, detail=f"Unsupported file type: {file_extension}")
        
        # Store file physically
        file_path = self._get_storage_path(doc_id, file.filename)
        try:
            with open(file_path, "wb") as buffer:
                # Copy content from uploaded file to storage
                shutil.copyfileobj(file.file, buffer)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to store file: {str(e)}")
        finally:
            file.file.close()
        
        # Extract text content
        try:
            content = await self._extract_text(file_path, file_extension)
        except Exception as e:
            # If text extraction fails, delete the stored file
            if os.path.exists(file_path):
                os.remove(file_path)
            raise HTTPException(status_code=500, detail=f"Failed to extract text: {str(e)}")
        
        # Get file size
        file_size = os.path.getsize(file_path)
        
        # Create document record
        document = Document(
            id=doc_id,
            filename=file.filename,
            content=content,
            user_id=user_id,
            upload_date=datetime.utcnow(),
            file_type=file_extension,
            file_size=file_size
        )
        
        # Store in memory
        self.documents[doc_id] = document
        
        # Create document info (without content)
        doc_info = DocumentInfo(
            id=doc_id,
            filename=file.filename,
            user_id=user_id,
            upload_date=document.upload_date,
            file_type=file_extension,
            file_size=file_size
        )
        
        # Return document response
        return DocumentResponse(
            success=True,
            document=doc_info,
            message="Document uploaded and processed successfully"
        )
    
    async def get_document(self, doc_id: str, user_id: str) -> Document:
        """Retrieve document by ID"""
        if doc_id not in self.documents:
            raise HTTPException(status_code=404, detail="Document not found")
        
        document = self.documents[doc_id]
        
        # Check if user has access to the document
        if document.user_id != user_id:
            raise HTTPException(status_code=403, detail="Access denied")
            
        return document
    
    async def get_user_documents(self, user_id: str) -> List[DocumentInfo]:
        """Get all documents for a specific user"""
        user_docs = []
        
        for doc in self.documents.values():
            if doc.user_id == user_id:
                # Convert to DocumentInfo (without content)
                doc_info = DocumentInfo(
                    id=doc.id,
                    filename=doc.filename,
                    user_id=doc.user_id,
                    upload_date=doc.upload_date,
                    file_type=doc.file_type,
                    file_size=doc.file_size
                )
                user_docs.append(doc_info)
                
        return user_docs
    
    async def delete_document(self, doc_id: str, user_id: str) -> bool:
        """Delete document by ID"""
        if doc_id not in self.documents:
            raise HTTPException(status_code=404, detail="Document not found")
        
        document = self.documents[doc_id]
        
        # Check if user has access to the document
        if document.user_id != user_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Delete physical file
        file_path = self._get_storage_path(doc_id, document.filename)
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"Failed to delete file: {str(e)}")
        
        # Remove from memory
        del self.documents[doc_id]
        
        return True
    
    def _get_storage_path(self, doc_id: str, filename: str) -> str:
        """Get the storage path for a document"""
        return os.path.join(self.storage_path, f"{doc_id}_{filename}")
    
    def _get_file_extension(self, filename: str) -> str:
        """Extract file extension from filename"""
        return os.path.splitext(filename)[1].lower().lstrip(".")
    
    def _is_supported_file_type(self, file_extension: str) -> bool:
        """Check if file type is supported"""
        supported_extensions = ["pdf", "txt", "docx", "doc"]
        return file_extension in supported_extensions
    
    async def _extract_text(self, file_path: str, file_extension: str) -> str:
        """Extract text from document based on file type"""
        if file_extension == "pdf":
            return self._extract_text_from_pdf(file_path)
        elif file_extension == "txt":
            return self._extract_text_from_txt(file_path)
        elif file_extension in ["doc", "docx"]:
            # For demo purposes, we'll just return a placeholder
            # In a real implementation, you would use a library like python-docx
            return f"[Text extracted from {file_extension.upper()} document]"
        else:
            return f"[Unsupported file type: {file_extension}]"
    
    def _extract_text_from_pdf(self, file_path: str) -> str:
        """Extract text from PDF file"""
        text = ""
        try:
            with open(file_path, "rb") as file:
                reader = PyPDF2.PdfReader(file)
                for page_num in range(len(reader.pages)):
                    text += reader.pages[page_num].extract_text() + "\n"
            return text
        except Exception as e:
            return f"[Error extracting PDF text: {str(e)}]"
    
    def _extract_text_from_txt(self, file_path: str) -> str:
        """Extract text from plain text file"""
        try:
            with open(file_path, "r", encoding="utf-8") as file:
                return file.read()
        except UnicodeDecodeError:
            # Try with a different encoding if UTF-8 fails
            try:
                with open(file_path, "r", encoding="latin-1") as file:
                    return file.read()
            except Exception as e:
                return f"[Error extracting text: {str(e)}]"
