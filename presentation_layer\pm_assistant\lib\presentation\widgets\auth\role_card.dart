import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';
import '../common/yonnovia_card.dart';

/// Role card widget following Single Responsibility Principle
/// Handles only role selection card display UI logic
class RoleCard extends StatelessWidget {
  final String title;
  final String description;
  final IconData icon;
  final List<String> features;
  final String assetPath;
  final VoidCallback? onTap;
  final bool isAdmin;
  final bool isEnabled;

  const RoleCard({
    super.key,
    required this.title,
    required this.description,
    required this.icon,
    required this.features,
    required this.assetPath,
    required this.onTap,
    this.isAdmin = false,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final opacity = isEnabled ? 1.0 : 0.5;

    return YonnoviaCard(
      onTap: isEnabled ? onTap : null,
      padding: const EdgeInsets.all(24),
      backgroundColor: isAdmin
          ? AppColors.primaryTeal.withAlpha((0.05 * 255).toInt())
          : AppColors.white,
      border: Border.all(
        color: (isAdmin
            ? AppColors.primaryTeal.withAlpha((0.3 * 255).toInt())
            : AppColors.borderLight.withAlpha((opacity * 255).toInt())),
        width: isAdmin ? 2 : 1,
      ),
      child: Opacity(
        opacity: opacity,
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isAdmin
                        ? AppColors.primaryTeal.withAlpha((0.1 * 255).toInt())
                        : AppColors.gray100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: isAdmin ? AppColors.primaryTeal : AppColors.gray600,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: isAdmin
                              ? AppColors.primaryTeal
                              : AppColors.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: const TextStyle(
                          fontSize: 14,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                if (!isEnabled)
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.warning.withAlpha((0.1 * 255).toInt()),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      'Coming Soon',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: AppColors.warning,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.asset(
                    assetPath,
                    width: 60,
                    height: 60,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          color: AppColors.gray100,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          icon,
                          color: AppColors.gray400,
                          size: 24,
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: features
                        .map((feature) => Padding(
                              padding: const EdgeInsets.only(bottom: 4),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.check_circle,
                                    color: isAdmin
                                        ? AppColors.primaryTeal
                                        : AppColors.success,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      feature,
                                      style: const TextStyle(
                                        fontSize: 13,
                                        color: AppColors.textSecondary,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ))
                        .toList(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
