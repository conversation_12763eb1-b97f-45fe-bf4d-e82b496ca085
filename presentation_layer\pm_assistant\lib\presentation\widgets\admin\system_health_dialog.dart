import 'package:flutter/material.dart';
import '../../viewmodels/admin_dashboard_viewmodel.dart';
import '../../../core/constants/app_colors.dart';
import '../common/yonnovia_button.dart';
import 'health_item.dart';

/// System health dialog widget following Single Responsibility Principle
/// Handles only system health display UI logic
class SystemHealthDialog extends StatefulWidget {
  final AdminDashboardViewModel adminViewModel;
  
  const SystemHealthDialog({super.key, required this.adminViewModel});

  static Future<void> show(BuildContext context, AdminDashboardViewModel adminViewModel) {
    return showDialog(
      context: context,
      builder: (context) => SystemHealthDialog(adminViewModel: adminViewModel),
    );
  }

  @override
  _SystemHealthDialogState createState() => _SystemHealthDialogState();
}

class _SystemHealthDialogState extends State<SystemHealthDialog> {
  Map<String, dynamic> healthData = {};
  bool isLoading = true;
  String? error;

  @override
  void initState() {
    super.initState();
    _loadHealthData();
  }

  Future<void> _loadHealthData() async {
    try {
      final result = await widget.adminViewModel.getSystemHealth();
      
      if (result['success'] == true) {
        setState(() {
          healthData = result['data'] ?? {};
          isLoading = false;
        });
      } else {
        setState(() {
          error = result['message'] ?? 'Failed to load system health';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        error = 'Error loading system health: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.health_and_safety, color: AppColors.primaryTeal),
          const SizedBox(width: 12),
          const Text('System Health'),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        height: 250, // Reduced height to prevent overflow
        child: isLoading
            ? const Center(child: CircularProgressIndicator())
            : error != null
                ? Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error_outline, color: AppColors.error, size: 48),
                      const SizedBox(height: 16),
                      Text(error!, textAlign: TextAlign.center),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          setState(() {
                            isLoading = true;
                            error = null;
                          });
                          _loadHealthData();
                        },
                        child: const Text('Retry'),
                      ),
                    ],
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      HealthItem(
                        component: 'Database',
                        status: healthData['status'] == 'healthy' ? 'Healthy' : 'Warning',
                        statusColor: healthData['status'] == 'healthy' ? AppColors.success : AppColors.warning,
                      ),
                      HealthItem(
                        component: 'API Server',
                        status: 'Healthy',
                        statusColor: AppColors.success,
                      ),
                      HealthItem(
                        component: 'Authentication',
                        status: 'Healthy',
                        statusColor: AppColors.success,
                      ),
                      HealthItem(
                        component: 'Chat System',
                        status: healthData['active_sessions'] != null ? 'Healthy' : 'Warning',
                        statusColor: healthData['active_sessions'] != null ? AppColors.success : AppColors.warning,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Last Check: ${DateTime.now().toString().substring(0, 16)}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Close'),
        ),
        YonnoviaButton(
          text: 'Refresh',
          onPressed: () {
            _loadHealthData();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('System health check initiated'),
                backgroundColor: AppColors.primaryTeal,
              ),
            );
          },
        ),
      ],
    );
  }
}
