import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../viewmodels/chat_viewmodel.dart';
import '../../core/constants/app_colors.dart';
import '../widgets/common/yonnovia_header.dart';
import '../widgets/common/yonnovia_text_field.dart';
import '../widgets/common/yonnovia_button.dart';
import '../widgets/chat/chat_empty_state.dart';
import '../widgets/chat/chat_message.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  _ChatScreenState createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Safe<PERSON><PERSON>(
        child: Column(
          children: [
            // Chat Header
            YonnoviaHeader(
              title: 'PM Assistant Chat',
              onBackPressed: () => Navigator.pop(context),
              actions: [
                IconButton(
                  icon: Icon(Icons.refresh, color: Colors.white),
                  onPressed: () {
                    context.read<ChatViewModel>().startNewConversation();
                  },
                ),
                SizedBox(width: 8),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'EN',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            
            // Chat Messages
            Expanded(
              child: Container(
                color: AppColors.background,
                child: Consumer<ChatViewModel>(
                  builder: (context, viewModel, child) {
                    if (viewModel.messages.isEmpty) {
                      return ChatEmptyState(
                        onSuggestionTap: (suggestion) {
                          _controller.text = suggestion;
                          _sendMessage();
                        },
                      );
                    }
                    
                    return ListView.builder(
                      padding: EdgeInsets.all(20),
                      itemCount: viewModel.messages.length,
                      itemBuilder: (context, index) {
                        final message = viewModel.messages[index];
                        return ChatMessage(
                          text: message.text,
                          isUser: message.isUser,
                        );
                      },
                    );
                  },
                ),
              ),
            ),
            
            // Chat Input
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.surface,
                border: Border(
                  top: BorderSide(color: AppColors.borderLight),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: YonnoviaTextField(
                      controller: _controller,
                      hintText: 'Ask me anything about project management...',
                      maxLines: 4,
                    ),
                  ),
                  SizedBox(width: 12),
                  YonnoviaButton(
                    text: 'Send',
                    icon: Icons.send,
                    onPressed: () => _sendMessage(),
                    width: 120,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _sendMessage() {
    if (_controller.text.isNotEmpty) {
      // Use streaming for instant token-by-token responses
      context.read<ChatViewModel>().sendMessageStream(_controller.text);
      _controller.clear();
    }
  }
}