# MVVM Architecture - PM Assistant Frontend

## Overview

The PM Assistant frontend implements **MVVM (Model-View-ViewModel) Architecture** combined with **SOLID principles** to create a maintainable, scalable, and testable Flutter application. This MVVM architecture ensures proper separation of concerns and follows industry best practices.

## 🏗️ MVVM Architecture

### MVVM Architecture Layers

```
┌─────────────────────────────────────────────────────────┐
│                    PRESENTATION LAYER                   │
├─────────────┬─────────────┬─────────────┬─────────────┤
│    Views    │ ViewModels  │   Models    │   Services  │
│  (UI Comp)  │(State Mgmt) │(Data Types) │(API Client) │
└─────────────┴─────────────┴─────────────┴─────────────┘
            ↕             ↕             ↕
┌─────────────────────────────────────────────────────────┐
│               BUSINESS LOGIC LAYER (API)               │
└─────────────────────────────────────────────────────────┘
```

### MVVM Benefits
- ✅ **Separation of Concerns**: Clear boundaries between UI and logic
- ✅ **Testability**: ViewModels can be unit tested independently  
- ✅ **Maintainability**: Easy to modify UI without affecting business logic
- ✅ **Reusability**: ViewModels can be shared across different views
- ✅ **State Management**: Centralized state with Provider pattern

### Components Breakdown

#### 1. **Models** (`lib/models/`)
Data structures that represent the core entities of the application.

```dart
// Example: User model
class User {
  final String id;
  final String name;
  final String email;
  final UserRole role;
  
  // Immutable data with clear structure
  User({required this.id, required this.name, ...});
}
```

**Key Models:**
- `User` - User account information and permissions with role-based access
- `Message` - Chat message structure with metadata and conversation context
- `Conversation` - Chat conversation container with analytics tracking

**Note**: Admin statistics and system health data are handled as dynamic JSON responses through the API Gateway rather than separate model classes, keeping the model layer lean and focused on core entities.

#### 2. **Views** (`lib/presentation/views/`)
UI components that display data and capture user interactions. Each view focuses on a single screen or feature.

```dart
// Views are stateful widgets that consume ViewModels
class ChatScreen extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<ChatViewModel>(
      builder: (context, viewModel, child) {
        // UI reacts to ViewModel state changes
        return ListView.builder(
          itemCount: viewModel.messages.length,
          itemBuilder: (context, index) => ChatMessage(...)
        );
      },
    );
  }
}
```

**View Responsibilities:**
- Render UI components with responsive design
- Handle user input events and validation
- Subscribe to ViewModel state changes with Provider
- Navigate between screens and manage routing
- Display admin features with role-based UI elements

#### 3. **ViewModels** (`lib/presentation/viewmodels/`)
Business logic layer that manages state and coordinates between Views and Services.

```dart
class ChatViewModel extends ChangeNotifier {
  final ApiGateway _apiGateway;
  List<Message> _messages = [];
  bool _isLoading = false;
  
  // Expose read-only state
  List<Message> get messages => List.unmodifiable(_messages);
  bool get isLoading => _isLoading;
  
  // Business logic methods with error handling
  Future<void> sendMessage(String text) async {
    _isLoading = true;
    notifyListeners(); // Update UI
    
    try {
      final result = await _apiGateway.sendMessage(
        sessionId: _currentSessionId,
        message: text,
      );
      if (result['success']) {
        _messages.add(Message.fromJson(result['data']));
      }
    } catch (e) {
      _errorMessage = 'Failed to send message: $e';
    } finally {
      _isLoading = false;
      notifyListeners(); // Update UI
    }
  }
}
```

**ViewModel Responsibilities:**
- Manage application state with Provider pattern
- Implement business logic and data validation
- Coordinate with API Gateway for backend communication
- Notify views of state changes with proper error handling
- Handle authentication and role-based feature access
- Manage admin dashboard functionality through API responses (no separate models needed)

## 🎯 SOLID Principles Implementation

### Single Responsibility Principle (SRP)
Each class has one reason to change. We've implemented this through:

#### Widget Extraction
Every UI component is extracted into its own widget file:

```dart
// ❌ Before: Multiple responsibilities in one file
class DashboardScreen extends StatelessWidget {
  Widget _buildStatCard(...) { ... }
  Widget _buildQuickAction(...) { ... }
  Widget _buildHeader(...) { ... }
}

// ✅ After: Single responsibility per file
class StatCard extends StatelessWidget { ... }       // Only handles stat display
class QuickActionCard extends StatelessWidget { ... } // Only handles quick actions
class YonnoviaHeader extends StatelessWidget { ... }  // Only handles header display
```

#### Organized Widget Structure
```
lib/presentation/widgets/
├── admin/          # Admin-specific components
├── auth/           # Authentication components  
├── chat/           # Chat-related components
├── common/         # Shared/reusable components
├── dashboard/      # Dashboard-specific components
├── history/        # Chat history components
└── profile/        # Profile-related components
```

### Open/Closed Principle (OCP)
Components are open for extension but closed for modification:

```dart
// Base button that can be extended
class YonnoviaButton extends StatelessWidget {
  final bool isSecondary;
  final bool isOutlined;
  final IconData? icon;
  
  // Extensible through parameters, not code changes
}
```

### Dependency Inversion Principle (DIP)
High-level modules depend on abstractions, not concrete implementations:

```dart
// ViewModel depends on service abstraction
class ChatViewModel extends ChangeNotifier {
  final ChatService _chatService; // Abstract interface
  
  ChatViewModel(this._chatService); // Dependency injection
}

// Concrete implementation injected at runtime
final viewModel = ChatViewModel(MockChatService());
```

## 📁 Project Structure

```
lib/
├── core/
│   ├── constants/     # App-wide constants (colors, themes)
│   ├── interfaces/    # Abstract interfaces and contracts
│   ├── theme/         # App theming configuration
│   └── utils/         # Utility functions and helpers
├── models/            # Data models and entities
├── presentation/
│   ├── viewmodels/    # Business logic layer
│   ├── views/         # Screen components
│   └── widgets/       # Reusable UI components
└── services/          # Data access and external APIs
```

## 🔄 Data Flow Architecture

### 1. User Interaction Flow
```
User Input → View → ViewModel → Service → External API
    ↓
UI Update ← View ← ViewModel ← Service ← API Response
```

### 2. State Management with Provider
```dart
// main.dart - Dependency injection setup
MultiProvider(
  providers: [
    ChangeNotifierProvider<AuthViewModel>(
      create: (_) => AuthViewModel(authService),
    ),
    ChangeNotifierProvider<ChatViewModel>(
      create: (_) => ChatViewModel(chatService),
    ),
  ],
  child: MaterialApp(...)
)

// View - State consumption
Consumer<ChatViewModel>(
  builder: (context, viewModel, child) {
    if (viewModel.isLoading) return LoadingWidget();
    return ChatWidget(messages: viewModel.messages);
  },
)
```

## 🧪 Testing Strategy

### Unit Testing ViewModels
```dart
// ViewModel testing is isolated and predictable
test('should add message when sendMessage is called', () async {
  // Arrange
  final mockService = MockChatService();
  final viewModel = ChatViewModel(mockService);
  
  // Act
  await viewModel.sendMessage('Hello');
  
  // Assert
  expect(viewModel.messages.length, 1);
  expect(viewModel.messages.first.text, 'Hello');
});
```

### Widget Testing
```dart
// Widgets can be tested in isolation
testWidgets('ChatMessage displays user message correctly', (tester) async {
  await tester.pumpWidget(
    ChatMessage(text: 'Hello', isUser: true)
  );
  
  expect(find.text('Hello'), findsOneWidget);
});
```

## 🎨 Design System

### Yonnovia Components
All UI components follow the Yonnovia design system:

```dart
YonnoviaButton(...)    // Consistent button styling
YonnoviaCard(...)      // Consistent card layouts  
YonnoviaHeader(...)    // Consistent header design
YonnoviaTextField(...) // Consistent input fields
```

### Color Scheme
```dart
class AppColors {
  static const primaryTeal = Color(0xFF4FD1C7);
  static const secondaryGreen = Color(0xFF10B981);
  static const backgroundGradient = LinearGradient(...);
  // Consistent color palette throughout the app
}
```

## 🚀 Key Benefits

### 1. **Maintainability**
- Clear separation of concerns
- Easy to locate and modify specific functionality
- Reduced coupling between components

### 2. **Scalability**
- New features can be added without affecting existing code
- Component reusability across different screens
- Modular architecture supports team development

### 3. **Testability**
- ViewModels can be unit tested independently
- Widgets can be tested in isolation
- Mock services enable predictable testing

### 4. **Code Quality**
- SOLID principles ensure clean, professional code
- Consistent naming conventions and structure
- Clear data flow and state management

## 📖 Working with the Codebase

### Adding a New Screen
1. Create the view in `lib/presentation/views/`
2. Extract widgets to appropriate folders in `lib/presentation/widgets/`
3. Create ViewModel if needed in `lib/presentation/viewmodels/`
4. Register dependencies in `main.dart`

### Adding a New Widget
1. Determine the widget's responsibility (admin, auth, chat, etc.)
2. Create the widget file in the appropriate folder
3. Follow the Single Responsibility Principle
4. Use the Yonnovia design system components

### Modifying Business Logic
1. Locate the appropriate ViewModel
2. Add/modify methods while maintaining immutability
3. Use `notifyListeners()` to update the UI
4. Handle error cases appropriately

## 🔍 Common Patterns

### State Updates
```dart
// Always notify listeners after state changes
void updateState() {
  _someState = newValue;
  notifyListeners(); // Triggers UI rebuild
}
```

### Error Handling
```dart
// Consistent error handling pattern
Future<void> performAction() async {
  try {
    _isLoading = true;
    notifyListeners();
    
    await _service.performAction();
  } catch (e) {
    _errorMessage = e.toString();
  } finally {
    _isLoading = false;
    notifyListeners();
  }
}
```

### Widget Composition
```dart
// Compose complex UIs from simple, reusable widgets
YonnoviaCard(
  child: Column(
    children: [
      YonnoviaHeader(title: 'Dashboard'),
      StatCard(value: '123', label: 'Users'),
      QuickActionCard(title: 'New Chat', onTap: () => ...),
    ],
  ),
)
```

This architecture ensures a robust, maintainable, and scalable Flutter application that follows industry best practices and provides an excellent foundation for future development.
