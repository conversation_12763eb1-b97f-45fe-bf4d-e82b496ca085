
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      Microsoft (R) Build Engine version 16.11.6+a918ceb31 pour .NET Framework
      Copyright (C) Microsoft Corporation. Tous droits réservés.
      
      La génération a démarré 07/08/2025 01:11:17.
      Projet "C:\\Users\\<USER>\\Documents\\GitHub\\Yonn-GPT-2025-01-Yonn-Team14\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj" sur le noud 1 (cibles par défaut).
      PrepareForBuild:
        Création du répertoire "Debug\\".
        Création du répertoire "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Création de "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild", car "AlwaysCreate" a été spécifié.
      ClCompile:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x86\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /Oy- /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc142.pdb" /external:W0 /Gd /TP /analyze- /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x86\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X86 /SAFESEH Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\Users\\<USER>\\Documents\\GitHub\\Yonn-GPT-2025-01-Yonn-Team14\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\Hostx64\\x86\\cl.exe
      FinalizeBuildStatus:
        Suppression du fichier "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Mise à jour de l'horodatage "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Génération du projet "C:\\Users\\<USER>\\Documents\\GitHub\\Yonn-GPT-2025-01-Yonn-Team14\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj" terminée (cibles par défaut).
      
      La génération a réussi.
          0 Avertissement(s)
          0 Erreur(s)
      
      Temps écoulé 00:00:01.14
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/Users/<USER>/Documents/GitHub/Yonn-GPT-2025-01-Yonn-Team14/build/CMakeFiles/3.31.5/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Documents/GitHub/Yonn-GPT-2025-01-Yonn-Team14/build/CMakeFiles/CMakeScratch/TryCompile-yhq82o"
      binary: "C:/Users/<USER>/Documents/GitHub/Yonn-GPT-2025-01-Yonn-Team14/build/CMakeFiles/CMakeScratch/TryCompile-yhq82o"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:X86"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/GitHub/Yonn-GPT-2025-01-Yonn-Team14/build/CMakeFiles/CMakeScratch/TryCompile-yhq82o'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_941e5.vcxproj /p:Configuration=Debug /p:Platform=win32 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine version 16.11.6+a918ceb31 pour .NET Framework
        Copyright (C) Microsoft Corporation. Tous droits réservés.
        
        La génération a démarré 07/08/2025 01:11:19.
        Projet "C:\\Users\\<USER>\\Documents\\GitHub\\Yonn-GPT-2025-01-Yonn-Team14\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yhq82o\\cmTC_941e5.vcxproj" sur le noud 1 (cibles par défaut).
        PrepareForBuild:
          Création du répertoire "cmTC_941e5.dir\\Debug\\".
          Création du répertoire "C:\\Users\\<USER>\\Documents\\GitHub\\Yonn-GPT-2025-01-Yonn-Team14\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yhq82o\\Debug\\".
          Création du répertoire "cmTC_941e5.dir\\Debug\\cmTC_941e5.tlog\\".
        InitializeBuildStatus:
          Création de "cmTC_941e5.dir\\Debug\\cmTC_941e5.tlog\\unsuccessfulbuild", car "AlwaysCreate" a été spécifié.
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x86\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /Oy- /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_941e5.dir\\Debug\\\\" /Fd"cmTC_941e5.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TP /analyze- /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          Compilateur d'optimisation Microsoft (R) C/C++ version 19.29.30159 pour x86
          CMakeCXXCompilerABI.cpp
          Copyright (C) Microsoft Corporation. Tous droits réservés.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /Oy- /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_941e5.dir\\Debug\\\\" /Fd"cmTC_941e5.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TP /analyze- /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x86\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Documents\\GitHub\\Yonn-GPT-2025-01-Yonn-Team14\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yhq82o\\Debug\\cmTC_941e5.exe" /INCREMENTAL /ILK:"cmTC_941e5.dir\\Debug\\cmTC_941e5.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Documents/GitHub/Yonn-GPT-2025-01-Yonn-Team14/build/CMakeFiles/CMakeScratch/TryCompile-yhq82o/Debug/cmTC_941e5.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Documents/GitHub/Yonn-GPT-2025-01-Yonn-Team14/build/CMakeFiles/CMakeScratch/TryCompile-yhq82o/Debug/cmTC_941e5.lib" /MACHINE:X86  /machine:X86 cmTC_941e5.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_941e5.vcxproj -> C:\\Users\\<USER>\\Documents\\GitHub\\Yonn-GPT-2025-01-Yonn-Team14\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yhq82o\\Debug\\cmTC_941e5.exe
        FinalizeBuildStatus:
          Suppression du fichier "cmTC_941e5.dir\\Debug\\cmTC_941e5.tlog\\unsuccessfulbuild".
          Mise à jour de l'horodatage "cmTC_941e5.dir\\Debug\\cmTC_941e5.tlog\\cmTC_941e5.lastbuildstate".
        Génération du projet "C:\\Users\\<USER>\\Documents\\GitHub\\Yonn-GPT-2025-01-Yonn-Team14\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yhq82o\\cmTC_941e5.vcxproj" terminée (cibles par défaut).
        
        La génération a réussi.
            0 Avertissement(s)
            0 Erreur(s)
        
        Temps écoulé 00:00:00.83
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/HostX64/x86/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/HostX64/x86/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.29.30159.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...
