{"cells": [{"cell_type": "markdown", "id": "5f5884ee", "metadata": {}, "source": ["# Fix Instructions Data Format\n", "\n", "This notebook fixes the format of instruction data by converting list-type instructions to string format for proper processing."]}, {"cell_type": "markdown", "id": "bb763af4", "metadata": {}, "source": ["## Import Required Libraries\n", "\n", "Import the necessary library for JSON processing."]}, {"cell_type": "code", "execution_count": null, "id": "409e02d7", "metadata": {}, "outputs": [], "source": ["import json"]}, {"cell_type": "markdown", "id": "ba1a4f9c", "metadata": {}, "source": ["## Set Input and Output Paths\n", "\n", "Define the paths for the input file that needs fixing and the output file for the corrected data."]}, {"cell_type": "code", "execution_count": null, "id": "66671b2b", "metadata": {}, "outputs": [], "source": ["input_path = \"data/augmented_data_en.jsonl\"\n", "output_path = \"data/augmented_data_en_fixed.jsonl\""]}, {"cell_type": "markdown", "id": "8beb6e1b", "metadata": {}, "source": ["## Process and Fix Instruction Format\n", "\n", "Read the input file line by line, check if instructions are in list format, and convert them to string format by joining the list elements."]}, {"cell_type": "code", "execution_count": null, "id": "56a69ca5", "metadata": {}, "outputs": [], "source": ["with open(input_path, \"r\", encoding=\"utf-8\") as infile, open(output_path, \"w\", encoding=\"utf-8\") as outfile:\n", "    for line in infile:\n", "        try:\n", "            sample = json.loads(line.strip())\n", "            instr = sample.get(\"instruction\", \"\")\n", "            # If instruction is a list, join to string\n", "            if isinstance(instr, list):\n", "                instr = \" \".join(instr).strip()\n", "            sample[\"instruction\"] = instr\n", "            outfile.write(json.dumps(sample, ensure_ascii=False) + \"\\n\")\n", "        except Exception as e:\n", "            print(\"Error fixing line:\", e)"]}, {"cell_type": "markdown", "id": "1b7c7241", "metadata": {}, "source": ["## Verification (Optional)\n", "\n", "You can run this cell to verify that the instructions have been properly formatted by checking a few samples from the output file."]}, {"cell_type": "code", "execution_count": null, "id": "ddad1a6a", "metadata": {}, "outputs": [], "source": ["# Optional: Verify the fixed data\n", "print(\"Checking first 3 lines of the fixed file:\")\n", "with open(output_path, \"r\", encoding=\"utf-8\") as f:\n", "    for i, line in enumerate(f):\n", "        if i >= 3:\n", "            break\n", "        sample = json.loads(line.strip())\n", "        print(f\"Line {i+1}: Instruction type: {type(sample.get('instruction', ''))}\")\n", "        print(f\"Instruction: {sample.get('instruction', '')[:100]}...\\n\")"]}, {"cell_type": "markdown", "id": "490f2ae1", "metadata": {}, "source": ["## Summary\n", "\n", "The instruction format fixing process is complete. All list-type instructions have been converted to string format and saved to the output file."]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}