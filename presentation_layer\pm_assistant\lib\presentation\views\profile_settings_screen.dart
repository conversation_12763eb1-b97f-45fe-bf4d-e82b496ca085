import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../widgets/common/yonnovia_header.dart';
import '../widgets/common/yonnovia_card.dart';
import '../widgets/common/yonnovia_text_field.dart';
import '../widgets/common/yonnovia_button.dart';
import '../widgets/profile/language_selector.dart';
import '../widgets/profile/theme_selector.dart';
import '../widgets/profile/notification_settings.dart';

class ProfileSettingsScreen extends StatefulWidget {
  const ProfileSettingsScreen({super.key});

  @override
  _ProfileSettingsScreenState createState() => _ProfileSettingsScreenState();
}

class _ProfileSettingsScreenState extends State<ProfileSettingsScreen> {
  final TextEditingController _nameController = TextEditingController(text: '<PERSON>');
  String _selectedLanguage = 'English';
  String _selectedTheme = 'Light';
  bool _emailNotifications = true;
  bool _pushNotifications = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            YonnoviaHeader(
              title: 'Profile Settings',
              onBackPressed: () => Navigator.pop(context),
              actions: [
                IconButton(
                  icon: Icon(Icons.save, color: Colors.white),
                  onPressed: _saveSettings,
                ),
              ],
            ),
            
            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(20),
                child: Column(
                  children: [
                    // Profile Image Section
                    YonnoviaCard(
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Icon(Icons.person, color: AppColors.primaryTeal, size: 24),
                              SizedBox(width: 12),
                              Text(
                                'Profile Information',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 20),
                          // Profile Image
                          Container(
                            height: 120,
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(12),
                              child: Image.asset(
                                'assets/images/salute.png',
                                width: 200,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Container(
                                    width: 200,
                                    height: 120,
                                    decoration: BoxDecoration(
                                      color: AppColors.primaryTeal.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Icon(
                                      Icons.person,
                                      size: 60,
                                      color: AppColors.primaryTeal,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                          SizedBox(height: 20),
                          // Display Name
                          YonnoviaTextField(
                            controller: _nameController,
                            hintText: 'Enter your display name',
                          ),
                        ],
                      ),
                    ),
                    
                    SizedBox(height: 20),
                    
                    // Preferences Section
                    YonnoviaCard(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.settings, color: AppColors.primaryTeal, size: 24),
                              SizedBox(width: 12),
                              Text(
                                'Preferences',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                              Spacer(),
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: AppColors.primaryTeal.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: AppColors.primaryTeal.withOpacity(0.3),
                                  ),
                                ),
                                child: Text(
                                  'Coming Soon',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.primaryTeal,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 20),
                          Opacity(
                            opacity: 0.6,
                            child: LanguageSelector(
                              selectedLanguage: _selectedLanguage,
                              onLanguageChanged: (value) {
                                setState(() {
                                  _selectedLanguage = value;
                                });
                              },
                            ),
                          ),
                          SizedBox(height: 20),
                          Opacity(
                            opacity: 0.6,
                            child: ThemeSelector(
                              selectedTheme: _selectedTheme,
                              onThemeChanged: (value) {
                                setState(() {
                                  _selectedTheme = value;
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    SizedBox(height: 20),
                    
                    // Notifications Section
                    YonnoviaCard(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.notifications, color: AppColors.primaryTeal, size: 24),
                              SizedBox(width: 12),
                              Text(
                                'Notifications',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                              Spacer(),
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: AppColors.primaryTeal.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: AppColors.primaryTeal.withOpacity(0.3),
                                  ),
                                ),
                                child: Text(
                                  'Coming Soon',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.primaryTeal,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 20),
                          Opacity(
                            opacity: 0.6,
                            child: NotificationSettings(
                              emailNotifications: _emailNotifications,
                              pushNotifications: _pushNotifications,
                              onEmailChanged: (value) {
                                setState(() {
                                  _emailNotifications = value!;
                                });
                              },
                              onPushChanged: (value) {
                                setState(() {
                                  _pushNotifications = value!;
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    SizedBox(height: 20),
                    
                    // Action Buttons
                    Row(
                      children: [
                        Expanded(
                          child: YonnoviaButton(
                            text: 'Save Changes',
                            onPressed: _saveSettings,
                            icon: Icons.save,
                          ),
                        ),
                        SizedBox(width: 12),
                        Expanded(
                          child: YonnoviaButton(
                            text: 'Reset',
                            onPressed: _resetSettings,
                            isOutlined: true,
                            icon: Icons.refresh,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _saveSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Settings saved successfully!'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _resetSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Reset Settings'),
        content: Text('Are you sure you want to reset all settings to default values?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _nameController.text = 'John Smith';
                _selectedLanguage = 'English';
                _selectedTheme = 'Light';
                _emailNotifications = true;
                _pushNotifications = false;
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Settings reset to defaults'),
                  backgroundColor: AppColors.info,
                ),
              );
            },
            child: Text('Reset', style: TextStyle(color: AppColors.error)),
          ),
        ],
      ),
    );
  }
}
