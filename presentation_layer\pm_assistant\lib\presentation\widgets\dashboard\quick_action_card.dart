import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';
import '../common/yonnovia_card.dart';

/// Quick action card widget following Single Responsibility Principle
/// Handles only quick action display UI logic
class QuickActionCard extends StatelessWidget {
  final String title;
  final String description;
  final String assetPath;
  final Color color;
  final VoidCallback onTap;

  const QuickActionCard({
    super.key,
    required this.title,
    required this.description,
    required this.assetPath,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return YonnoviaCard(
      onTap: onTap,
      padding: const EdgeInsets.all(16),
      backgroundColor: color.withOpacity(0.05),
      border: Border.all(
        color: color.withOpacity(0.2),
        width: 1,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Image.asset(
              assetPath,
              width: 32,
              height: 32,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
