"""
User Models for Business Logic Layer
===================================
Defines all user-related data models and DTOs
"""

from pydantic import BaseModel, validator
from typing import Optional
from datetime import datetime


class UserCreate(BaseModel):
    """Model for user registration request"""
    email: str  # Changed from EmailStr to avoid dependency
    password: str
    full_name: str  # Changed from 'name' to match auth service
    role: Optional[str] = "user"
    
    @validator('password')
    def password_strength(cls, v):
        if len(v) < 6:
            raise ValueError('Password must be at least 6 characters long')
        return v
    
    @validator('role')
    def validate_role(cls, v):
        allowed_roles = ["user", "admin", "manager"]
        if v and v not in allowed_roles:
            raise ValueError(f'Role must be one of {allowed_roles}')
        return v


class UserLogin(BaseModel):
    """Model for user login request"""
    email: str  # Changed from EmailStr
    password: str


class User(BaseModel):
    """Model for user data response"""
    id: str
    email: str
    full_name: str  # Changed to match service expectations
    role: str
    created_at: datetime
    last_login_at: Optional[datetime] = None
    is_active: bool = True


class TokenResponse(BaseModel):
    """Model for authentication token response"""
    access_token: str
    token_type: str = "bearer"
    user: User


class UserSettings(BaseModel):
    """Model for user settings"""
    notifications_enabled: bool = True
    theme: str = "light"
    language: str = "en"
    timezone: str = "UTC"
