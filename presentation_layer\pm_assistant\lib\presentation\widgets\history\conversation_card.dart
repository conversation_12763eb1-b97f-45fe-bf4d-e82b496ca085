import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';
import '../../../models/conversation.dart';
import '../common/yonnovia_card.dart';
import '../common/yonnovia_button.dart';

/// Conversation card widget following Single Responsibility Principle
/// Handles only conversation card display UI logic
class ConversationCard extends StatelessWidget {
  final Conversation conversation;
  final VoidCallback onTap;
  final VoidCallback? onDelete;
  final VoidCallback? onArchive;
  final VoidCallback? onExport;

  const ConversationCard({
    super.key,
    required this.conversation,
    required this.onTap,
    this.onDelete,
    this.onArchive,
    this.onExport,
  });

  @override
  Widget build(BuildContext context) {
    // Use messageCount property if available, otherwise fall back to messages.length
    final messageCount = conversation.messageCount ?? conversation.messages.length;
    final lastMessageTime = conversation.updatedAt;
    final timeDifference = DateTime.now().difference(lastMessageTime);
    
    // Get last message text - try backend lastMessage first, then fall back to messages array
    String? lastMessageText = conversation.lastMessage;
    if (lastMessageText == null || lastMessageText.isEmpty) {
      if (conversation.messages.isNotEmpty) {
        lastMessageText = conversation.messages.last.text;
      }
    }
    
    String timeString;
    if (timeDifference.inDays > 0) {
      timeString = '${timeDifference.inDays}d ago';
    } else if (timeDifference.inHours > 0) {
      timeString = '${timeDifference.inHours}h ago';
    } else if (timeDifference.inMinutes > 0) {
      timeString = '${timeDifference.inMinutes}m ago';
    } else {
      timeString = 'Just now';
    }

    return Container(
      margin: EdgeInsets.only(bottom: 12),
      child: YonnoviaCard(
        child: Column(
          children: [
            // Main content row
            Row(
              children: [
                // Conversation icon
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.primaryTeal.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.chat_bubble_outline,
                    color: AppColors.primaryTeal,
                    size: 20,
                  ),
                ),
                SizedBox(width: 12),
                
                // Title and details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        conversation.title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 4),
                      Text(
                        '$timeString • $messageCount messages',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      if (lastMessageText != null && lastMessageText.isNotEmpty) ...[
                        SizedBox(height: 4),
                        Text(
                          lastMessageText,
                          style: TextStyle(
                            fontSize: 14,
                            color: AppColors.textSecondary,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
                
                // Main action button
                YonnoviaButton(
                  text: 'Open',
                  onPressed: onTap,
                  isSecondary: true,
                  width: 80,
                ),
              ],
            ),
            
            // Action buttons row
            if (onDelete != null || onArchive != null || onExport != null) ...[
              SizedBox(height: 12),
              Divider(color: AppColors.gray300, height: 1),
              SizedBox(height: 12),
              Row(
                children: [
                  if (onExport != null) ...[
                    Expanded(
                      child: TextButton.icon(
                        onPressed: onExport,
                        icon: Icon(Icons.download, size: 16, color: AppColors.primaryTeal),
                        label: Text(
                          'Export',
                          style: TextStyle(color: AppColors.primaryTeal, fontSize: 12),
                        ),
                      ),
                    ),
                  ],
                  if (onArchive != null) ...[
                    Expanded(
                      child: TextButton.icon(
                        onPressed: onArchive,
                        icon: Icon(Icons.archive, size: 16, color: AppColors.gray600),
                        label: Text(
                          'Archive',
                          style: TextStyle(color: AppColors.gray600, fontSize: 12),
                        ),
                      ),
                    ),
                  ],
                  if (onDelete != null) ...[
                    Expanded(
                      child: TextButton.icon(
                        onPressed: onDelete,
                        icon: Icon(Icons.delete, size: 16, color: AppColors.error),
                        label: Text(
                          'Delete',
                          style: TextStyle(color: AppColors.error, fontSize: 12),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}
