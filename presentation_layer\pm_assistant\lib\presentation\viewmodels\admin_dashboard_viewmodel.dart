import 'package:flutter/foundation.dart';
import '../../core/api_gateway/api_gateway.dart';

class AdminDashboardViewModel extends ChangeNotifier {
  final ApiGateway _apiGateway;
  
  // Constructor now requires ApiGateway instance
  AdminDashboardViewModel(this._apiGateway);
  
  // State variables
  bool _isLoading = false;
  String? _error;
  
  // Dashboard stats
  int _totalUsers = 0;
  int _totalConversations = 0;
  double _uptimePercentage = 0.0;
  int _activeUsers = 0;
  int _documentsCount = 0;
  
  // System health
  String _systemStatus = 'Unknown';
  String _memoryUsage = '0%';
  String _cpuUsage = '0%';
  bool _ragInitialized = false;
  bool _modelLoaded = false;
  
  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  int get totalUsers => _totalUsers;
  int get totalConversations => _totalConversations;
  double get uptimePercentage => _uptimePercentage;
  int get activeUsers => _activeUsers;
  int get documentsCount => _documentsCount;
  String get systemStatus => _systemStatus;
  String get memoryUsage => _memoryUsage;
  String get cpuUsage => _cpuUsage;
  bool get ragInitialized => _ragInitialized;
  bool get modelLoaded => _modelLoaded;
  
  /// Load admin dashboard data
  Future<void> loadDashboardData() async {
    _setLoading(true);
    _setError(null);
    
    try {
      // Load stats and system health in parallel
      await Future.wait([
        _loadAdminStats(),
        _loadSystemHealth(),
      ]);
    } catch (e) {
      _setError('Failed to load dashboard data: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }
  
  /// Load admin statistics
  Future<void> _loadAdminStats() async {
    try {
      final response = await _apiGateway.getAdminStats();
      
      if (response['success'] == true) {
        final stats = response['stats'];
        _totalUsers = stats['total_users'] ?? 0;
        _totalConversations = stats['total_conversations'] ?? 0;
        _uptimePercentage = (stats['uptime_percentage'] ?? 0.0).toDouble();
        _activeUsers = stats['active_sessions'] ?? 0;
        _documentsCount = stats['documents_count'] ?? 0;
        notifyListeners();
      } else {
        throw Exception(response['error'] ?? 'Failed to load admin stats');
      }
    } catch (e) {
      throw Exception('Error loading admin stats: ${e.toString()}');
    }
  }
  
  /// Load system health data
  Future<void> _loadSystemHealth() async {
    try {
      final response = await _apiGateway.getSystemHealth();
      
      if (response['success'] == true) {
        final health = response['health'];
        _systemStatus = health['status'] ?? 'Unknown';
        _memoryUsage = health['memory_usage'] ?? '0%';
        _cpuUsage = health['cpu_usage'] ?? '0%';
        _ragInitialized = health['rag_initialized'] ?? false;
        _modelLoaded = health['model_loaded'] ?? false;
        notifyListeners();
      } else {
        throw Exception(response['error'] ?? 'Failed to load system health');
      }
    } catch (e) {
      throw Exception('Error loading system health: ${e.toString()}');
    }
  }
  
  /// Get all users
  Future<Map<String, dynamic>> getAllUsers() async {
    try {
      return await _apiGateway.getAllUsers();
    } catch (e) {
      throw Exception('Error getting users: ${e.toString()}');
    }
  }
  
  /// Get analytics data
  Future<Map<String, dynamic>> getAnalytics() async {
    try {
      return await _apiGateway.getAnalytics();
    } catch (e) {
      throw Exception('Error getting analytics: ${e.toString()}');
    }
  }
  
  /// Get system health data
  Future<Map<String, dynamic>> getSystemHealth() async {
    try {
      return await _apiGateway.getSystemHealth();
    } catch (e) {
      throw Exception('Error getting system health: ${e.toString()}');
    }
  }
  
  /// Refresh dashboard data
  Future<void> refresh() async {
    await loadDashboardData();
  }
  
  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }
  
  /// Clear error
  void clearError() {
    _setError(null);
  }
}
