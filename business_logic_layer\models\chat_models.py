"""
Chat Models for Business Logic Layer
===================================
Defines all chat and session-related data models and DTOs
"""

from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime


class SessionCreate(BaseModel):
    """Model for creating a new chat session"""
    title: Optional[str] = None


class SessionResponse(BaseModel):
    """Model for session response"""
    id: str
    title: str
    created_at: datetime
    last_activity: datetime
    user_id: str


class MessageRequest(BaseModel):
    """Model for incoming chat message"""
    message: str
    session_id: Optional[str] = None


class MessageResponse(BaseModel):
    """Model for chat message response"""
    response: str
    session_id: str
    timestamp: datetime
    message_id: str


class ConversationSummary(BaseModel):
    """Model for conversation summary"""
    id: str
    title: str
    last_message: str
    last_activity: datetime
    message_count: int
    user_id: str


class AnalyticsData(BaseModel):
    """Model for analytics data"""
    total_users: int
    total_sessions: int
    total_messages: int
    active_users_today: int
    average_session_length: float
    most_active_hours: List[int]
