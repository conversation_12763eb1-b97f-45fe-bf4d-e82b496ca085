# Development Environment Configuration
# For local development with Docker

# Application Settings
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=true
LOG_LEVEL=DEBUG
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1
DEVELOPMENT=1

# Model Configuration (adjust paths for your local setup)
MODEL_PATH=/app/business_logic_layer/models/qwen2.5-7b-pm-assistant.q4_k_m.gguf
DOCS_FOLDER=/app/business_logic_layer/Docs

# RAG System Settings
VECTOR_STORE_PATH=pm_rag_store.pkl.gz
EMBEDDING_DIM=384
MAX_DOC_CHUNK_SIZE=800
CHUNK_OVERLAP=150
RELEVANCE_THRESHOLD=0.5
MIN_PM_KEYWORDS=2

# LLM Settings (reduced for development)
LLM_MAX_TOKENS=500
LLM_TEMPERATURE=0.7
LLM_N_CTX=1024
LLM_N_THREADS=4
LLM_N_GPU_LAYERS=0

# Conversation Cache Settings
MAX_SESSIONS=100
MAX_MESSAGES_PER_SESSION=20
SESSION_TIMEOUT_HOURS=2

# CORS Settings (permissive for development)
CORS_ORIGINS=*
CORS_METHODS=*
CORS_HEADERS=*

# Database Settings
POSTGRES_DB=pm_assistant_dev
POSTGRES_USER=pm_user
POSTGRES_PASSWORD=dev_password
POSTGRES_HOST=postgres-dev
POSTGRES_PORT=5432

# Redis Settings
REDIS_HOST=redis-dev
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Security Settings (use weak keys for development)
SECRET_KEY=dev_secret_key_not_for_production
JWT_SECRET_KEY=dev_jwt_secret_not_for_production
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# File Upload Settings
MAX_FILE_SIZE=10MB
ALLOWED_FILE_TYPES=pdf,docx,txt,md

# Development Tools
ENABLE_DEBUG=true
ENABLE_PROFILING=true
ENABLE_METRICS=true
METRICS_PORT=9090
