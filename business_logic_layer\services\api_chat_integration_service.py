#!/usr/bin/env python3
"""
Chat Integration Service - Business Logic Layer
==============================================
This service handles chat functionality and conversation history management.
It integrates with the FastAPI server endpoints and manages business logic.
"""

import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from uuid import uuid4

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class Message:
    """Message model for chat system"""
    id: str
    text: str
    is_user: bool
    timestamp: datetime
    session_id: str
    user_id: str
    
    def to_dict(self):
        return {
            'id': self.id,
            'text': self.text,
            'is_user': self.is_user,
            'timestamp': self.timestamp.isoformat(),
            'session_id': self.session_id,
            'user_id': self.user_id
        }

@dataclass
class Conversation:
    """Conversation model for chat history"""
    id: str
    title: str
    messages: List[Message]
    created_at: datetime
    updated_at: datetime
    user_id: str
    is_archived: bool = False
    
    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'messages': [msg.to_dict() for msg in self.messages],
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'user_id': self.user_id,
            'is_archived': self.is_archived,
            'message_count': len(self.messages)
        }

class ChatIntegrationService:
    """
    Chat Integration Service - Business Logic Layer
    Handles conversation history and chat business logic
    """
    
    def __init__(self):
        # In-memory storage for conversations (will be replaced with database later)
        self.conversations_db: Dict[str, Conversation] = {}
        self.user_conversations: Dict[str, List[str]] = {}  # user_id -> conversation_ids
        
        # Initialize with some test data
        self._initialize_test_conversations()
    
    def _initialize_test_conversations(self):
        """Initialize test conversations for demo purposes"""
        try:
            # Test conversation for admin user
            admin_user_id = "f90c6d5f-d84d-4436-b2f0-754382fe0bb1"  # admin user ID
            
            # Conversation 1
            conv1_id = "conv_1_admin"
            conv1_messages = [
                Message(
                    id="msg_1",
                    text="How do I create an effective project timeline?",
                    is_user=True,
                    timestamp=datetime.now() - timedelta(hours=2),
                    session_id="session_1",
                    user_id=admin_user_id
                ),
                Message(
                    id="msg_2",
                    text="To create an effective project timeline, start by breaking down your project into smaller tasks. Use tools like Gantt charts to visualize dependencies and critical path. Always include buffer time for unexpected delays and regularly review progress.",
                    is_user=False,
                    timestamp=datetime.now() - timedelta(hours=2, minutes=-1),
                    session_id="session_1",
                    user_id=admin_user_id
                ),
                Message(
                    id="msg_3",
                    text="What about risk management in the timeline?",
                    is_user=True,
                    timestamp=datetime.now() - timedelta(hours=1),
                    session_id="session_1",
                    user_id=admin_user_id
                ),
                Message(
                    id="msg_4",
                    text="Great question! Include risk assessment in your timeline by identifying potential risks early, assigning probability and impact scores, and building mitigation strategies into your schedule. Reserve 10-20% of your timeline for risk contingencies.",
                    is_user=False,
                    timestamp=datetime.now() - timedelta(hours=1, minutes=-1),
                    session_id="session_1",
                    user_id=admin_user_id
                )
            ]
            
            conv1 = Conversation(
                id=conv1_id,
                title="Project Timeline Planning",
                messages=conv1_messages,
                created_at=datetime.now() - timedelta(hours=2),
                updated_at=datetime.now() - timedelta(hours=1),
                user_id=admin_user_id
            )
            
            # Conversation 2
            conv2_id = "conv_2_admin"
            conv2_messages = [
                Message(
                    id="msg_5",
                    text="Best practices for team management?",
                    is_user=True,
                    timestamp=datetime.now() - timedelta(days=1),
                    session_id="session_2",
                    user_id=admin_user_id
                ),
                Message(
                    id="msg_6",
                    text="Effective team management includes: 1) Clear communication channels, 2) Regular one-on-one meetings, 3) Setting clear expectations and goals, 4) Providing constructive feedback, 5) Recognizing achievements, and 6) Facilitating professional development opportunities.",
                    is_user=False,
                    timestamp=datetime.now() - timedelta(days=1, minutes=-2),
                    session_id="session_2",
                    user_id=admin_user_id
                )
            ]
            
            conv2 = Conversation(
                id=conv2_id,
                title="Team Management Best Practices",
                messages=conv2_messages,
                created_at=datetime.now() - timedelta(days=1),
                updated_at=datetime.now() - timedelta(days=1, minutes=-2),
                user_id=admin_user_id
            )
            
            # Store conversations
            self.conversations_db[conv1_id] = conv1
            self.conversations_db[conv2_id] = conv2
            
            # Map to user
            self.user_conversations[admin_user_id] = [conv1_id, conv2_id]
            
            # Test conversation for regular user
            regular_user_id = "u90c6d5f-d84d-4436-b2f0-754382fe0bb2"  # regular user ID
            
            conv3_id = "conv_3_user"
            conv3_messages = [
                Message(
                    id="msg_7",
                    text="How do I track project budget effectively?",
                    is_user=True,
                    timestamp=datetime.now() - timedelta(hours=3),
                    session_id="session_3",
                    user_id=regular_user_id
                ),
                Message(
                    id="msg_8",
                    text="For effective budget tracking: 1) Set up a detailed budget baseline, 2) Track actual vs planned expenses weekly, 3) Use earned value management techniques, 4) Monitor cost variance and schedule variance, 5) Implement change control processes for budget modifications.",
                    is_user=False,
                    timestamp=datetime.now() - timedelta(hours=3, minutes=-1),
                    session_id="session_3",
                    user_id=regular_user_id
                )
            ]
            
            conv3 = Conversation(
                id=conv3_id,
                title="Budget Tracking Methods",
                messages=conv3_messages,
                created_at=datetime.now() - timedelta(hours=3),
                updated_at=datetime.now() - timedelta(hours=3, minutes=-1),
                user_id=regular_user_id
            )
            
            self.conversations_db[conv3_id] = conv3
            self.user_conversations[regular_user_id] = [conv3_id]
            
            logger.info(f"Initialized {len(self.conversations_db)} test conversations")
            
        except Exception as e:
            logger.error(f"Error initializing test conversations: {e}")
    
    def get_user_conversations(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all conversations for a user"""
        try:
            conversation_ids = self.user_conversations.get(user_id, [])
            conversations = []
            
            for conv_id in conversation_ids:
                if conv_id in self.conversations_db:
                    conv = self.conversations_db[conv_id]
                    conversations.append({
                        'id': conv.id,
                        'title': conv.title,
                        'created_at': conv.created_at.isoformat(),
                        'updated_at': conv.updated_at.isoformat(),
                        'message_count': len(conv.messages),
                        'last_message': conv.messages[-1].text if conv.messages else None,
                        'is_archived': conv.is_archived
                    })
            
            # Sort by updated_at descending (most recent first)
            conversations.sort(key=lambda x: x['updated_at'], reverse=True)
            
            logger.info(f"Retrieved {len(conversations)} conversations for user {user_id}")
            return conversations
            
        except Exception as e:
            logger.error(f"Error getting user conversations: {e}")
            return []
    
    def get_conversation_detail(self, conversation_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed conversation with all messages"""
        try:
            if conversation_id not in self.conversations_db:
                logger.warning(f"Conversation {conversation_id} not found")
                return None
            
            conversation = self.conversations_db[conversation_id]
            
            # Check if user has access to this conversation
            if conversation.user_id != user_id:
                logger.warning(f"User {user_id} does not have access to conversation {conversation_id}")
                return None
            
            result = conversation.to_dict()
            logger.info(f"Retrieved conversation detail for {conversation_id}")
            return result
            
        except Exception as e:
            logger.error(f"Error getting conversation detail: {e}")
            return None
    
    def create_conversation_from_session(self, session_id: str, user_id: str, messages: List[Dict[str, Any]]) -> str:
        """Create a new conversation from a chat session"""
        try:
            conversation_id = f"conv_{uuid4().hex[:8]}"
            
            # Convert messages to Message objects
            conv_messages = []
            for msg_data in messages:
                message = Message(
                    id=msg_data.get('id', f"msg_{uuid4().hex[:8]}"),
                    text=msg_data['text'],
                    is_user=msg_data['is_user'],
                    timestamp=datetime.fromisoformat(msg_data['timestamp']) if isinstance(msg_data['timestamp'], str) else msg_data['timestamp'],
                    session_id=session_id,
                    user_id=user_id
                )
                conv_messages.append(message)
            
            # Generate title from first user message
            title = "New Conversation"
            for msg in conv_messages:
                if msg.is_user:
                    title = msg.text[:50] + "..." if len(msg.text) > 50 else msg.text
                    break
            
            # Create conversation
            conversation = Conversation(
                id=conversation_id,
                title=title,
                messages=conv_messages,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                user_id=user_id
            )
            
            # Store conversation
            self.conversations_db[conversation_id] = conversation
            
            # Add to user's conversations
            if user_id not in self.user_conversations:
                self.user_conversations[user_id] = []
            self.user_conversations[user_id].append(conversation_id)
            
            logger.info(f"Created conversation {conversation_id} for user {user_id}")
            return conversation_id
            
        except Exception as e:
            logger.error(f"Error creating conversation: {e}")
            raise e
    
    def update_conversation_title(self, conversation_id: str, user_id: str, new_title: str) -> bool:
        """Update conversation title"""
        try:
            if conversation_id not in self.conversations_db:
                return False
            
            conversation = self.conversations_db[conversation_id]
            
            # Check user access
            if conversation.user_id != user_id:
                return False
            
            conversation.title = new_title
            conversation.updated_at = datetime.now()
            
            logger.info(f"Updated conversation {conversation_id} title to: {new_title}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating conversation title: {e}")
            return False
    
    def delete_conversation(self, conversation_id: str, user_id: str) -> bool:
        """Delete a conversation"""
        try:
            if conversation_id not in self.conversations_db:
                return False
            
            conversation = self.conversations_db[conversation_id]
            
            # Check user access
            if conversation.user_id != user_id:
                return False
            
            # Remove from storage
            del self.conversations_db[conversation_id]
            
            # Remove from user's conversation list
            if user_id in self.user_conversations:
                self.user_conversations[user_id] = [
                    cid for cid in self.user_conversations[user_id] 
                    if cid != conversation_id
                ]
            
            logger.info(f"Deleted conversation {conversation_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting conversation: {e}")
            return False
    
    def archive_conversation(self, conversation_id: str, user_id: str) -> bool:
        """Archive a conversation"""
        try:
            if conversation_id not in self.conversations_db:
                return False
            
            conversation = self.conversations_db[conversation_id]
            
            # Check user access
            if conversation.user_id != user_id:
                return False
            
            conversation.is_archived = True
            conversation.updated_at = datetime.now()
            
            logger.info(f"Archived conversation {conversation_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error archiving conversation: {e}")
            return False
    
    def get_conversation_stats(self, user_id: str) -> Dict[str, Any]:
        """Get conversation statistics for a user"""
        try:
            user_conv_ids = self.user_conversations.get(user_id, [])
            total_conversations = len(user_conv_ids)
            total_messages = 0
            user_messages = 0
            assistant_messages = 0
            archived_count = 0
            
            for conv_id in user_conv_ids:
                if conv_id in self.conversations_db:
                    conv = self.conversations_db[conv_id]
                    total_messages += len(conv.messages)
                    
                    if conv.is_archived:
                        archived_count += 1
                    
                    for msg in conv.messages:
                        if msg.is_user:
                            user_messages += 1
                        else:
                            assistant_messages += 1
            
            return {
                'total_conversations': total_conversations,
                'total_messages': total_messages,
                'user_messages': user_messages,
                'assistant_messages': assistant_messages,
                'archived_conversations': archived_count,
                'active_conversations': total_conversations - archived_count
            }
            
        except Exception as e:
            logger.error(f"Error getting conversation stats: {e}")
            return {
                'total_conversations': 0,
                'total_messages': 0,
                'user_messages': 0,
                'assistant_messages': 0,
                'archived_conversations': 0,
                'active_conversations': 0
            }

# Global service instance
chat_integration_service = ChatIntegrationService()
