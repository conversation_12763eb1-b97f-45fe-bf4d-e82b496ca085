import requests
import json
import time
from typing import Optional

def check_health(base_url: str) -> bool:
    print("* Checking API health...")
    try:
        response = requests.get(f"{base_url}/health")
        response.raise_for_status()
        health = response.json()
        print("* Health Check:")
        for key, value in health.items():
            print(f"   {key.replace('_', ' ').title()}: {value}")
        return health["status"] == "healthy"
    except requests.RequestException as e:
        print(f"- Error checking health: {e}")
        return False

def create_session(base_url: str, user_id: Optional[str] = None, preferred_language: str = "en") -> Optional[str]:
    print("* Creating new session...")
    try:
        payload = {
            "user_id": user_id,
            "preferred_language": preferred_language
        }
        response = requests.post(f"{base_url}/sessions", json=payload)
        response.raise_for_status()
        session_data = response.json()
        session_id = session_data.get("session_id")
        print(f"* Session created: {session_id[:8]}...")
        return session_id
    except requests.RequestException as e:
        print(f"- Error creating session: {e}")
        return None

def chat(base_url: str, session_id: str, message: str) -> None:
    print(f"* User: {message}")
    try:
        payload = {
            "message": message,
            "session_id": session_id
        }
        response = requests.post(f"{base_url}/chat", json=payload)
        response.raise_for_status()
        result = response.json()
        print(f"* Assistant (Session: {session_id[:8]}, Language: {result['language']}):")
        print("-" * 50)
        print(result["response"])
        print("-" * 50)
        print(f"* Sources used: {result['sources_count']}")
    except requests.RequestException as e:
        print(f"- Error processing chat request: {e}")

def stream_chat(base_url: str, session_id: str, message: str) -> None:
    print(f"* User: {message}")
    try:
        payload = {
            "message": message,
            "session_id": session_id
        }
        with requests.post(f"{base_url}/chat/stream", json=payload, stream=True) as response:
            response.raise_for_status()
            print(f"* Assistant (Session: {session_id[:8]}):")
            print("-" * 50)
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    if decoded_line.startswith("data: "):
                        data = json.loads(decoded_line[6:])
                        if "chunk" in data:
                            print(data["chunk"], end="", flush=True)
                        elif "status" in data:
                            print(f"* {data['message']}")
                        elif "completed" in data:
                            print(f"\n* Response completed | Sources: {data['sources_count']}")
                        elif "error" in data:
                            print(f"- {data['error']}")
            print("-" * 50)
    except requests.RequestException as e:
        print(f"- Error streaming response: {e}")

def main():
    print("* Professional Project Management Chatbot Client")
    base_url = "http://localhost:8000"
    
    if not check_health(base_url):
        print("- Server is not healthy. Exiting...")
        return
    
    session_id = create_session(base_url, user_id="test_user", preferred_language="en")
    if not session_id:
        print("- Failed to create session. Exiting...")
        return
    
    print("\n* Type 'quit', 'exit', or 'bye' to end the session.")
    print("* Ask any project management question to begin!")
    
    while True:
        user_input = input("\nYou: ").strip()
        if user_input.lower() in ['quit', 'exit', 'bye']:
            print("\n* Thank you for using the Project Management Chatbot. Session ended.")
            break
        if not user_input:
            print("- Please enter a valid question.")
            continue
        print("- Analyzing...")
        stream_chat(base_url, session_id, user_input)
        time.sleep(1)  # Brief pause to avoid overwhelming the server

if __name__ == "__main__":
    main()