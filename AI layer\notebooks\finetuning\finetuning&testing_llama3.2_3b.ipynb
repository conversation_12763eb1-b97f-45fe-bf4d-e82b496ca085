{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7a4209e4-14ff-48bf-a1b0-95ea670f3606", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧹 Emergency disk space solution...\n", "📁 Using temp directory: /data/teams/heavy/team_14_gpt/tmp\n", "✅ Environment configured to avoid compilation\n", "✓ Cleaned /data/teams/heavy/team_14_gpt/.cache\n", "✓ Cleaned /data/teams/heavy/team_14_gpt/.triton\n", "✓ Cleaned /data/teams/heavy/team_14_gpt/tmp\n", "✓ Cleaned /tmp\n", "✓ Conda cache cleared\n", "✓ Pip cache cleared\n", "🎯 READY: Environment optimized to avoid compilation disk space issues\n", "📝 NOTE: Triton compilation disabled, using pre-compiled binaries only\n"]}], "source": ["# � EMERGENCY DISK SPACE SOLUTION: Prevent Triton compilation\n", "import os\n", "import subprocess\n", "import shutil\n", "import tempfile\n", "import sys\n", "\n", "print(\"🧹 Emergency disk space solution...\")\n", "\n", "# Critical: Remove ALL problematic Triton environment variables first\n", "problematic_vars = [\n", "    'TRITO<PERSON>_CACHE_MANAGER', 'TRITON_CACHE_DIR', 'TRITON_CACHE_PATH',\n", "    'TRITON_CACHE_MANAGER_HOST', 'TRITON_CACHE_MANAGER_PORT'\n", "]\n", "for var in problematic_vars:\n", "    if var in os.environ:\n", "        del os.environ[var]\n", "        print(f\"✓ Removed problematic env var: {var}\")\n", "\n", "# 1. Set up user temp directory (avoid /tmp completely)\n", "user_temp = os.path.expanduser(\"~/tmp\")\n", "os.makedirs(user_temp, exist_ok=True)\n", "os.environ['TMPDIR'] = user_temp\n", "os.environ['TMP'] = user_temp\n", "os.environ['TEMP'] = user_temp\n", "os.environ['TEMPDIR'] = user_temp\n", "tempfile.tempdir = user_temp\n", "print(f\"📁 Using temp directory: {user_temp}\")\n", "\n", "# 2. CRITICAL: Disable Triton compilation to prevent disk space issues\n", "os.environ['TRITON_DISABLE_EAGER_COMPILATION'] = '1'\n", "# Let Triton use default cache location to avoid parsing issues\n", "os.environ['CUDA_CACHE_DISABLE'] = '1'\n", "os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'\n", "\n", "# 3. Disable bitsandbytes compilation\n", "os.environ['BITSANDBYTES_NOWELCOME'] = '1'\n", "os.environ['BITSANDBYTES_QUIET'] = '1'\n", "\n", "# 4. Force minimal compilation\n", "os.environ['TORCH_COMPILE_DISABLE'] = '1'\n", "\n", "# 4.1. Additional Triton cache control\n", "os.environ['TRITON_DISABLE_LINE_INFO'] = '1'\n", "os.environ['TRITON_INTERPRET'] = '1'\n", "# Completely disable Triton cache manager to avoid parsing issues\n", "for triton_var in ['TRITON_CACHE_MANAGER', 'TRITON_CACHE_DIR']:\n", "    if triton_var in os.environ:\n", "        del os.environ[triton_var]\n", "\n", "# 5. Set environment variable to disable tokenizers parallelism\n", "os.environ[\"TOKENIZERS_PARALLELISM\"] = \"false\"\n", "\n", "# 6. Enterprise-safe environment configuration\n", "os.environ['CUDA_VISIBLE_DEVICES'] = '0'\n", "os.environ['DISABLE_MLIR_THREADING'] = '1'\n", "os.environ['CUDA_LAUNCH_BLOCKING'] = '1'\n", "\n", "print(\"✅ Environment configured to avoid compilation\")\n", "\n", "# 7. Aggressive cleanup\n", "cleanup_dirs = [\n", "    os.path.expanduser(\"~/.cache\"),\n", "    os.path.expanduser(\"~/.triton\"),\n", "    os.path.expanduser(\"~/.torch\"),\n", "    user_temp,\n", "    \"/tmp\" if os.path.exists(\"/tmp\") else None\n", "]\n", "\n", "for cleanup_dir in cleanup_dirs:\n", "    if cleanup_dir and os.path.exists(cleanup_dir):\n", "        try:\n", "            # Only remove our own files to avoid permission issues\n", "            for root, dirs, files in os.walk(cleanup_dir):\n", "                for file in files:\n", "                    try:\n", "                        os.remove(os.path.join(root, file))\n", "                    except:\n", "                        pass\n", "            print(f\"✓ Cleaned {cleanup_dir}\")\n", "        except Exception as e:\n", "            print(f\"⚠️  Partial cleanup of {cleanup_dir}: {e}\")\n", "\n", "# 8. Clear Python cache\n", "for root, dirs, files in os.walk('.'):\n", "    for dir_name in dirs:\n", "        if dir_name == '__pycache__':\n", "            shutil.rmtree(os.path.join(root, dir_name), ignore_errors=True)\n", "\n", "# 9. Clear conda/pip caches\n", "try:\n", "    subprocess.run([\"conda\", \"clean\", \"-a\", \"-y\"], capture_output=True, timeout=30)\n", "    print(\"✓ Conda cache cleared\")\n", "except:\n", "    pass\n", "\n", "try:\n", "    subprocess.run([\"pip\", \"cache\", \"purge\"], capture_output=True, timeout=30)\n", "    print(\"✓ Pip cache cleared\")\n", "except:\n", "    pass\n", "\n", "print(\"🎯 READY: Environment optimized to avoid compilation disk space issues\")\n", "print(\"📝 NOTE: Triton compilation disabled, using pre-compiled binaries only\")"]}, {"cell_type": "code", "execution_count": 2, "id": "d1729006-9263-4a59-8275-10cf9c0ed18b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📦 Installing complete Unsloth stack for enterprise JupyterLab server...\n", "🔧 Running: scikit-learn\n", "✅ Success\n", "🔧 Running: git+https://github.com/unslothai/unsloth.git\n", "⚠️ Warning: \u001b[31mERROR: Invalid requirement: '@': Expected package name at the start of dependency specifier\n", "   \n", "🔧 Running: accelerate\n", "✅ Success\n", "🔧 Running: wandb\n", "✅ Success\n", "🔧 Running: it'\n", "✅ Success\n", "🎯 Complete installation finished!\n", "⚠️ IMPORTANT: Please restart the kernel now!\n", "🔄 <PERSON>el > <PERSON><PERSON> > Run All Cells\n"]}], "source": ["# 📦 ENTERPRISE UNSLOTH INSTALLATION: Complete package installation\n", "import subprocess\n", "import sys\n", "\n", "print(\"📦 Installing complete Unsloth stack for enterprise JupyterLab server...\")\n", "\n", "# Install comprehensive package list\n", "install_commands = [\n", "    # Core packages\n", "    f\"{sys.executable} -m pip install --no-cache-dir --user transformers datasets torch scikit-learn\",\n", "    \n", "    # Unsloth with proper flags\n", "    f\"{sys.executable} -m pip install --no-cache-dir --user unsloth[colab-new] @ git+https://github.com/unslothai/unsloth.git\",\n", "    \n", "    # TRL and supporting packages\n", "    f\"{sys.executable} -m pip install --no-cache-dir --user trl peft accelerate\",\n", "    \n", "    # Monitoring and evaluation\n", "    f\"{sys.executable} -m pip install --no-cache-dir --user wandb\",\n", "    \n", "    # Install xformers separately (may fail in some environments)\n", "    f\"{sys.executable} -m pip install --no-cache-dir --user xformers --no-deps || echo 'xformers install failed, continuing without it'\"\n", "]\n", "\n", "for cmd in install_commands:\n", "    try:\n", "        print(f\"🔧 Running: {cmd.split()[-1] if 'install' in cmd else cmd}\")\n", "        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)\n", "        if result.returncode == 0:\n", "            print(\"✅ Success\")\n", "        else:\n", "            print(f\"⚠️ Warning: {result.stderr[:100] if result.stderr else 'Unknown error'}\")\n", "    except Exception as e:\n", "        print(f\"⚠️ Error: {e}\")\n", "\n", "print(\"🎯 Complete installation finished!\")\n", "print(\"⚠️ IMPORTANT: Please restart the kernel now!\")\n", "print(\"🔄 <PERSON> > <PERSON><PERSON> > Run All Cells\")"]}, {"cell_type": "code", "execution_count": 3, "id": "7996a47f-ce24-4992-a9c6-b8548e412e36", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n", "🦥 Unsloth Zoo will now patch everything to make training faster!\n", "🛡️  team_14_gpt → 32GB VRAM + Triton fixed\n", "✅ Unsloth imported successfully\n", "🤖 Loading Llama 3.2-3B Instruct with Unsloth...\n", "Unsloth: WARNING `trust_remote_code` is True.\n", "Are you certain you want to do remote code execution?\n", "==((====))==  Unsloth 2025.7.3: Fast Llama patching. Transformers: 4.53.2.\n", "   \\\\   /|    NVIDIA A100-SXM4-80GB. Num GPUs = 1. Max memory: 79.252 GB. Platform: Linux.\n", "O^O/ \\_/ \\    Torch: 2.7.1+cu126. CUDA: 8.0. CUDA Toolkit: 12.6. Triton: 3.3.1\n", "\\        /    Bfloat16 = TRUE. FA [Xformers = 0.0.31.post1. FA2 = False]\n", " \"-____-\"     Free license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7bb9ea0fcd7144fb8998e21b421cc3dd", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/2.35G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "734da7b7b6d745f7ab052869a93bd620", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/234 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7664fb358be340559d777e4fe6b11f23", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2e47b7b3ea1a40008bf50eeb0c15b64f", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/454 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9a365736fb534b1d93ab05dc03cf741a", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/17.2M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4be798a1d38548878389487f35efb7ac", "version_major": 2, "version_minor": 0}, "text/plain": ["chat_template.jinja: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ Model loaded successfully: meta-llama/Llama-3.2-3B-Instruct\n", "📏 Max sequence length: 2048\n", "🔧 4-bit quantization: True\n", "💾 Model type: <class 'transformers.models.llama.modeling_llama.LlamaForCausalLM'>\n", "🔧 Tokenizer type: <class 'transformers.tokenization_utils_fast.PreTrainedTokenizerFast'>\n", "✓ CUDA cache cleared\n", "🧪 Testing model...\n", "✅ Tokenizer working, input shape: torch.<PERSON><PERSON>([1, 7])\n", "✅ Ready for LoRA configuration!\n"]}], "source": ["# 🤖 LOAD LLAMA 3.2-3B INSTRUCT: Optimized model loading with proper configuration\n", "# CRITICAL: Import unsloth FIRST before any other transformers library\n", "try:\n", "    import unsloth\n", "    from unsloth import FastLanguageModel\n", "    import torch\n", "    print(\"✅ Unsloth imported successfully\")\n", "except ImportError as e:\n", "    print(f\"❌ Unsloth import failed: {e}\")\n", "    print(\"💡 Please restart kernel and rerun installation cell\")\n", "    raise\n", "\n", "print(\"🤖 Loading Llama 3.2-3B Instruct with Unsloth...\")\n", "\n", "# Model configuration - optimized for better performance\n", "model_name = \"meta-llama/Llama-3.2-3B-Instruct\"\n", "max_seq_length = 2048  # Choose any! We auto support RoPE Scaling internally!\n", "dtype = None  # None for auto detection. Float16 for Tesla T4, V100, Bfloat16 for Ampere+\n", "load_in_4bit = True  # Use 4bit quantization to reduce memory usage. Can be False.\n", "\n", "# Your HuggingFace token\n", "hf_token = \"*************************************\"\n", "\n", "try:\n", "    # Load model and tokenizer with optimized settings\n", "    model, tokenizer = FastLanguageModel.from_pretrained(\n", "        model_name=model_name,\n", "        max_seq_length=max_seq_length,\n", "        dtype=dtype,\n", "        load_in_4bit=load_in_4bit,\n", "        token=hf_token,\n", "        # Enterprise-safe additional parameters\n", "        device_map=\"auto\",\n", "        trust_remote_code=True,\n", "        low_cpu_mem_usage=True,\n", "    )\n", "\n", "    print(f\"✅ Model loaded successfully: {model_name}\")\n", "    print(f\"📏 Max sequence length: {max_seq_length}\")\n", "    print(f\"🔧 4-bit quantization: {load_in_4bit}\")\n", "    print(f\"💾 Model type: {type(model)}\")\n", "    print(f\"🔧 Tokenizer type: {type(tokenizer)}\")\n", "    \n", "    # Clear CUDA cache\n", "    if torch.cuda.is_available():\n", "        torch.cuda.empty_cache()\n", "        print(\"✓ CUDA cache cleared\")\n", "    \n", "    # Verify model is working\n", "    print(\"🧪 Testing model...\")\n", "    test_input = \"Hello, how are you?\"\n", "    inputs = tokenizer(test_input, return_tensors=\"pt\")\n", "    print(f\"✅ Tokenizer working, input shape: {inputs['input_ids'].shape}\")\n", "    \n", "    print(\"✅ Ready for LoRA configuration!\")\n", "\n", "except Exception as e:\n", "    print(f\"❌ Error loading model: {e}\")\n", "    print(\"💡 Troubleshooting:\")\n", "    print(\"   1. Check if you have Llama 3.2-3B-Instruct access\")\n", "    print(\"   2. Verify HuggingFace token is correct\")\n", "    print(\"   3. Ensure GPU memory is available\")\n", "    print(\"   4. Try restarting kernel and running cells in order\")\n", "    \n", "    # Environment info for debugging\n", "    print(\"\\n🔧 Environment info:\")\n", "    print(f\"   Python: {sys.version}\")\n", "    print(f\"   PyTorch: {torch.__version__ if 'torch' in locals() else 'Not loaded'}\")\n", "    print(f\"   CUDA available: {torch.cuda.is_available() if 'torch' in locals() else 'Unknown'}\")\n", "    \n", "    model = None\n", "    tokenizer = None"]}, {"cell_type": "code", "execution_count": 4, "id": "bbaf2bc4-ad63-435f-b910-7f726a5bdcd9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 Configuring LoRA with optimized settings...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Unsloth 2025.7.3 patched 28 layers with 28 QKV layers, 28 O layers and 28 MLP layers.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ LoRA configuration applied successfully!\n", "📊 Model Statistics:\n", "   • Trainable parameters: 24,313,856\n", "   • All parameters: 1,865,526,272\n", "   • Trainable percentage: 1.30%\n", "   • Memory efficiency: 98.7% reduction\n", "🧪 Testing LoRA model...\n", "✅ LoRA model ready, input shape: torch.Size([1, 5])\n", "✓ CUDA cache cleared after LoRA setup\n", "🎯 Ready for dataset preparation!\n"]}], "source": ["# 🔧 OPTIMIZED LORA CONFIGURATION: Enhanced LoRA setup for better performance\n", "from unsloth import FastLanguageModel\n", "\n", "if model is None:\n", "    print(\"❌ No model loaded - please run the model loading cell first\")\n", "else:\n", "    print(\"🔧 Configuring LoRA with optimized settings...\")\n", "    \n", "    try:\n", "        # Enhanced LoRA configuration based on successful Qwen approach\n", "        model = FastLanguageModel.get_peft_model(\n", "            model,\n", "            r=16,  # LoRA rank - good balance between performance and efficiency\n", "            target_modules=[\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\", \n", "                           \"gate_proj\", \"up_proj\", \"down_proj\"],  # Target all attention and MLP layers\n", "            lora_alpha=16,  # LoRA alpha - same as rank for optimal performance\n", "            lora_dropout=0,  # 0 is optimized for unsloth\n", "            bias=\"none\",     # \"none\" is optimized for unsloth\n", "            use_gradient_checkpointing=True,  # Enable gradient checkpointing for memory efficiency\n", "            random_state=42,  # Fixed random state for reproducibility\n", "            use_rslora=False,  # Disable RSLora for stability\n", "            loftq_config=None,  # No LoFTQ configuration\n", "        )\n", "        \n", "        print(\"✅ LoRA configuration applied successfully!\")\n", "        \n", "        # Calculate and display trainable parameters\n", "        trainable_params = 0\n", "        all_params = 0\n", "        for name, param in model.named_parameters():\n", "            all_params += param.numel()\n", "            if param.requires_grad:\n", "                trainable_params += param.numel()\n", "        \n", "        print(f\"📊 Model Statistics:\")\n", "        print(f\"   • Trainable parameters: {trainable_params:,}\")\n", "        print(f\"   • All parameters: {all_params:,}\")\n", "        print(f\"   • Trainable percentage: {100 * trainable_params / all_params:.2f}%\")\n", "        print(f\"   • Memory efficiency: {100 * (1 - trainable_params / all_params):.1f}% reduction\")\n", "        \n", "        # Verify LoRA model is ready\n", "        print(\"🧪 Testing LoRA model...\")\n", "        test_input = \"Test LoRA configuration\"\n", "        inputs = tokenizer(test_input, return_tensors=\"pt\")\n", "        print(f\"✅ LoRA model ready, input shape: {inputs['input_ids'].shape}\")\n", "        \n", "        # Clear memory after configuration\n", "        if torch.cuda.is_available():\n", "            torch.cuda.empty_cache()\n", "            print(\"✓ CUDA cache cleared after LoRA setup\")\n", "        \n", "        print(\"🎯 Ready for dataset preparation!\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ LoRA configuration failed: {e}\")\n", "        print(\"💡 Troubleshooting:\")\n", "        print(\"   1. Ensure model loaded properly in previous cell\")\n", "        print(\"   2. Check GPU memory availability\")\n", "        print(\"   3. Verify unsloth version compatibility\")\n", "        print(\"   4. Try reducing LoRA rank (r) from 16 to 8\")\n", "        print(\"   5. <PERSON><PERSON> kernel and run all cells in order\")\n", "        model = None"]}, {"cell_type": "code", "execution_count": 5, "id": "4665ef86-9a11-4a0e-928d-b24694ab9515", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📚 Advanced dataset preparation for Llama 3.2 Instruct...\n", "🔧 EOS Token: <|eot_id|>\n", "📥 Loading datasets...\n", "⚠️ File not found: cleaned_data.jsonl\n", "⚠️ File not found: cleaned_data_arabic.jsonl\n", "📥 Loading Arabic Translation dataset from translated_output_ar.jsonl...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Old caching folder /data/teams/heavy/team_14_gpt/.cache/huggingface/datasets/json/default-d5d914ace07f249c/0.0.0/f4e89e8750d5d5ffbef2c078bf0ddfedef29dc2faff52a6255cf513c05eb1092 for dataset json exists but no data were found. Removing it. \n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5554d20ec3dc488c8d039291e5275510", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating train split: 0 examples [00:00, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d9b9aac88a9e40cf82604f2ac8413c96", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/1000 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ Arabic Translation: 1000 samples loaded\n", "🔗 Concatenating datasets...\n", "📊 Total combined samples: 1000\n", "🔄 Converting to pandas for stratified splitting...\n", "📊 Language distribution: {'ar': 1000}\n", "📊 Data split completed:\n", "   • Train: 800 samples\n", "   • Test: 100 samples\n", "   • Eval: 100 samples\n", "🎨 Formatting datasets...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9bfa36d42c394fdc99796b81b56869e4", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/800 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ Successfully formatted 800 examples\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0202dc25937b4f3082165548e8bd7e16", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/100 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ Successfully formatted 100 examples\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9d3f93ccde794373a28a74851bab5e47", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/100 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ Successfully formatted 100 examples\n", "💾 Saving datasets...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "57ec27d7fbfb4cc2a4ca4d19bc1a5ac5", "version_major": 2, "version_minor": 0}, "text/plain": ["Saving the dataset (0/1 shards):   0%|          | 0/800 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1c5b6f3822664ad7b90757ce9b450647", "version_major": 2, "version_minor": 0}, "text/plain": ["Saving the dataset (0/1 shards):   0%|          | 0/100 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "03385c247b794232aef74704bf1e4f8f", "version_major": 2, "version_minor": 0}, "text/plain": ["Saving the dataset (0/1 shards):   0%|          | 0/100 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ Dataset preparation completed successfully!\n", "📊 Final dataset sizes:\n", "   • Training: 800 samples\n", "   • Testing: 100 samples\n", "   • Evaluation: 100 samples\n", "\n", "📋 Sample formatted prompt:\n", "<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n", "\n", "You are a helpful assistant specialized in translation and multilingual tasks. Provide accurate and contextual responses.<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\n", "كيف تستخدم التحليلات التنبؤية للمخاطر؟\n", "\n", "Input: التنبؤ بالمخاطر<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\n", "توقع المخاطر بالبيانات التاريخية.<|eot_id|><|eot_id|>\n", "🎯 Ready for training!\n"]}], "source": ["# 📚 ADVANCED DATASET PREPARATION: Proper data loading, splitting, and formatting\n", "import os\n", "import json\n", "from datasets import load_dataset, concatenate_datasets, Dataset\n", "from sklearn.model_selection import train_test_split\n", "import pandas as pd\n", "\n", "print(\"📚 Advanced dataset preparation for Llama 3.2 Instruct...\")\n", "\n", "# Get EOS token\n", "if tokenizer is None:\n", "    print(\"❌ No tokenizer available - please run model loading cell first\")\n", "else:\n", "    EOS_TOKEN = tokenizer.eos_token\n", "    print(f\"🔧 EOS Token: {EOS_TOKEN}\")\n", "\n", "# Enhanced Llama 3.2 Instruct prompt template with better formatting\n", "alpaca_prompt = \"\"\"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n", "\n", "You are a helpful assistant specialized in translation and multilingual tasks. Provide accurate and contextual responses.<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\n", "{}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\n", "{}<|eot_id|>\"\"\"\n", "\n", "def formatting_prompts_func(examples):\n", "    \"\"\"\n", "    Format dataset examples into prompts for fine-tuning with enhanced validation.\n", "    \"\"\"\n", "    instructions = examples.get(\"instruction\", [])\n", "    inputs = examples.get(\"input\", [])\n", "    outputs = examples.get(\"output\", [])\n", "    \n", "    # Handle cases where input might be missing\n", "    if not inputs:\n", "        inputs = [\"\"] * len(instructions)\n", "    \n", "    texts = []\n", "    valid_count = 0\n", "    \n", "    for instruction, input_text, output in zip(instructions, inputs, outputs):\n", "        # Validate data quality\n", "        if not instruction or not output:\n", "            continue\n", "            \n", "        # Combine instruction and input for better context\n", "        if input_text and input_text.strip():\n", "            full_instruction = f\"{instruction}\\n\\nInput: {input_text}\"\n", "        else:\n", "            full_instruction = instruction\n", "            \n", "        # Format with Llama 3.2 template\n", "        try:\n", "            formatted_text = alpaca_prompt.format(full_instruction, output) + EOS_TOKEN\n", "            texts.append(formatted_text)\n", "            valid_count += 1\n", "        except Exception as e:\n", "            print(f\"⚠️ Formatting error: {e}\")\n", "            continue\n", "    \n", "    print(f\"✅ Successfully formatted {valid_count} examples\")\n", "    return {\"text\": texts}\n", "\n", "def load_and_split_datasets():\n", "    \"\"\"\n", "    Load JSONL datasets, add metadata, concatenate, and split into train/test/eval sets.\n", "    \"\"\"\n", "    # Dataset information\n", "    datasets_info = [\n", "        (\"cleaned_data.jsonl\", \"English\", \"en\"),\n", "        (\"cleaned_data_arabic.jsonl\", \"Arabic\", \"ar\"),\n", "        (\"translated_output_ar.jsonl\", \"Arabic Translation\", \"ar\")\n", "    ]\n", "    \n", "    loaded_datasets = []\n", "    total_samples = 0\n", "    \n", "    print(\"📥 Loading datasets...\")\n", "    \n", "    for file_name, description, lang_code in datasets_info:\n", "        file_path = file_name\n", "        if os.path.exists(file_path):\n", "            try:\n", "                print(f\"📥 Loading {description} dataset from {file_path}...\")\n", "                ds = load_dataset(\"json\", data_files=file_path, split=\"train\")\n", "                \n", "                # Verify required columns\n", "                required_cols = [\"instruction\", \"output\"]\n", "                missing_cols = [col for col in required_cols if col not in ds.column_names]\n", "                \n", "                if missing_cols:\n", "                    print(f\"⚠️ {file_path} missing columns: {missing_cols}\")\n", "                    continue\n", "                \n", "                # Add metadata\n", "                ds = ds.map(lambda x: {**x, \"language\": lang_code, \"source\": description})\n", "                \n", "                # Add to loaded datasets\n", "                loaded_datasets.append(ds)\n", "                total_samples += len(ds)\n", "                print(f\"✅ {description}: {len(ds)} samples loaded\")\n", "                \n", "            except Exception as e:\n", "                print(f\"❌ Error loading {file_path}: {e}\")\n", "                continue\n", "        else:\n", "            print(f\"⚠️ File not found: {file_path}\")\n", "    \n", "    if not loaded_datasets:\n", "        print(\"❌ No datasets loaded successfully\")\n", "        return None, None, None\n", "    \n", "    # Concatenate all datasets\n", "    print(\"🔗 Concatenating datasets...\")\n", "    combined_dataset = concatenate_datasets(loaded_datasets)\n", "    print(f\"📊 Total combined samples: {len(combined_dataset)}\")\n", "    \n", "    # Convert to pandas for stratified splitting\n", "    print(\"🔄 Converting to pandas for stratified splitting...\")\n", "    df = combined_dataset.to_pandas()\n", "    \n", "    # Check language distribution\n", "    lang_dist = df['language'].value_counts()\n", "    print(f\"📊 Language distribution: {lang_dist.to_dict()}\")\n", "    \n", "    try:\n", "        # Split into train (80%) and temp (20%) sets, stratified by language\n", "        train_df, temp_df = train_test_split(\n", "            df,\n", "            test_size=0.2,\n", "            stratify=df[\"language\"],\n", "            random_state=42\n", "        )\n", "        \n", "        # Split temp into test (50%) and eval (50%) sets  \n", "        test_df, eval_df = train_test_split(\n", "            temp_df,\n", "            test_size=0.5,\n", "            stratify=temp_df[\"language\"],\n", "            random_state=42\n", "        )\n", "        \n", "        print(f\"📊 Data split completed:\")\n", "        print(f\"   • Train: {len(train_df)} samples\")\n", "        print(f\"   • Test: {len(test_df)} samples\")\n", "        print(f\"   • Eval: {len(eval_df)} samples\")\n", "        \n", "        # Convert back to Hugging Face Datasets and remove metadata columns\n", "        train_dataset = Dataset.from_pandas(train_df).remove_columns(['language', 'source'])\n", "        test_dataset = Dataset.from_pandas(test_df).remove_columns(['language', 'source'])\n", "        eval_dataset = Dataset.from_pandas(eval_df).remove_columns(['language', 'source'])\n", "        \n", "        # Apply formatting function to each split\n", "        print(\"🎨 Formatting datasets...\")\n", "        train_dataset = train_dataset.map(formatting_prompts_func, batched=True, remove_columns=train_dataset.column_names)\n", "        test_dataset = test_dataset.map(formatting_prompts_func, batched=True, remove_columns=test_dataset.column_names)\n", "        eval_dataset = eval_dataset.map(formatting_prompts_func, batched=True, remove_columns=eval_dataset.column_names)\n", "        \n", "        # Save datasets for later use\n", "        print(\"💾 Saving datasets...\")\n", "        train_dataset.save_to_disk(\"train_dataset\")\n", "        test_dataset.save_to_disk(\"test_dataset\")\n", "        eval_dataset.save_to_disk(\"eval_dataset\")\n", "        \n", "        return train_dataset, test_dataset, eval_dataset\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error during stratified splitting: {e}\")\n", "        print(\"💡 Falling back to simple random split...\")\n", "        \n", "        # Simple random split as fallback\n", "        train_df = df.sample(frac=0.8, random_state=42)\n", "        temp_df = df.drop(train_df.index)\n", "        test_df = temp_df.sample(frac=0.5, random_state=42)\n", "        eval_df = temp_df.drop(test_df.index)\n", "        \n", "        # Convert and format\n", "        train_dataset = Dataset.from_pandas(train_df).remove_columns(['language', 'source'])\n", "        test_dataset = Dataset.from_pandas(test_df).remove_columns(['language', 'source'])\n", "        eval_dataset = Dataset.from_pandas(eval_df).remove_columns(['language', 'source'])\n", "        \n", "        train_dataset = train_dataset.map(formatting_prompts_func, batched=True, remove_columns=train_dataset.column_names)\n", "        test_dataset = test_dataset.map(formatting_prompts_func, batched=True, remove_columns=test_dataset.column_names)\n", "        eval_dataset = eval_dataset.map(formatting_prompts_func, batched=True, remove_columns=eval_dataset.column_names)\n", "        \n", "        return train_dataset, test_dataset, eval_dataset\n", "\n", "# Execute dataset preparation\n", "train_dataset, test_dataset, eval_dataset = load_and_split_datasets()\n", "\n", "if train_dataset is not None:\n", "    print(\"✅ Dataset preparation completed successfully!\")\n", "    print(f\"📊 Final dataset sizes:\")\n", "    print(f\"   • Training: {len(train_dataset)} samples\")\n", "    print(f\"   • Testing: {len(test_dataset)} samples\")\n", "    print(f\"   • Evaluation: {len(eval_dataset)} samples\")\n", "    \n", "    # Show sample formatted prompt\n", "    if len(train_dataset) > 0:\n", "        print(f\"\\n📋 Sample formatted prompt:\")\n", "        sample = train_dataset[0][\"text\"]\n", "        print(sample[:500] + \"...\" if len(sample) > 500 else sample)\n", "    \n", "    print(\"🎯 Ready for training!\")\n", "else:\n", "    print(\"❌ Dataset preparation failed. Please check your data files.\")\n", "    train_dataset = test_dataset = eval_dataset = None"]}, {"cell_type": "code", "execution_count": null, "id": "8e288640-0f43-4a15-a8f1-28bfc6d0a889", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 Setting up optimized training with evaluation...\n", "🔧 Hardware: NVIDIA A100-SXM4-80GB (85.1GB)\n", "🔧 BFloat16 supported: True\n", "📊 OPTIMIZED Training Configuration (2 Epochs):\n", "   • Training samples: 800\n", "   • Batch size: 2\n", "   • Gradient accumulation: 4\n", "   • Effective batch size: 8\n", "   • Steps per epoch: 100\n", "   • Target epochs: 2\n", "   • TOTAL TRAINING STEPS: 1000\n", "   • Estimated training time: 33.3 minutes\n", "✅ OPTIMIZED Training arguments configured (2 Epochs)\n", "   • Precision: BFloat16\n", "   • Optimizer: AdamW 8-bit\n", "   • Learning rate: 0.0001\n", "   • Warmup steps: 50\n", "   • LR scheduler: cosine\n", "   • Total steps: 1000\n", "   • Evaluation frequency: Every 50 steps\n", "   • Save frequency: Every 100 steps\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9a9dda0ea1c9472cb899bd15491087ea", "version_major": 2, "version_minor": 0}, "text/plain": ["Unsloth: Tokenizing [\"text\"]:   0%|          | 0/800 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3c281840c3124118a818f8e659f8d61c", "version_major": 2, "version_minor": 0}, "text/plain": ["Unsloth: Tokenizing [\"text\"]:   0%|          | 0/100 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ SFTTrainer configured successfully!\n", "✓ CUDA cache cleared before training\n", "\n", "🚀 Starting OPTIMIZED training (1000 steps - 2 epochs)...\n", "📊 This will take approximately 33.3 minutes\n", "============================================================\n", "\n", "📊 TRAINING PROGRESS TABLE\n", "======================================================================\n", "Step     Training Loss   Validation Loss Learning Rate\n", "----------------------------------------------------------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["==((====))==  Unsloth - 2x faster free finetuning | Num GPUs used = 1\n", "   \\\\   /|    Num examples = 800 | Num Epochs = 10 | Total steps = 1,000\n", "O^O/ \\_/ \\    Batch size per device = 2 | Gradient accumulation steps = 4\n", "\\        /    Data Parallel GPUs = 1 | Total batch size (2 x 4 x 1) = 8\n", " \"-____-\"     Trainable parameters = 24,313,856 of 3,237,063,680 (0.75% trained)\n", "/data/miniconda3/envs/team_14_gpt/lib/python3.10/site-packages/triton/runtime/interpreter.py:544: RuntimeWarning: overflow encountered in exp\n", "  return TensorHandle(op(arg.data), arg.dtype.scalar)\n"]}], "source": ["# 🚀 OPTIMIZED TRAINING: Advanced training configuration with evaluation and monitoring\n", "from trl import SFTTrainer\n", "from transformers import TrainingArguments\n", "import torch\n", "import math\n", "import time\n", "import threading\n", "from IPython.display import display, clear_output\n", "import sys\n", "\n", "print(\"🚀 Setting up optimized training with evaluation...\")\n", "\n", "# Real-time training progress monitor with classic table format\n", "class TrainingProgressMonitor:\n", "    def __init__(self, trainer, max_steps):\n", "        self.trainer = trainer\n", "        self.max_steps = max_steps\n", "        self.monitoring = False\n", "        self.last_step = 0\n", "        self.start_time = time.time()\n", "        self.shown_steps = set()  # Track which steps we've already shown\n", "        \n", "    def start_monitoring(self):\n", "        \"\"\"Start monitoring training progress in real-time\"\"\"\n", "        self.monitoring = True\n", "        # Print table header\n", "        print(\"\\n📊 TRAINING PROGRESS TABLE\")\n", "        print(\"=\" * 70)\n", "        print(f\"{'Step':<8} {'Training Loss':<15} {'Validation Loss':<15} {'Learning Rate':<12}\")\n", "        print(\"-\" * 70)\n", "        \n", "        monitor_thread = threading.Thread(target=self._monitor_loop)\n", "        monitor_thread.daemon = True\n", "        monitor_thread.start()\n", "        \n", "    def stop_monitoring(self):\n", "        \"\"\"Stop monitoring\"\"\"\n", "        self.monitoring = False\n", "        \n", "    def _monitor_loop(self):\n", "        \"\"\"Monitor training progress and show table format\"\"\"\n", "        while self.monitoring:\n", "            try:\n", "                if hasattr(self.trainer, 'state') and self.trainer.state:\n", "                    current_step = self.trainer.state.global_step\n", "                    \n", "                    # Only show new steps\n", "                    if current_step > self.last_step and current_step not in self.shown_steps:\n", "                        self.last_step = current_step\n", "                        self.shown_steps.add(current_step)\n", "                        \n", "                        # Get latest log history\n", "                        if hasattr(self.trainer.state, 'log_history') and self.trainer.state.log_history:\n", "                            latest_log = self.trainer.state.log_history[-1]\n", "                            \n", "                            # Extract metrics\n", "                            train_loss = latest_log.get('loss', 'N/A')\n", "                            eval_loss = latest_log.get('eval_loss', 'N/A')\n", "                            lr = latest_log.get('learning_rate', 'N/A')\n", "                            \n", "                            # Format values\n", "                            train_loss_str = f\"{train_loss:.6f}\" if train_loss != 'N/A' else 'N/A'\n", "                            eval_loss_str = f\"{eval_loss:.6f}\" if eval_loss != 'N/A' else 'N/A'\n", "                            lr_str = f\"{lr:.2e}\" if lr != 'N/A' else 'N/A'\n", "                            \n", "                            # Print table row\n", "                            print(f\"{current_step:<8} {train_loss_str:<15} {eval_loss_str:<15} {lr_str:<12}\")\n", "                            \n", "                            # Show progress indicator every 50 steps\n", "                            if current_step % 50 == 0:\n", "                                progress = current_step / self.max_steps\n", "                                elapsed_time = time.time() - self.start_time\n", "                                steps_per_second = current_step / elapsed_time if elapsed_time > 0 else 0\n", "                                eta_minutes = (self.max_steps - current_step) / steps_per_second / 60 if steps_per_second > 0 else 0\n", "                                \n", "                                print(f\"📈 Progress: {progress:.1%} | Speed: {steps_per_second:.2f} steps/sec | ETA: {eta_minutes:.1f}min\")\n", "                                print(\"-\" * 70)\n", "                \n", "                time.sleep(2)  # Check every 2 seconds for faster updates\n", "                \n", "            except Exception as e:\n", "                print(f\"⚠️ Monitoring error: {e}\")\n", "                time.sleep(5)  # Wait longer on error\n", "\n", "# Verify all components are ready\n", "if model is None:\n", "    print(\"❌ No model available - please run model loading and LoRA configuration cells\")\n", "elif tokenizer is None:\n", "    print(\"❌ No tokenizer available - please run model loading cell\")\n", "elif train_dataset is None:\n", "    print(\"❌ No training dataset available - please run dataset preparation cell\")\n", "else:\n", "    try:\n", "        # Check hardware capabilities\n", "        def check_hardware_capabilities():\n", "            \"\"\"Check if bfloat16 is supported and get device info\"\"\"\n", "            try:\n", "                if torch.cuda.is_available():\n", "                    device_capability = torch.cuda.get_device_capability()\n", "                    supports_bf16 = device_capability[0] >= 8\n", "                    gpu_name = torch.cuda.get_device_name()\n", "                    memory_gb = torch.cuda.get_device_properties(0).total_memory / 1e9\n", "                    return supports_bf16, gpu_name, memory_gb\n", "                return False, \"CPU\", 0\n", "            except:\n", "                return False, \"Unknown\", 0\n", "        \n", "        supports_bf16, gpu_name, memory_gb = check_hardware_capabilities()\n", "        print(f\"🔧 Hardware: {gpu_name} ({memory_gb:.1f}GB)\")\n", "        print(f\"🔧 BFloat16 supported: {supports_bf16}\")\n", "        \n", "        # Calculate training parameters - MUCH MORE AGGRESSIVE TRAINING\n", "        train_size = len(train_dataset)\n", "        batch_size = 2\n", "        gradient_accumulation_steps = 4\n", "        effective_batch_size = batch_size * gradient_accumulation_steps\n", "        \n", "        # Calculate steps for MULTIPLE epochs with much higher limits\n", "        steps_per_epoch = math.ceil(train_size / effective_batch_size)\n", "        \n", "        # OPTIMIZED TRAINING STEPS\n", "        # Target: 2 epochs for optimal balance between quality and training time\n", "        target_epochs = 2\n", "        max_steps = steps_per_epoch * target_epochs\n", "        \n", "        # Ensure minimum of 1000 steps even for small datasets\n", "        max_steps = max(max_steps, 1000)\n", "        \n", "        # Cap at reasonable maximum (5,000 steps for efficiency)\n", "        max_steps = min(max_steps, 5000)\n", "        \n", "        print(f\"📊 OPTIMIZED Training Configuration (2 Epochs):\")\n", "        print(f\"   • Training samples: {train_size}\")\n", "        print(f\"   • Batch size: {batch_size}\")\n", "        print(f\"   • Gradient accumulation: {gradient_accumulation_steps}\")\n", "        print(f\"   • Effective batch size: {effective_batch_size}\")\n", "        print(f\"   • Steps per epoch: {steps_per_epoch}\")\n", "        print(f\"   • Target epochs: {target_epochs}\")\n", "        print(f\"   • TOTAL TRAINING STEPS: {max_steps}\")\n", "        print(f\"   • Estimated training time: {max_steps * 2 / 60:.1f} minutes\")\n", "        \n", "        # ENHANCED training arguments for MUCH better results\n", "        training_args = TrainingArguments(\n", "            output_dir=\"./llama3.2-3b-translation-model\",\n", "            \n", "            # Training configuration\n", "            per_device_train_batch_size=batch_size,\n", "            per_device_eval_batch_size=batch_size,\n", "            gradient_accumulation_steps=gradient_accumulation_steps,\n", "            \n", "            # MUCH MORE AGGRESSIVE Learning schedule\n", "            learning_rate=1e-4,  # Slightly lower LR for stability over longer training\n", "            warmup_steps=max(50, max_steps // 20),  # 5% warmup (more gradual)\n", "            max_steps=max_steps,\n", "            lr_scheduler_type=\"cosine\",  # Cosine decay for better convergence\n", "            \n", "            # Precision settings\n", "            fp16=not supports_bf16,\n", "            bf16=supports_bf16,\n", "            \n", "            # Optimizer settings\n", "            optim=\"adamw_8bit\",\n", "            weight_decay=0.01,\n", "            adam_beta1=0.9,\n", "            adam_beta2=0.999,\n", "            adam_epsilon=1e-8,\n", "            \n", "            # MUCH MORE FREQUENT Logging and saving\n", "            logging_steps=max(10, max_steps // 100),  # Log 100 times during training\n", "            save_steps=max(100, max_steps // 20),     # Save 20 times during training\n", "            save_total_limit=5,  # Keep more checkpoints\n", "            \n", "            # FREQUENT Evaluation settings\n", "            eval_strategy=\"steps\",\n", "            eval_steps=max(50, max_steps // 40),     # Evaluate 40 times during training\n", "            eval_accumulation_steps=4,\n", "            \n", "            # Memory and performance optimizations\n", "            dataloader_num_workers=0,\n", "            dataloader_pin_memory=False,\n", "            remove_unused_columns=False,\n", "            \n", "            # Enhanced monitoring\n", "            report_to=\"none\",  # Can enable wandb if needed\n", "            logging_dir=\"./logs\",\n", "            load_best_model_at_end=True,\n", "            metric_for_best_model=\"eval_loss\",\n", "            greater_is_better=False,\n", "            \n", "            # Reproducibility\n", "            seed=42,\n", "            data_seed=42,\n", "            \n", "            # Safety settings for intensive training\n", "            max_grad_norm=1.0,\n", "            gradient_checkpointing=True,\n", "            \n", "            # Learning rate scheduling\n", "            warmup_ratio=0.05,  # 5% warmup\n", "            \n", "            # Additional optimizations\n", "            group_by_length=True,\n", "            ddp_find_unused_parameters=False,\n", "            \n", "            # Additional safety settings\n", "            dataloader_drop_last=False,\n", "            prediction_loss_only=False,\n", "        )\n", "        \n", "        print(f\"✅ OPTIMIZED Training arguments configured (2 Epochs)\")\n", "        print(f\"   • Precision: {'BFloat16' if supports_bf16 else 'Float16'}\")\n", "        print(f\"   • Optimizer: AdamW 8-bit\")\n", "        print(f\"   • Learning rate: {training_args.learning_rate}\")\n", "        print(f\"   • Warmup steps: {training_args.warmup_steps}\")\n", "        print(f\"   • LR scheduler: {training_args.lr_scheduler_type}\")\n", "        print(f\"   • Total steps: {training_args.max_steps}\")\n", "        print(f\"   • Evaluation frequency: Every {training_args.eval_steps} steps\")\n", "        print(f\"   • Save frequency: Every {training_args.save_steps} steps\")\n", "        \n", "        # Create trainer with evaluation dataset\n", "        trainer = SFTT<PERSON>er(\n", "            model=model,\n", "            tokenizer=tokenizer,\n", "            train_dataset=train_dataset,\n", "            eval_dataset=eval_dataset if eval_dataset is not None else None,\n", "            dataset_text_field=\"text\",\n", "            max_seq_length=max_seq_length,\n", "            args=training_args,\n", "            packing=False,  # Disable packing for better stability\n", "        )\n", "        \n", "        print(\"✅ SFTTrainer configured successfully!\")\n", "        \n", "        # Clear cache before training\n", "        if torch.cuda.is_available():\n", "            torch.cuda.empty_cache()\n", "            print(\"✓ CUDA cache cleared before training\")\n", "        \n", "        # Start OPTIMIZED training with progress monitoring\n", "        print(f\"\\n🚀 Starting OPTIMIZED training ({max_steps} steps - 2 epochs)...\")\n", "        print(f\"📊 This will take approximately {max_steps * 2 / 60:.1f} minutes\")\n", "        print(\"=\" * 60)\n", "        \n", "        # Initialize progress monitor\n", "        progress_monitor = TrainingProgressMonitor(trainer, max_steps)\n", "        progress_monitor.start_time = time.time()  # Set start time directly\n", "        \n", "        # Start monitoring in background\n", "        progress_monitor.start_monitoring()\n", "        \n", "        try:\n", "            # Start training\n", "            trainer_stats = trainer.train()\n", "            \n", "            # Stop monitoring\n", "            progress_monitor.stop_monitoring()\n", "            \n", "        except Exception as train_error:\n", "            # Stop monitoring on error\n", "            progress_monitor.stop_monitoring()\n", "            raise train_error\n", "        \n", "        print(\"=\" * 60)\n", "        print(\"✅ OPTIMIZED Training completed successfully!\")\n", "        print(f\"📊 Final Training Statistics:\")\n", "        print(f\"   • Final training loss: {trainer_stats.training_loss:.4f}\")\n", "        print(f\"   • Total steps completed: {trainer_stats.global_step}\")\n", "        print(f\"   • Training epochs: {trainer_stats.global_step / steps_per_epoch:.2f}\")\n", "        print(f\"   • Training time: {trainer_stats.metrics.get('train_runtime', 0):.2f}s\")\n", "        print(f\"   • Steps per second: {trainer_stats.global_step / trainer_stats.metrics.get('train_runtime', 1):.2f}\")\n", "        \n", "        # Enhanced training metrics\n", "        if hasattr(trainer_stats, 'log_history') and trainer_stats.log_history:\n", "            print(f\"   • Training log entries: {len(trainer_stats.log_history)}\")\n", "            \n", "            # Show loss progression\n", "            losses = [entry.get('train_loss') for entry in trainer_stats.log_history if 'train_loss' in entry]\n", "            if len(losses) >= 2:\n", "                print(f\"   • Loss improvement: {losses[0]:.4f} → {losses[-1]:.4f}\")\n", "                print(f\"   • Loss reduction: {((losses[0] - losses[-1]) / losses[0] * 100):.1f}%\")\n", "        \n", "        print(\"🎯 Model optimally fine-tuned with 2 epochs - great balance!\")\n", "        \n", "        # Clear cache after training\n", "        if torch.cuda.is_available():\n", "            torch.cuda.empty_cache()\n", "            print(\"✓ CUDA cache cleared after training\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Training failed: {e}\")\n", "        print(\"💡 Troubleshooting OPTIMIZED training:\")\n", "        print(\"   1. Check GPU memory availability\")\n", "        print(\"   2. Reduce batch size to 1 if OOM errors occur\")\n", "        print(\"   3. 2 epochs should provide good balance of quality and speed\")\n", "        print(\"   4. Monitor training loss - should decrease steadily\")\n", "        print(\"   5. Ensure adequate disk space for checkpoints\")\n", "        print(\"   6. Check dataset formatting is correct\")\n", "        print(\"   7. Consider reducing gradient_accumulation_steps if memory issues\")\n", "        \n", "        # Additional debugging info\n", "        if torch.cuda.is_available():\n", "            print(f\"\\n🔧 GPU Memory Info:\")\n", "            print(f\"   • Total: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f}GB\")\n", "            print(f\"   • Allocated: {torch.cuda.memory_allocated() / 1e9:.1f}GB\")\n", "            print(f\"   • Cached: {torch.cuda.memory_reserved() / 1e9:.1f}GB\")\n", "        \n", "        trainer = None"]}, {"cell_type": "code", "execution_count": null, "id": "fa96c528-3707-4a58-ab57-e2b223991196", "metadata": {}, "outputs": [], "source": ["# 💾 COMPREHENSIVE MODEL SAVING: Save model in multiple formats with proper organization\n", "import os\n", "import shutil\n", "from pathlib import Path\n", "\n", "print(\"💾 Comprehensive model saving...\")\n", "\n", "if model is None:\n", "    print(\"❌ No model to save - please run training first\")\n", "elif tokenizer is None:\n", "    print(\"❌ No tokenizer to save - please run model loading first\")\n", "else:\n", "    try:\n", "        # Create organized directory structure\n", "        base_dir = \"llama3.2-3b-translation-finetuned\"\n", "        lora_dir = f\"{base_dir}/lora-adapters\"\n", "        merged_dir = f\"{base_dir}/merged-model\"\n", "        gguf_dir = f\"{base_dir}/gguf-model\"\n", "        \n", "        # Create directories\n", "        for directory in [base_dir, lora_dir, merged_dir, gguf_dir]:\n", "            os.makedirs(directory, exist_ok=True)\n", "        \n", "        print(f\"📁 Created directory structure: {base_dir}/\")\n", "        \n", "        # 1. Save LoRA adapters (most important and always works)\n", "        print(\"📝 Saving LoRA adapters...\")\n", "        try:\n", "            model.save_pretrained(lora_dir)\n", "            tokenizer.save_pretrained(lora_dir)\n", "            print(f\"✅ LoRA adapters saved to '{lora_dir}'\")\n", "            \n", "            # Save training configuration\n", "            config_info = {\n", "                \"model_name\": \"meta-llama/Llama-3.2-3B-Instruct\",\n", "                \"max_seq_length\": 2048,\n", "                \"lora_r\": 16,\n", "                \"lora_alpha\": 16,\n", "                \"target_modules\": [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\", \"gate_proj\", \"up_proj\", \"down_proj\"],\n", "                \"training_complete\": True\n", "            }\n", "            \n", "            with open(f\"{lora_dir}/training_config.json\", \"w\") as f:\n", "                json.dump(config_info, f, indent=2)\n", "            \n", "            print(\"✅ Training configuration saved\")\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ LoRA adapter saving failed: {e}\")\n", "            return\n", "        \n", "        # 2. Try to save merged model\n", "        print(\"🔗 Attempting to save merged model...\")\n", "        try:\n", "            if hasattr(model, 'save_pretrained_merged'):\n", "                model.save_pretrained_merged(merged_dir, tokenizer, save_method=\"merged_16bit\")\n", "                print(f\"✅ Merged model saved to '{merged_dir}'\")\n", "            else:\n", "                print(\"ℹ️ Merged model saving not available with this unsloth version\")\n", "                \n", "                # Alternative: try manual merging\n", "                try:\n", "                    from peft import PeftModel\n", "                    base_model = FastLanguageModel.from_pretrained(\n", "                        \"meta-llama/Llama-3.2-3B-Instruct\",\n", "                        max_seq_length=2048,\n", "                        dtype=None,\n", "                        load_in_4bit=False,\n", "                        token=hf_token,\n", "                    )[0]\n", "                    \n", "                    # This is a placeholder - actual merging would need more complex logic\n", "                    print(\"ℹ️ Manual merging not implemented in this version\")\n", "                    \n", "                except Exception as merge_e:\n", "                    print(f\"⚠️ Manual merging failed: {merge_e}\")\n", "                    \n", "        except Exception as e:\n", "            print(f\"⚠️ Merged model save failed: {e}\")\n", "        \n", "        # 3. Try GGUF conversion\n", "        print(\"🔄 Attempting GGUF conversion...\")\n", "        try:\n", "            if hasattr(model, 'save_pretrained_gguf'):\n", "                model.save_pretrained_gguf(gguf_dir, tokenizer, quantization_method=\"q4_k_m\")\n", "                print(f\"✅ GGUF model saved to '{gguf_dir}'\")\n", "            else:\n", "                print(\"ℹ️ GGUF conversion not available with this unsloth version\")\n", "        except Exception as e:\n", "            print(f\"⚠️ GGUF conversion failed: {e}\")\n", "        \n", "        # 4. Save additional metadata\n", "        print(\"📋 Saving metadata...\")\n", "        try:\n", "            metadata = {\n", "                \"model_info\": {\n", "                    \"base_model\": \"meta-llama/Llama-3.2-3B-Instruct\",\n", "                    \"fine_tuned_model\": \"llama3.2-3b-translation-finetuned\",\n", "                    \"training_date\": str(pd.Timestamp.now()),\n", "                    \"framework\": \"unsloth\",\n", "                    \"quantization\": \"4-bit\"\n", "                },\n", "                \"training_data\": {\n", "                    \"train_samples\": len(train_dataset) if train_dataset else 0,\n", "                    \"eval_samples\": len(eval_dataset) if eval_dataset else 0,\n", "                    \"test_samples\": len(test_dataset) if test_dataset else 0\n", "                },\n", "                \"model_parameters\": {\n", "                    \"lora_rank\": 16,\n", "                    \"lora_alpha\": 16,\n", "                    \"target_modules\": [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\", \"gate_proj\", \"up_proj\", \"down_proj\"],\n", "                    \"learning_rate\": 2e-4,\n", "                    \"max_seq_length\": 2048\n", "                }\n", "            }\n", "            \n", "            with open(f\"{base_dir}/model_metadata.json\", \"w\") as f:\n", "                json.dump(metadata, f, indent=2)\n", "            \n", "            print(\"✅ <PERSON><PERSON><PERSON> saved\")\n", "            \n", "        except Exception as e:\n", "            print(f\"⚠️ Metadata saving failed: {e}\")\n", "        \n", "        # 5. Create README\n", "        print(\"📄 Creating README...\")\n", "        try:\n", "            readme_content = f\"\"\"# Llama 3.2-3B Translation Fine-tuned Model\n", "\n", "## Model Information\n", "- **Base Model**: meta-llama/Llama-3.2-3B-Instruct\n", "- **Fine-tuning Method**: <PERSON><PERSON> (Low-Rank Adaptation)\n", "- **Framework**: Unsloth\n", "- **Quantization**: 4-bit\n", "\n", "## Training Configuration\n", "- **LoRA Rank**: 16\n", "- **LoRA Alpha**: 16\n", "- **Target Modules**: q_proj, k_proj, v_proj, o_proj, gate_proj, up_proj, down_proj\n", "- **Learning Rate**: 2e-4\n", "- **Max Sequence Length**: 2048\n", "\n", "## Usage\n", "To use this model, load the LoRA adapters:\n", "\n", "```python\n", "from unsloth import FastLanguageModel\n", "\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name=\"meta-llama/Llama-3.2-3B-Instruct\",\n", "    max_seq_length=2048,\n", "    dtype=None,\n", "    load_in_4bit=True,\n", "    token=\"your_hf_token\"\n", ")\n", "\n", "# Load LoRA adapters\n", "model.load_adapter(\"{lora_dir}\")\n", "```\n", "\n", "## Directory Structure\n", "- `lora-adapters/`: LoRA adapter weights and configuration\n", "- `merged-model/`: Merged model (if available)\n", "- `gguf-model/`: GGUF format model (if available)\n", "- `model_metadata.json`: Training metadata\n", "- `README.md`: This file\n", "\n", "## Training Data\n", "- Training samples: {len(train_dataset) if train_dataset else 'N/A'}\n", "- Evaluation samples: {len(eval_dataset) if eval_dataset else 'N/A'}\n", "- Test samples: {len(test_dataset) if test_dataset else 'N/A'}\n", "\n", "Generated with Unsloth and Llama 3.2-3B Instruct\n", "\"\"\"\n", "            \n", "            with open(f\"{base_dir}/README.md\", \"w\") as f:\n", "                f.write(readme_content)\n", "            \n", "            print(\"✅ README created\")\n", "            \n", "        except Exception as e:\n", "            print(f\"⚠️ README creation failed: {e}\")\n", "        \n", "        # 6. Show final directory structure\n", "        print(f\"\\n📁 Final directory structure:\")\n", "        try:\n", "            for root, dirs, files in os.walk(base_dir):\n", "                level = root.replace(base_dir, '').count(os.sep)\n", "                indent = ' ' * 2 * level\n", "                print(f\"{indent}{os.path.basename(root)}/\")\n", "                sub_indent = ' ' * 2 * (level + 1)\n", "                for file in files[:5]:  # Show first 5 files\n", "                    print(f\"{sub_indent}{file}\")\n", "                if len(files) > 5:\n", "                    print(f\"{sub_indent}... and {len(files) - 5} more files\")\n", "        except Exception as e:\n", "            print(f\"⚠️ Directory listing failed: {e}\")\n", "        \n", "        print(f\"\\n🎉 Model saving complete!\")\n", "        print(f\"✅ Primary model saved as LoRA adapters in '{lora_dir}'\")\n", "        print(f\"📁 Complete package available in '{base_dir}'\")\n", "        print(f\"🔧 Ready for inference and deployment!\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Model saving failed: {e}\")\n", "        print(\"💡 Troubleshooting:\")\n", "        print(\"   1. Check disk space availability\")\n", "        print(\"   2. Verify write permissions\")\n", "        print(\"   3. Try saving to a different directory\")\n", "        print(\"   4. Ensure training completed successfully\")\n", "        print(\"   5. Check if model and tokenizer are valid\")"]}, {"cell_type": "code", "execution_count": null, "id": "04c90280-fdba-4404-9d4a-f0b907500e71", "metadata": {}, "outputs": [], "source": ["# 🧪 COMPREHENSIVE MODEL TESTING: Advanced evaluation with diverse prompts\n", "import time\n", "import random\n", "\n", "print(\"🧪 Comprehensive testing of the fine-tuned Llama 3.2 model...\")\n", "\n", "if model is None or tokenizer is None:\n", "    print(\"❌ No model or tokenizer available - please run previous cells\")\n", "else:\n", "    try:\n", "        # Enable fast inference\n", "        try:\n", "            from unsloth import FastLanguageModel\n", "            FastLanguageModel.for_inference(model)\n", "            print(\"✅ Fast inference enabled\")\n", "        except Exception as e:\n", "            print(f\"ℹ️ Using standard inference: {e}\")\n", "        \n", "        # Enhanced chat template for Llama 3.2\n", "        chat_template = \"\"\"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n", "\n", "You are a helpful assistant specialized in translation and multilingual tasks. Provide accurate, contextual, and culturally appropriate responses.<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\n", "{}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\n", "\"\"\"\n", "        \n", "        # Comprehensive test prompts covering different capabilities\n", "        test_prompts = [\n", "            # Translation tasks\n", "            \"Translate this to Arabic: Hello, how are you today?\",\n", "            \"Translate this to English: مرحبا، كيف حالك؟\",\n", "            \"Translate this to French: Good morning, have a great day!\",\n", "            \n", "            # Multilingual understanding\n", "            \"What is the meaning of 'Bonjour' in English?\",\n", "            \"Explain the difference between 'salaam' and 'marhaba' in Arabic.\",\n", "            \n", "            # General knowledge\n", "            \"What is the capital of France?\",\n", "            \"Explain machine learning in simple terms.\",\n", "            \"What are the benefits of learning multiple languages?\",\n", "            \n", "            # Contextual tasks\n", "            \"How do you say 'thank you' in different languages?\",\n", "            \"Write a short greeting in Arabic and English.\",\n", "            \n", "            # Complex translation\n", "            \"Translate this sentence and explain the cultural context: 'It's raining cats and dogs'\",\n", "        ]\n", "        \n", "        # Test configurations\n", "        test_configs = [\n", "            {\"temperature\": 0.7, \"top_p\": 0.9, \"description\": \"Balanced\"},\n", "            {\"temperature\": 0.3, \"top_p\": 0.8, \"description\": \"Conservative\"},\n", "            {\"temperature\": 1.0, \"top_p\": 0.95, \"description\": \"Creative\"},\n", "        ]\n", "        \n", "        # Select a random test configuration\n", "        config = random.choice(test_configs)\n", "        print(f\"🎯 Using {config['description']} configuration (temp={config['temperature']}, top_p={config['top_p']})\")\n", "        \n", "        successful_tests = 0\n", "        total_tests = len(test_prompts)\n", "        \n", "        print(\"🔍 Running comprehensive inference tests...\")\n", "        print(\"=\" * 60)\n", "        \n", "        for i, prompt in enumerate(test_prompts, 1):\n", "            print(f\"\\n--- Test {i}/{total_tests} ---\")\n", "            print(f\"📝 Prompt: {prompt}\")\n", "            \n", "            try:\n", "                start_time = time.time()\n", "                \n", "                # Format prompt\n", "                formatted_prompt = chat_template.format(prompt)\n", "                \n", "                # Tokenize with proper handling\n", "                inputs = tokenizer(\n", "                    formatted_prompt,\n", "                    return_tensors=\"pt\",\n", "                    truncation=True,\n", "                    max_length=max_seq_length,\n", "                    padding=False\n", "                )\n", "                \n", "                # Move to GPU if available\n", "                if torch.cuda.is_available():\n", "                    inputs = {k: v.cuda() for k, v in inputs.items()}\n", "                \n", "                # Generate response with enhanced parameters\n", "                with torch.no_grad():\n", "                    outputs = model.generate(\n", "                        **inputs,\n", "                        max_new_tokens=150,  # Increased for better responses\n", "                        temperature=config['temperature'],\n", "                        top_p=config['top_p'],\n", "                        do_sample=True,\n", "                        pad_token_id=tokenizer.eos_token_id,\n", "                        repetition_penalty=1.1,\n", "                        length_penalty=1.0,\n", "                        early_stopping=True,\n", "                    )\n", "                \n", "                # Decode and clean response\n", "                full_response = tokenizer.decode(outputs[0], skip_special_tokens=False)\n", "                \n", "                # Extract assistant's response\n", "                if \"<|start_header_id|>assistant<|end_header_id|>\" in full_response:\n", "                    response = full_response.split(\"<|start_header_id|>assistant<|end_header_id|>\")[-1]\n", "                    response = response.split(\"<|eot_id|>\")[0].strip()\n", "                else:\n", "                    response = full_response.strip()\n", "                \n", "                inference_time = time.time() - start_time\n", "                \n", "                # Response quality assessment\n", "                response_length = len(response.split())\n", "                quality_indicators = {\n", "                    \"length_ok\": 5 <= response_length <= 100,\n", "                    \"not_repetitive\": len(set(response.split())) / max(len(response.split()), 1) > 0.7,\n", "                    \"has_content\": len(response.strip()) > 10\n", "                }\n", "                \n", "                quality_score = sum(quality_indicators.values()) / len(quality_indicators)\n", "                \n", "                print(f\"✅ Response ({inference_time:.2f}s, {response_length} words, quality: {quality_score:.1%}):\")\n", "                print(f\"💬 {response}\")\n", "                \n", "                # Show quality details\n", "                if quality_score < 0.8:\n", "                    print(f\"⚠️ Quality issues: {[k for k, v in quality_indicators.items() if not v]}\")\n", "                \n", "                successful_tests += 1\n", "                \n", "            except Exception as e:\n", "                print(f\"❌ Test {i} failed: {e}\")\n", "                print(f\"🔧 Error type: {type(e).__name__}\")\n", "                \n", "                # Memory check for CUDA errors\n", "                if torch.cuda.is_available() and \"CUDA\" in str(e):\n", "                    print(f\"🔧 GPU Memory: {torch.cuda.memory_allocated() / 1e9:.1f}GB / {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f}GB\")\n", "                    torch.cuda.empty_cache()\n", "        \n", "        print(\"=\" * 60)\n", "        print(f\"\\n📊 Testing Results:\")\n", "        print(f\"   • Successful tests: {successful_tests}/{total_tests}\")\n", "        print(f\"   • Success rate: {successful_tests/total_tests:.1%}\")\n", "        print(f\"   • Configuration: {config['description']}\")\n", "        \n", "        if successful_tests == total_tests:\n", "            print(\"🎉 All tests passed! Your model is working perfectly!\")\n", "        elif successful_tests > total_tests * 0.8:\n", "            print(\"✅ Most tests passed! Your model is working well!\")\n", "        elif successful_tests > total_tests * 0.5:\n", "            print(\"⚠️ Some tests failed. Consider additional training or parameter tuning.\")\n", "        else:\n", "            print(\"❌ Many tests failed. Check model configuration and training.\")\n", "        \n", "        # Memory cleanup\n", "        if torch.cuda.is_available():\n", "            torch.cuda.empty_cache()\n", "            print(\"✓ CUDA cache cleared after testing\")\n", "        \n", "        print(\"\\n🎯 Testing complete! Your Llama 3.2-3B model is ready for use!\")\n", "        \n", "        # Usage instructions\n", "        print(\"\\n💡 Next steps:\")\n", "        print(\"   1. The model is now fine-tuned and tested\")\n", "        print(\"   2. LoRA adapters are saved and ready for deployment\")\n", "        print(\"   3. You can load the model using the saved adapters\")\n", "        print(\"   4. Consider further evaluation on your specific use cases\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Testing failed: {e}\")\n", "        print(\"💡 Troubleshooting:\")\n", "        print(\"   1. Ensure model and tokenizer are loaded properly\")\n", "        print(\"   2. Check GPU memory availability\")\n", "        print(\"   3. Try reducing max_new_tokens\")\n", "        print(\"   4. Verify the model was trained successfully\")\n", "        print(\"   5. Check if fast inference is causing issues\")\n", "        \n", "        # Debug info\n", "        if torch.cuda.is_available():\n", "            print(f\"\\n🔧 Debug info:\")\n", "            print(f\"   • Model type: {type(model)}\")\n", "            print(f\"   • Tokenizer type: {type(tokenizer)}\")\n", "            print(f\"   • GPU memory: {torch.cuda.memory_allocated() / 1e9:.1f}GB\")\n", "            torch.cuda.empty_cache()"]}], "metadata": {"kernelspec": {"display_name": "team_14_gpt Environment (GUARANTEED + CONDA + LOCK)", "language": "python", "name": "team_14_gpt_conda"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}