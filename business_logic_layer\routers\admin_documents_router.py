"""
Admin Documents Router
=====================
Handles document management for RAG system - admin only functionality.
"""

import logging
from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException, Depends, status, UploadFile, File, Form
from pydantic import BaseModel

from services.auth_service import AuthService
from services.rag_service import RAGService

# Initialize services
auth_service = AuthService()
rag_service = RAGService()

router = APIRouter(prefix="/admin/documents", tags=["admin-documents"])

# Models
class DocumentUploadResponse(BaseModel):
    success: bool
    message: str
    document_id: str = None

class DocumentListResponse(BaseModel):
    success: bool
    documents: List[Dict[str, Any]]
    total_count: int

class VectorStoreStatsResponse(BaseModel):
    success: bool
    stats: Dict[str, Any]

@router.post("/upload", response_model=DocumentUploadResponse)
async def upload_document(
    file: UploadFile = File(...),
    title: str = Form(...),
    description: str = Form(default=""),
    category: str = Form(default="general"),
    current_user: Dict[str, Any] = Depends(auth_service.get_current_admin_user)
):
    """Upload and process a document for the RAG system"""
    try:
        # Validate file type
        allowed_types = ['text/plain', 'application/pdf', 'text/markdown', 'application/msword']
        if file.content_type not in allowed_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported file type: {file.content_type}"
            )
        
        # Read file content
        content = await file.read()
        
        # Handle different file types
        if file.content_type == 'text/plain' or file.content_type == 'text/markdown':
            text_content = content.decode('utf-8')
        elif file.content_type == 'application/pdf':
            # For PDF files, you might want to use PyPDF2 or similar
            # For now, we'll assume text content
            text_content = content.decode('utf-8', errors='ignore')
        else:
            text_content = content.decode('utf-8', errors='ignore')
        
        # Prepare metadata
        metadata = {
            'title': title,
            'description': description,
            'category': category,
            'filename': file.filename,
            'content_type': file.content_type,
            'uploaded_by': current_user['username'],
            'file_size': len(content)
        }
        
        # Add document to RAG system
        success = rag_service.add_document(
            content=text_content,
            source=f"{category}/{file.filename}",
            metadata=metadata
        )
        
        if success:
            logging.info(f"📄 Document uploaded successfully: {file.filename}")
            return DocumentUploadResponse(
                success=True,
                message=f"Document '{title}' uploaded and processed successfully",
                document_id=f"{category}/{file.filename}"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to process document"
            )
            
    except Exception as e:
        logging.error(f"❌ Error uploading document: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error uploading document: {str(e)}"
        )

@router.post("/upload-text", response_model=DocumentUploadResponse)
async def upload_text_document(
    title: str,
    content: str,
    description: str = "",
    category: str = "general",
    current_user: Dict[str, Any] = Depends(auth_service.get_current_admin_user)
):
    """Upload text content directly as a document"""
    try:
        # Prepare metadata
        metadata = {
            'title': title,
            'description': description,
            'category': category,
            'content_type': 'text/plain',
            'uploaded_by': current_user['username'],
            'file_size': len(content)
        }
        
        # Add document to RAG system
        success = rag_service.add_document(
            content=content,
            source=f"{category}/{title}",
            metadata=metadata
        )
        
        if success:
            logging.info(f"📝 Text document uploaded successfully: {title}")
            return DocumentUploadResponse(
                success=True,
                message=f"Text document '{title}' uploaded and processed successfully",
                document_id=f"{category}/{title}"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to process text document"
            )
            
    except Exception as e:
        logging.error(f"❌ Error uploading text document: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error uploading text document: {str(e)}"
        )

@router.get("/list", response_model=DocumentListResponse)
async def list_documents(
    current_user: Dict[str, Any] = Depends(auth_service.get_current_admin_user)
):
    """List all documents in the RAG system"""
    try:
        # Get unique documents from chunks
        documents = {}
        for chunk in rag_service.vector_store['chunks']:
            source = chunk.source
            if source not in documents:
                documents[source] = {
                    'source': source,
                    'title': chunk.metadata.get('title', source),
                    'description': chunk.metadata.get('description', ''),
                    'category': chunk.metadata.get('category', 'general'),
                    'uploaded_by': chunk.metadata.get('uploaded_by', 'unknown'),
                    'file_size': chunk.metadata.get('file_size', 0),
                    'content_type': chunk.metadata.get('content_type', 'text/plain'),
                    'created_at': chunk.created_at,
                    'chunk_count': 0
                }
            documents[source]['chunk_count'] += 1
        
        document_list = list(documents.values())
        
        return DocumentListResponse(
            success=True,
            documents=document_list,
            total_count=len(document_list)
        )
        
    except Exception as e:
        logging.error(f"❌ Error listing documents: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error listing documents: {str(e)}"
        )

@router.delete("/delete/{document_source:path}")
async def delete_document(
    document_source: str,
    current_user: Dict[str, Any] = Depends(auth_service.get_current_admin_user)
):
    """Delete a document from the RAG system"""
    try:
        success = rag_service.delete_document(document_source)
        
        if success:
            logging.info(f"🗑️ Document deleted successfully: {document_source}")
            return {
                "success": True,
                "message": f"Document '{document_source}' deleted successfully"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Document not found: {document_source}"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"❌ Error deleting document: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting document: {str(e)}"
        )

@router.get("/stats", response_model=VectorStoreStatsResponse)
async def get_vector_store_stats(
    current_user: Dict[str, Any] = Depends(auth_service.get_current_admin_user)
):
    """Get statistics about the vector store"""
    try:
        stats = rag_service.get_vector_store_stats()
        
        return VectorStoreStatsResponse(
            success=True,
            stats=stats
        )
        
    except Exception as e:
        logging.error(f"❌ Error getting vector store stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting vector store stats: {str(e)}"
        )

@router.post("/search")
async def search_documents(
    query: str,
    top_k: int = 5,
    current_user: Dict[str, Any] = Depends(auth_service.get_current_admin_user)
):
    """Search documents in the RAG system"""
    try:
        results = rag_service.search_similar_chunks(query, top_k)
        
        return {
            "success": True,
            "query": query,
            "results": results,
            "total_found": len(results)
        }
        
    except Exception as e:
        logging.error(f"❌ Error searching documents: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error searching documents: {str(e)}"
        )
