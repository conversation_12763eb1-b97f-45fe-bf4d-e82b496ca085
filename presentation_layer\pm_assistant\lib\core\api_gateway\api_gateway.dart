import 'dart:convert';
import 'package:http/http.dart' as http;

/// API Gateway for Communication with Business Logic Layer
/// This is the only component that communicates with the backend
/// Following the architecture: Presentation Layer -> API Gateway -> Business Logic Layer
class ApiGateway {
  static const String _baseUrl = 'http://54.38.33.180:8000';
  String? _authToken;

  /// Default headers for requests
  Map<String, String> get _defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  /// Headers with authentication token
  Map<String, String> get _authHeaders => {
    ..._defaultHeaders,
    if (_authToken != null) 'Authorization': 'Bearer $_authToken',
  };

  /// Set authentication token
  void setAuthToken(String? token) {
    _authToken = token;
  }

  // ============================================================================
  // AUTHENTICATION ENDPOINTS
  // ============================================================================

  /// Register new user
  Future<Map<String, dynamic>> register({
    required String email,
    required String password,
    required String fullName,
    required String role,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/auth/register'),
        headers: _defaultHeaders,
        body: jsonEncode({
          'email': email,
          'password': password,
          'full_name': fullName,
          'role': role,
        }),
      ).timeout(const Duration(seconds: 10));

      final responseData = jsonDecode(response.body);
      
      if (response.statusCode == 200) {
        // Backend returns TokenResponse with access_token, token_type, and user
        return {
          'success': true,
          'access_token': responseData['access_token'],
          'token_type': responseData['token_type'],
          'user': responseData['user'],
        };
      } else {
        return {
          'success': false,
          'error': responseData['detail'] ?? 'Registration failed',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: ${e.toString()}',
      };
    }
  }

  /// Login user
  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    try {
      print('🔐 Attempting login to: $_baseUrl/auth/login');
      print('📧 Email: $email');

      final response = await http.post(
        Uri.parse('$_baseUrl/auth/login'),
        headers: _defaultHeaders,
        body: jsonEncode({
          'email': email,
          'password': password,
        }),
      ).timeout(const Duration(seconds: 10));

      print('📱 Response status: ${response.statusCode}');
      print('📱 Response body: ${response.body}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        print('✅ Login successful');
        // Backend returns TokenResponse with access_token, token_type, and user
        return {
          'success': true,
          'access_token': responseData['access_token'],
          'token_type': responseData['token_type'],
          'user': responseData['user'],
        };
      } else {
        print('❌ Login failed: ${responseData['detail']}');
        return {
          'success': false,
          'error': responseData['detail'] ?? 'Login failed',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: ${e.toString()}',
      };
    }
  }

  /// Get current user
  Future<Map<String, dynamic>> getCurrentUser() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/auth/me'),
        headers: _authHeaders,
      ).timeout(const Duration(seconds: 10));

      final responseData = jsonDecode(response.body);
      
      if (response.statusCode == 200) {
        return {
          'success': true,
          'user': responseData,
        };
      } else {
        return {
          'success': false,
          'error': responseData['detail'] ?? 'Failed to get user info',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: ${e.toString()}',
      };
    }
  }

  /// Logout user
  Future<Map<String, dynamic>> logout() async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/auth/logout'),
        headers: _authHeaders,
      ).timeout(const Duration(seconds: 10));

      _authToken = null; // Clear token on logout
      return jsonDecode(response.body);
    } catch (e) {
      _authToken = null; // Clear token even on error
      return {
        'success': false,
        'error': 'Network error: ${e.toString()}',
      };
    }
  }

  // ============================================================================
  // CHAT ENDPOINTS
  // ============================================================================

  /// Send message
  Future<Map<String, dynamic>> sendMessage({
    required String sessionId,
    required String message,
  }) async {
    try {
      print('💬 Sending to: $_baseUrl/chat/send');
      print('🔑 Session ID: $sessionId');
      print('📝 Message: $message');
      print('🔐 Auth headers: $_authHeaders');

      final response = await http.post(
        Uri.parse('$_baseUrl/chat/send'),
        headers: _authHeaders,
        body: jsonEncode({
          'session_id': sessionId,
          'message': message,
        }),
      ).timeout(const Duration(seconds: 60)); // Increased timeout for AI model responses

      print('📱 Chat response status: ${response.statusCode}');
      print('📱 Chat response body: ${response.body}');

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        print('✅ Chat message sent successfully');
        return responseData;
      } else {
        print('❌ Chat message failed: ${responseData['detail'] ?? responseData['error']}');
        return {
          'success': false,
          'error': responseData['detail'] ?? responseData['error'] ?? 'Failed to send message',
        };
      }
    } catch (e) {
      print('❌ Chat network error: ${e.toString()}');
      return {
        'success': false,
        'error': 'Network error: ${e.toString()}',
      };
    }
  }

  /// Stream message response token by token
  Stream<Map<String, dynamic>> streamMessage({
    required String sessionId,
    required String message,
  }) async* {
    try {
      print('🚀 Starting stream for message: ${message.substring(0, message.length > 50 ? 50 : message.length)}...');

      final request = http.Request(
        'POST',
        Uri.parse('$_baseUrl/chat/stream'),
      );

      request.headers.addAll(_authHeaders);
      request.body = jsonEncode({
        'session_id': sessionId,
        'message': message,
      });

      final streamedResponse = await request.send();

      if (streamedResponse.statusCode == 200) {
        print('✅ Stream connection established');

        String buffer = '';
        await for (final chunk in streamedResponse.stream.transform(utf8.decoder)) {
          buffer += chunk;
          final lines = buffer.split('\n');

          // Process complete lines, keep incomplete line in buffer
          for (int i = 0; i < lines.length - 1; i++) {
            final line = lines[i].trim();
            if (line.startsWith('data: ')) {
              final jsonStr = line.substring(6).trim();
              if (jsonStr.isNotEmpty && jsonStr != '') {
                try {
                  final data = jsonDecode(jsonStr);
                  print('📦 Received chunk: ${data['type'] ?? 'unknown'} - ${data['chunk']?.length ?? 0} chars');
                  yield data;
                } catch (e) {
                  print('⚠️ Failed to parse JSON: $jsonStr');
                }
              }
            }
          }

          // Keep the last incomplete line in buffer
          buffer = lines.last;
        }

        print('🏁 Stream completed');
      } else {
        print('❌ Stream failed with status: ${streamedResponse.statusCode}');
        yield {
          'error': 'Failed to stream response',
          'status_code': streamedResponse.statusCode,
          'type': 'error',
        };
      }
    } catch (e) {
      print('❌ Stream error: ${e.toString()}');
      yield {
        'error': 'Network error: ${e.toString()}',
        'type': 'error',
      };
    }
  }

  /// Get conversation history
  Future<Map<String, dynamic>> getConversationHistory(String conversationId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/chat/conversation/$conversationId'),
        headers: _authHeaders,
      ).timeout(const Duration(seconds: 10));

      return jsonDecode(response.body);
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: ${e.toString()}',
      };
    }
  }

  /// Create new conversation
  Future<Map<String, dynamic>> createConversation({
    required String title,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/chat/conversation'),
        headers: _authHeaders,
        body: jsonEncode({
          'title': title,
        }),
      ).timeout(const Duration(seconds: 10));

      return jsonDecode(response.body);
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: ${e.toString()}',
      };
    }
  }

  /// Get all conversations for user
  Future<Map<String, dynamic>> getConversations() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/chat/conversations'),
        headers: _authHeaders,
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        // Backend returns List<ConversationSummary>, wrap it in success response
        return {
          'success': true,
          'conversations': responseData, // responseData is a List
        };
      } else {
        final errorData = jsonDecode(response.body);
        return {
          'success': false,
          'error': errorData['detail'] ?? 'Failed to load conversations',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: ${e.toString()}',
      };
    }
  }

  /// Delete conversation
  Future<Map<String, dynamic>> deleteConversation(String conversationId) async {
    try {
      final response = await http.delete(
        Uri.parse('$_baseUrl/chat/conversation/$conversationId'),
        headers: _authHeaders,
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        return {
          'success': responseData['success'] ?? true,
          'message': responseData['message'] ?? 'Conversation deleted successfully',
        };
      } else {
        final errorData = jsonDecode(response.body);
        return {
          'success': false,
          'error': errorData['detail'] ?? 'Failed to delete conversation',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: ${e.toString()}',
      };
    }
  }

  /// Update conversation title
  Future<Map<String, dynamic>> updateConversationTitle({
    required String conversationId,
    required String title,
  }) async {
    try {
      final response = await http.put(
        Uri.parse('$_baseUrl/chat/conversation/$conversationId'),
        headers: _authHeaders,
        body: jsonEncode({
          'title': title,
        }),
      ).timeout(const Duration(seconds: 10));

      return jsonDecode(response.body);
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: ${e.toString()}',
      };
    }
  }

  // ============================================================================
  // ADMIN ENDPOINTS
  // ============================================================================

  /// Get admin statistics
  Future<Map<String, dynamic>> getAdminStats() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/admin/stats'),
        headers: _authHeaders,
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        final errorData = jsonDecode(response.body);
        return {
          'success': false,
          'error': errorData['detail'] ?? 'Failed to get admin stats',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: ${e.toString()}',
      };
    }
  }

  /// Get system health
  Future<Map<String, dynamic>> getSystemHealth() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/admin/system-health'),
        headers: _authHeaders,
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final healthData = jsonDecode(response.body);
        return {
          'success': true,
          'health': healthData,
        };
      } else {
        final errorData = jsonDecode(response.body);
        return {
          'success': false,
          'error': errorData['detail'] ?? 'Failed to get system health',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: ${e.toString()}',
      };
    }
  }

  /// Get all users (admin only)
  Future<Map<String, dynamic>> getAllUsers() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/admin/users'),
        headers: _authHeaders,
      ).timeout(const Duration(seconds: 10));

      return jsonDecode(response.body);
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: ${e.toString()}',
      };
    }
  }

  /// Get analytics data
  Future<Map<String, dynamic>> getAnalytics() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/admin/analytics'),
        headers: _authHeaders,
      ).timeout(const Duration(seconds: 10));

      return jsonDecode(response.body);
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: ${e.toString()}',
      };
    }
  }

  // ============================================================================
  // HEALTH CHECK
  // ============================================================================

  /// Health check
  Future<Map<String, dynamic>> healthCheck() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/health'),
        headers: _defaultHeaders,
      ).timeout(const Duration(seconds: 5));

      return {
        'success': response.statusCode == 200,
        'status': response.statusCode,
        'data': jsonDecode(response.body),
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: ${e.toString()}',
      };
    }
  }

  // Document Management Methods (Admin Only)

  /// Upload document for RAG system
  Future<Map<String, dynamic>> uploadDocument({
    required String filePath,
    required String title,
    String description = '',
    String category = 'general',
  }) async {
    try {
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$_baseUrl/admin/documents/upload'),
      );

      request.headers.addAll(_authHeaders);
      request.fields['title'] = title;
      request.fields['description'] = description;
      request.fields['category'] = category;

      request.files.add(await http.MultipartFile.fromPath('file', filePath));

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      return jsonDecode(response.body);
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: ${e.toString()}',
      };
    }
  }

  /// Upload text document for RAG system
  Future<Map<String, dynamic>> uploadTextDocument({
    required String title,
    required String content,
    String description = '',
    String category = 'general',
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/admin/documents/upload-text'),
        headers: _authHeaders,
        body: jsonEncode({
          'title': title,
          'content': content,
          'description': description,
          'category': category,
        }),
      ).timeout(const Duration(seconds: 30));

      return jsonDecode(response.body);
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: ${e.toString()}',
      };
    }
  }

  /// Get list of documents in RAG system
  Future<Map<String, dynamic>> getDocuments() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/admin/documents/list'),
        headers: _authHeaders,
      ).timeout(const Duration(seconds: 15));

      return jsonDecode(response.body);
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: ${e.toString()}',
      };
    }
  }

  /// Delete document from RAG system
  Future<Map<String, dynamic>> deleteDocument(String documentSource) async {
    try {
      final response = await http.delete(
        Uri.parse('$_baseUrl/admin/documents/delete/$documentSource'),
        headers: _authHeaders,
      ).timeout(const Duration(seconds: 15));

      return jsonDecode(response.body);
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: ${e.toString()}',
      };
    }
  }

  /// Get vector store statistics
  Future<Map<String, dynamic>> getVectorStoreStats() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/admin/documents/stats'),
        headers: _authHeaders,
      ).timeout(const Duration(seconds: 15));

      return jsonDecode(response.body);
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: ${e.toString()}',
      };
    }
  }

  /// Search documents in RAG system
  Future<Map<String, dynamic>> searchDocuments({
    required String query,
    int topK = 5,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/admin/documents/search'),
        headers: _authHeaders,
        body: jsonEncode({
          'query': query,
          'top_k': topK,
        }),
      ).timeout(const Duration(seconds: 30));

      return jsonDecode(response.body);
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: ${e.toString()}',
      };
    }
  }
}
