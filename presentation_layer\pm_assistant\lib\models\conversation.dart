import 'message.dart';

/// Simple Conversation model for frontend
/// Follows Single Responsibility Principle - handles only conversation data
class Conversation {
  final String id;
  final String title;
  final List<Message> messages;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? userId;
  final bool isArchived;
  final Map<String, dynamic> metadata;
  final String? lastMessage; // Backend's last_message field
  final int? messageCount; // Backend's message_count field

  const Conversation({
    required this.id,
    required this.title,
    required this.messages,
    required this.createdAt,
    required this.updatedAt,
    this.userId,
    this.isArchived = false,
    this.metadata = const {},
    this.lastMessage,
    this.messageCount,
  });

  /// Factory constructor for creating new conversation
  factory Conversation.create({
    required String title,
    String? userId,
    List<Message>? initialMessages,
    String? id,
  }) {
    final now = DateTime.now();
    final messages = initialMessages ?? [];
    return Conversation(
      id: id ?? now.millisecondsSinceEpoch.toString(),
      title: title,
      messages: messages,
      createdAt: now,
      updatedAt: now,
      userId: userId,
      lastMessage: messages.isNotEmpty ? messages.last.text : null,
      messageCount: messages.length,
    );
  }

  /// Factory constructor for creating from JSON
  factory Conversation.fromJson(Map<String, dynamic> json) {
    return Conversation(
      id: json['id'] ?? '',
      title: json['title'] ?? 'Untitled Conversation',
      messages: (json['messages'] as List<dynamic>?)
          ?.map((msgJson) => Message.fromJson(msgJson))
          .toList() ?? [],
      createdAt: DateTime.tryParse(json['created_at'] ?? '') ?? DateTime.now(),
      // Map backend 'last_activity' to frontend 'updated_at'
      updatedAt: DateTime.tryParse(json['updated_at'] ?? json['last_activity'] ?? '') ?? DateTime.now(),
      userId: json['user_id'],
      isArchived: json['is_archived'] ?? false,
      metadata: json['metadata'] ?? {},
      lastMessage: json['last_message'], // Backend's last_message field
      messageCount: json['message_count'], // Backend's message_count field
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'messages': messages.map((msg) => msg.toJson()).toList(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'user_id': userId,
      'is_archived': isArchived,
      'metadata': metadata,
    };
  }

  /// Get the last message in the conversation
  Message? get lastMessageObject => messages.isNotEmpty ? messages.last : null;

  /// Get the number of messages (prefer backend messageCount if available)
  int get messageLength => messageCount ?? messages.length;

  /// Check if conversation is empty
  bool get isEmpty => messages.isEmpty;

  /// Get conversation duration
  Duration? get duration {
    if (messages.isEmpty) return null;
    return messages.last.timestamp.difference(messages.first.timestamp);
  }

  /// Add a message to the conversation
  Conversation addMessage(Message message) {
    final updatedMessages = List<Message>.from(messages)..add(message);
    return copyWith(
      messages: updatedMessages,
      updatedAt: DateTime.now(),
    );
  }

  /// Remove a message from the conversation
  Conversation removeMessage(String messageId) {
    final updatedMessages = messages.where((msg) => msg.id != messageId).toList();
    return copyWith(
      messages: updatedMessages,
      updatedAt: DateTime.now(),
    );
  }

  /// Clear all messages
  Conversation clearMessages() {
    return copyWith(
      messages: [],
      updatedAt: DateTime.now(),
    );
  }

  /// Archive the conversation
  Conversation archive() {
    return copyWith(
      isArchived: true,
      updatedAt: DateTime.now(),
    );
  }

  /// Create a copy with updated values
  Conversation copyWith({
    String? id,
    String? title,
    List<Message>? messages,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? userId,
    bool? isArchived,
    Map<String, dynamic>? metadata,
    String? lastMessage,
    int? messageCount,
  }) {
    final updatedMessages = messages ?? this.messages;
    return Conversation(
      id: id ?? this.id,
      title: title ?? this.title,
      messages: updatedMessages,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      userId: userId ?? this.userId,
      isArchived: isArchived ?? this.isArchived,
      metadata: metadata ?? this.metadata,
      lastMessage: lastMessage ?? (updatedMessages.isNotEmpty ? updatedMessages.last.text : this.lastMessage),
      messageCount: messageCount ?? updatedMessages.length,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Conversation && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Conversation(id: $id, title: $title, messageCount: $messageCount)';
  }
}
