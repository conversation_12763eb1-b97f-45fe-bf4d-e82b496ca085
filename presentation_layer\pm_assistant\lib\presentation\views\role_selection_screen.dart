import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../viewmodels/auth_viewmodel.dart';
import '../widgets/common/yonnovia_card.dart';
import '../widgets/auth/role_card.dart';
import '../../core/constants/app_colors.dart';
import 'regular_dashboard_screen.dart';
import 'admin_dashboard_screen.dart';

class RoleSelectionScreen extends StatelessWidget {
  const RoleSelectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final user = context.watch<AuthViewModel>().user;
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Container(
                constraints: const BoxConstraints(maxWidth: 450),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Welcome Section
                    YonnoviaCard(
                        padding: const EdgeInsets.all(40),
                        child: Container(
                          decoration: const BoxDecoration(
                            gradient: AppColors.primaryGradient,
                            borderRadius: BorderRadius.all(Radius.circular(12)),
                          ),
                          padding: const EdgeInsets.all(24),
                          child: Column(
                            children: [
                              // Assistant Illustration
                              Container(
                                padding: const EdgeInsets.all(20),
                                decoration: BoxDecoration(
                                  color: AppColors.primaryTeal
                                      .withAlpha((0.15 * 255).toInt()),
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: AppColors.primaryTeal
                                        .withAlpha((0.3 * 255).toInt()),
                                    width: 1,
                                  ),
                                ),
                                child: Image.asset(
                                  'assets/images/assistant.png',
                                  width: 80,
                                  height: 80,
                                ),
                              ),
                              const SizedBox(height: 24),
                              // App Logo
                              Image.asset(
                                'assets/images/logo_dark_mode.png',
                                width: 280,
                                height: 75,
                              ),
                              const SizedBox(height: 20),
                              const Center(
                                child: Text(
                                  'PM Assistant',
                                  style: TextStyle(
                                    fontSize: 28,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.white,
                                    letterSpacing: 0.5,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              const SizedBox(height: 12),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 8),
                                decoration: BoxDecoration(
                                  gradient: const LinearGradient(
                                    colors: [
                                      AppColors.primaryTeal,
                                      AppColors.secondaryGreen
                                    ],
                                  ),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: const Text(
                                  'Choose Your Role',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: AppColors.darkGray,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 12),
                              Text(
                                'Select your role to continue with the appropriate dashboard and features',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: AppColors.white
                                      .withAlpha((0.9 * 255).toInt()),
                                  fontWeight: FontWeight.w500,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        )),
                    const SizedBox(height: 48),
                    // Role Selection Cards
                    YonnoviaCard(
                      padding: const EdgeInsets.all(36),
                      elevated: true,
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: AppColors.primaryTeal
                                      .withAlpha((0.1 * 255).toInt()),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: const Icon(
                                  Icons.person_outline,
                                  color: AppColors.primaryTeal,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 12),
                              const Text(
                                'Select Role',
                                style: TextStyle(
                                  fontSize: 26,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Choose the role that best fits your needs',
                            style: TextStyle(
                              fontSize: 15,
                              color: AppColors.textSecondary,
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 36),
                          // Regular User Card
                          RoleCard(
                            title: 'Regular User',
                            description:
                                'Access project management tools and chat with the AI assistant',
                            icon: Icons.person,
                            features: [
                              'Chat with PM Assistant',
                              'View conversation history',
                              'Access project insights',
                              'Collaborate with team'
                            ],
                            assetPath: 'assets/images/mobile.png',
                            onTap: () {
                              Navigator.pushAndRemoveUntil(
                                context,
                                MaterialPageRoute(
                                  builder: (_) =>
                                      const RegularDashboardScreen(),
                                ),
                                (route) => false,
                              );
                            },
                          ),
                          const SizedBox(height: 24),
                          // Admin User Card
                          RoleCard(
                            title: 'Admin User',
                            description:
                                'Full access to admin features, user management, and analytics',
                            icon: Icons.admin_panel_settings,
                            features: [
                              'All regular user features',
                              'User management',
                              'System analytics',
                              'Knowledge base management'
                            ],
                            assetPath: 'assets/images/settings.png',
                            isAdmin: true,
                            isEnabled: user?.isAdmin ?? false,
                            onTap: (user?.isAdmin ?? false)
                                ? () {
                                    Navigator.pushAndRemoveUntil(
                                      context,
                                      MaterialPageRoute(
                                        builder: (_) =>
                                            const AdminDashboardScreen(),
                                      ),
                                      (route) => false,
                                    );
                                  }
                                : null,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
