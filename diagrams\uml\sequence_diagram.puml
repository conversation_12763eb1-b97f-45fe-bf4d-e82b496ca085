@startuml ProjectManagementChatbot_SequenceDiagram

!theme plain
skinparam participant {
    BackgroundColor #E6F3FF
    BorderColor #0066CC
}
skinparam sequence {
    ArrowColor #0066CC
    LifeLineBackgroundColor #F0F8FF
    LifeLineBorderColor #0066CC
}

title Project Management Chatbot - Main Interaction Flow

actor "User" as User
participant "Web Interface" as UI
participant "Chatbot Controller" as Controller
participant "NLP Processor" as NLP
participant "Knowledge Base" as KB
participant "Response Generator" as ResponseGen

== User Interaction ==
User -> UI: Ask project management question
activate UI

UI -> Controller: Process user query
activate Controller

Controller -> NLP: Analyze text and extract intent
activate NLP
NLP --> Controller: Intent and entities
deactivate NLP

Controller -> KB: Search for relevant answers
activate KB
KB --> Controller: Matching Q&A pairs
deactivate KB

Controller -> ResponseGen: Generate response
activate ResponseGen
ResponseGen --> Controller: Formatted answer
deactivate ResponseGen

Controller --> UI: Return response
deactivate Controller

UI --> User: Display answer and suggestions
deactivate UI

== Follow-up Question ==
User -> UI: Ask follow-up question
activate UI

UI -> Controller: Process with context
activate Controller

note over Controller: Uses previous conversation\ncontext for better answers

Controller -> KB: Search with context
activate KB
KB --> Controller: Contextual answers
deactivate KB

Controller -> ResponseGen: Generate contextual response
activate ResponseGen
ResponseGen --> Controller: Enhanced answer
deactivate ResponseGen

Controller --> UI: Return contextual response
deactivate Controller

UI --> User: Display related answer
deactivate UI

note over User, ResponseGen: Simple interaction flow:\nAsk → Process → Search → Respond

@enduml
